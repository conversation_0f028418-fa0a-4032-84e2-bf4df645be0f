<?php
/**
 * File: public.tpl.inc.php
 *
 *
 */


?><!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="robots" content="all">

<?php

//////////////////////////////////////////////
// GET FILETIME
// Sets variable for query string based on last-modified time on file,
// so it will always cause refresh of file if there is a newer version.
function get_that_filetime($file_url = false) {
    if($file_url && file_exists($file_url)) {
        return filemtime($file_url);
    }
}
//////////////////////////////////////////////


// BUILD META DATA AND OTHER IMPORTANT BITS

// CLIENT URL
$client_url = 'https://';
$client_url .= str_replace('http://','',$this->End_User_URL);

$canonical_url = $client_url . $this->db_page_row['page_group_item_name'] . $this->db_page_row['item_name'];
switch($this->phantom_row['lid']) {
    case 1421875517: // C_news
        if($this->phantom_row['category']=='articles') {
            $canonical_url = $client_url . '/articles/' . $this->phantom_row['slug'];
        } else {
            $canonical_url = $client_url . '/news/' . $this->phantom_row['slug'];
        }
        break;
    case 1689198468: // C_documentation
        $canonical_url = $client_url . '/documentation/' . $this->phantom_row['slug'];
        break;
}

// KEWORDS
$meta_keywords = '';
if ($this->db_page_row['meta_keywords'] != '') {
    $meta_keywords = htmlspecialchars( $this->db_page_row['meta_keywords'], ENT_COMPAT );
}
else {
    $meta_keywords = htmlspecialchars( $this->meta_keywords, ENT_COMPAT );
}

// META DESCRIPTION
$meta_description = '';
if ($this->db_page_row['meta_description'] != '') {
    $meta_description = htmlspecialchars( $this->db_page_row['meta_description'], ENT_COMPAT );
}
else {
    $meta_description = htmlspecialchars( $this->meta_description, ENT_COMPAT );
}

// TITLE
$title = '';
if ($this->db_page_row['head_title'] != '') {
    $title = $this->db_page_row['head_title'];
}
else {
    $title = $this->db_page_row['menu_display'];
}
if ($this->html_filename !== 'home'){
    $title = $title . ' - ' . htmlspecialchars( $this->End_User_Name, ENT_COMPAT );
}

// SITE NAME
$site_name = htmlspecialchars($this->End_User_Name, ENT_COMPAT);

// META IMAGES
$site_url = ($this->SSL_enabled == 1 ? 'https://' : 'http://') . $this->End_User_URL;
$page_image = '';
$og_image = '';
$twitter_image = '';
if (($this->db_page_row['item_name'] == 'news-detail') || ($this->db_page_row['item_name'] == 'articles-detail')) {
    if ($this->phantom_row['attachment_filename']) {
        $page_image = '/images/news/' . $this->phantom_row['attachment_filename'];
    }
} elseif ($this->db_page_row['attachment_filename']) {
    $page_image = '/images/' . $this->db_page_row['attachment_filename'];
} elseif ($this->image_social_1) {
    $page_image = $this->image_social_1;
}
if ($page_image) {
    $og_image = '<meta property="og:image" content="' . $site_url . $page_image . '">';
    $twitter_image = '<meta name="twitter:image" content="' . $site_url . $page_image . '">';
    // $schema_image = '"image" : "' . $site_url . $page_image . '",';
}

// favicon and touch icons
if ($this->favicon_front)
{echo '<link rel="shortcut icon" href="'. htmlspecialchars($this->favicon_front, ENT_COMPAT) .'">' . "\n"; }
//if ($this->icon_180)
//{echo '<link rel="apple-touch-icon" sizes="180x180" href="' . htmlspecialchars($this->icon_180, ENT_COMPAT) . '">' . "\n";}
//if ($this->icon_32)
//{echo '<link rel="icon" type="image/png" sizes="32x32" href="' . htmlspecialchars($this->icon_32, ENT_COMPAT) . '">' . "\n";}
//if ($this->icon_16)
//{echo '<link rel="icon" type="image/png" sizes="16x16" href="' . htmlspecialchars($this->icon_16, ENT_COMPAT) . '">' . "\n";}
//if ($this->icon_192 AND $this->icon_512)
//{echo '<link rel="manifest" href="/manifest.json">' . "\n";}
//if ($this->theme_color_android)
//{echo '<meta name="theme-color" content="' . htmlspecialchars($this->theme_color_android, ENT_COMPAT) . '">' . "\n";}
//if ($this->icon_svg)
//{echo '<link rel="mask-icon" href="' . htmlspecialchars($this->icon_svg, ENT_COMPAT) . '" color="' . htmlspecialchars($this->icon_color_safari_pinned, ENT_COMPAT) . '">' . "\n";}
?>

<meta name="keywords" content="<?php echo $meta_keywords; ?>">
<meta name="description" content="<?php echo $meta_description; ?>">
<meta property="og:description" content="<?php echo $meta_description; ?>">
<title><?php echo $title; ?></title>
<meta property="og:title" content="<?php echo $title; ?>">
<meta property="og:site_name" content="<?php echo $site_name; ?>">
<?php echo $og_image; ?>
<meta name="twitter:card" content="summary_large_image">
<?php echo $twitter_image; ?>
<link rel="shortcut icon" href="' . $client_url . $this->favicon_front . '">

<link rel="canonical" href="<?php echo $canonical_url; ?>">
<script src="https://use.typekit.net/ekd0bjc.js"></script>
<script>try{Typekit.load({ async: true });}catch(e){}</script>
<link href="/assets_2025/css/fontawesome/css/fontawesome.css" rel="stylesheet" />
<link href="/assets_2025/css/fontawesome/css/brands.css" rel="stylesheet" />
<link href="/assets_2025/css/fontawesome/css/solid.css" rel="stylesheet" />
<link href="/assets_2025/css/public.css" rel="stylesheet">

<script src="/assets_2025/js/vendor/jquery-3.7.1.min.js"></script>
<script type="text/javascript">

$(document).ready(function () {
    $("input[name=frm_password]").keyup(function(){
        $("input[name=visible_password]").val($("input[name=frm_password]").val());
    });
    $("input[name=frm_password]").change(function(){
        $("input[name=visible_password]").val($("input[name=frm_password]").val());
    });

    $("input[name=visible_password]").keyup(function(){
        $("input[name=frm_password]").val($("input[name=visible_password]").val());
    });
    $("input[name=visible_password]").change(function(){
        $("input[name=frm_password]").val($("input[name=visible_password]").val());
    });

    $('a.hider').click(function () {
        $('div.hider').slideToggle("slow");
    });

    <?php if ($_SESSION['login_message']) {
    echo 'alert("'. $_SESSION['login_message'] .'");';
    unset($_SESSION['login_message']);
}
    ?>

});
</script>

<?php
echo $this->db_page_row['insert_head'] . "\n";
?>

</head>

<body class="<?php if ($this->db_page_row['css_class'] != '') {echo ($this->db_page_row['css_class']);} ?>">

<?php
// EMBEDDED IMAGES
if ($this->image_social_1)
    {echo '<img src="//' . htmlspecialchars($this->End_User_URL, ENT_COMPAT) . htmlspecialchars($this->image_social_1, ENT_COMPAT) . '" style="display: none;" alt="' . htmlspecialchars($this->End_User_Name, ENT_COMPAT) . '">' . "\n";}
if ($this->image_social_2)
    {echo '<img src="//' . htmlspecialchars($this->End_User_URL, ENT_COMPAT) . htmlspecialchars($this->image_social_2, ENT_COMPAT) . '" style="display: none;" alt="' . htmlspecialchars($this->End_User_Name, ENT_COMPAT) . '">' . "\n";}

// USER ACCESS - because we need to insert it into the code twice
$user_access_links  = '';
if($this->authenticated) {
    $user_access_links .= '
    <a href="' . $this->db_user_row['landing_page'] . '" class="text-nowrap btn-contact btn btn-sm btn-red" title="Auto-Logout at ' . $this->session_expire_time .'" rel="nofollow">Portal</a>
    <a href="/a/KDpg_logout.html" class="text-nowrap btn-consultation btn btn-sm btn-blue" rel="nofollow">Logout</a>
    ';
}
else {
    $user_access_links .= '
    <a href="/register" class="text-nowrap btn-contact btn btn-sm btn-red">Register</a>
    <a href="/login" class="text-nowrap btn-consultation btn btn-sm btn-blue">Sign In</a>
    ';
}
?>

<header role="banner" id="site_header" class="d-print-none sticky-top text-bg-white">
    <nav class="navbar navbar-expand-xl d-flex flex-column" aria-label="Main Navigation">
        <div class="container-xl position-relative">
            <a href="#site_main" class="skip btn btn-primary">Skip to main content</a>
            <a href="/home" class="navbar-brand p-0 me-auto">
                <img src="/assets_2025/images/logo_first.png" class="img-fluid" alt="<?php echo $this->End_User_Name; ?> Logo">
            </a>
            <div id="nav_primary" class="offcanvas-xl offcanvas-end" tabindex="-1">
                <div class="offcanvas-header">
                    <button type="button" class="btn-close d-xl-none" data-bs-dismiss="offcanvas" data-bs-target="#nav_primary" aria-label="Close">
                        <span class="fa-solid fa-xmark" aria-hidden="true"></span>
                    </button>
                    <a href="/home" class="navbar-brand me-auto">
                        <img src="/assets_2025/images/logo_first.png" class="img-fluid" alt="<?php echo $this->End_User_Name; ?> Logo">
                    </a>
                </div>
                <div class="offcanvas-body">
                    <?php
                    echo $this->Show_List('list_C_nav_primary.');
                    ?>
                    <div class="user-access d-flex flex-column flex-xl-row gap-3">
                        <?php
                        echo $user_access_links;
                        ?>
                    </div>
                </div>
            </div>
            <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#nav_primary" aria-controls="nav_primary" aria-expanded="false" aria-label="Toggle navigation">
                <span class="fa-solid fa-bars" aria-hidden="true"></span>
            </button>
        </div>
    </nav>
</header>

<main role="main" id="site_main">

    <?php


    // SECTIONAL PAGES - pull in sections content
    if($this->db_page_row['page_type'] == 'landing') {

        // see cust_functions_pagecomposer.inc.php
        echo draw_page_sections($this);
    }

    // OTHER INTERIOR PAGES
    else {
        if ($this->db_page_row['header']) {
            $page_header = '<h1>' . $this->db_page_row['header'] . '</h1>';
        }

        echo '
        <div class="container-xl">
            <div class="content-generic">
                ' . $page_header . '
                ' . $this->db_page_row['body'] . '
            </div>
        </div>
        ';
    }
    ?>

</main>

<footer role="contentinfo" id="site_footer" class="text-bg-gray-900">
    <div class="container-xl">
        <div class="footer-row-1 d-flex flex-column flex-lg-row flex-wrap flex-xl-nowrap justify-content-between">
            <div class="footer-logo mx-auto mx-lg-0">
                <a href="/home" class="d-block">
                    <img src="/assets_2025/images/<EMAIL>" class="img-fluid" alt="FIRST in Texas Logo">
                </a>
            </div>
            <div class="footer-nav d-flex flex-column flex-md-row justify-content-between text-center text-md-start">
                <div class="footer-nav-col mx-auto mx-md-0">
                    <h2 class="fs-20 fw-bold text-uppercase mb-2">Get Involved</h2>
                    <ul class="list-unstyled fs-16 lh-lg">
                        <li><a href="/register">Register</a></li>
                        <li><a href="/donate">Donate</a></li>
                        <li><a href="/volunteer">Volunteer</a></li>
                        <li><a href="/sponsor">Sponsor</a></li>
                    </ul>
                </div>
                <div class="footer-nav-col mx-auto mx-md-0">
                    <h2 class="fs-20 fw-bold text-uppercase mb-2">Learn More</h2>
                    <ul class="list-unstyled fs-16 lh-lg">
                        <li><a href="#">Available Grants</a></li>
                        <li><a href="#">Events</a></li>
                        <li><a href="#">Resources</a></li>
                    </ul>
                </div>
                <div class="footer-nav-col mx-auto mx-md-0">
                    <h2 class="fs-20 fw-bold text-uppercase mb-2">Programs</h2>
                    <ul class="list-unstyled fs-16 lh-lg">
                        <li><a href="#">FIRST LEGO League</a></li>
                        <li><a href="#">FIRST Tech Challenge</a></li>
                        <li><a href="#">FIRST Robotics Competition</a></li>
                        <li><a href="#">FIRST Access</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-connect flex-shrink-0 d-flex flex-column flex-md-row flex-xl-column justify-content-stretch justify-content-md-between justify-content-xl-stretch mx-auto mx-md-0">
                <div class="footer-newsletter mx-auto mx-md-0">
                    <p class="fs-20 mb-3 text-center text-md-start">Sign up for our Newsletter</p>
                    <form>
                        <div class="input-group">
                            <input type="text" class="form-control rounded-0" placeholder="email address" aria-label="Email Address">
                            <button type="submit" class="btn btn-primary rounded-0">Submit</button>
                        </div>
                    </form>
                </div>
                <div class="footer-social mx-auto mx-md-0">
                    <p class="fs-20 mb-3 text-center text-md-end text-xl-start">Connect with FIRST in Texas</p>
                    <div class="footer-social-links d-flex flex-row flex-wrap gap-3 justify-content-center justify-content-md-end justify-content-xl-start">
                        <?php
                        if ($this->social_facebook) {echo '<a href="' . $this->social_facebook  . '" target="_blank" rel="noopener noreferrer nofollow"><span class="visually-hidden">Facebook</span><i class="fs-32 fa-brands fa-facebook"></i></a>' . "\n";}
                        if ($this->social_twitter) {echo '<a href="' . $this->social_twitter  . '" target="_blank" rel="noopener noreferrer nofollow"><span class="visually-hidden">Twitter</span><i class="fs-32 fa-brands fa-x-twitter"></i></a>' . "\n";}
                        if ($this->social_youtube) {echo '<a href="' . $this->social_youtube  . '" target="_blank" rel="noopener noreferrer nofollow"><span class="visually-hidden">YouTube</span><i class="fs-32 fa-brands fa-youtube"></i></a>' . "\n";}
                        if ($this->social_pinterest) {echo '<a href="' . $this->social_pinterest  . '" target="_blank" rel="noopener noreferrer nofollow"><span class="visually-hidden">Pinterest</span><i class="fs-32 fa-brands fa-pinterest"></i></a>' . "\n";}
                        if ($this->social_linkedin) {echo '<a href="' . $this->social_linkedin  . '" target="_blank" rel="noopener noreferrer nofollow"><span class="visually-hidden">LinkedIn</span><i class="fs-32 fa-brands fa-linkedin"></i></a>' . "\n";}
                        if ($this->social_instagram) {echo '<a href="' . $this->social_instagram  . '" target="_blank" rel="noopener noreferrer nofollow"><span class="visually-hidden">Instagram</span><i class="fs-32 fa-brands fa-instagram"></i></a>' . "\n";}
                        ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-row-2 justify-content-between">
            <div class="footer-legal col-12 d-flex flex-row flex-wrap gap-3 justify-content-center justify-content-xl-start text-center text-xl-start">
                <span class="w-100 w-xl-auto">&copy; <?php echo(date("Y")) . ' ' . $this->End_User_Name ?>. All Rights Reserved.</span>
                <a href="/privacy-policy">Privacy Policy</a>
                <a href="/terms-and-conditions">Terms &amp; Conditions</a>
                <?php echo $this->inspector_link('Powered by Dialogs'); ?>
            </div>
        </div>
    </div>
</footer>



<script src="/assets_2025/js/vendor/bootstrap.bundle.min.js"></script>
<script src="/assets_2025/js/public.js"></script>

<?php
echo $this->db_page_row['insert_body'] . "\n";
?>

</body>
</html>



