<?php
/**
 * File: cust_functions_pagecomposer.inc.php
 *
 * Add your custom functions here. This should be included at the end of cust_functions.inc.php, so that it's available everywhere.
 *
 * These functions may then be called from within list templates, page templates, page automation, and list automation scripts.
 *
 * @see "README" in /sections/ folder for instructions on usage. ;)
 *
 * <AUTHOR> Apps, Inc.
 * @copyright � 2006-2018 Dialogs Apps, Inc.
 *
 */


require_once('inc/class.PageSection.inc.php');
require_once('inc/cust_functions_pagecomposer_edit.inc.php');


/////////////////////////////////////////////////
/**
 * draw_KD_page_sections function.
 *
 * Accepts an ID for a page, and renders HTML for that given page's sections (if any).
 *
 * @access   public
 * @param    Page    $obj         (Dialogs $this)
 * @param    int     $page_id     KD_page.item_id (default: 0)
 * @return   string  (HTML)
 */
function draw_page_sections(Page $obj, $page_id = 0) {
    if (!is_object($obj) || !method_exists($obj, 'query')) {
        return '';
    }

    // get the current page, by default
    $page_id = (int)$page_id ? (int)$page_id : (int)$obj->db_page_row['item_id'];
    if( !$page_id ) {
        return '';
    }

    // initialize the array of HTML elements
    // (each section's HTML will be stored in this array).
    $html_array = array();

    // this loops through the sections of the current page, and renders the HTML
    $section_results = $obj->query("SELECT * 
            FROM `KD_page_section` 
            WHERE KD_page_item_id='".(int)$page_id."' 
            AND item_active=1 
            ORDER BY list_sort
            ");
    while($section_row = $obj->fetchAssoc($section_results)) {

        // we keep our sections in an array here, rather than just appending the string,
        // to help with any memory issues (because HTML strings can get quite long).
        $html_array[ $section_row['item_id'] ] = draw_KD_page_section($obj,$section_row['item_id']);

    }

    // return the imploded HTML array (one element for each section).
    return implode('',$html_array);

}

/////////////////////////////////////////////////
/**
 * draw_KD_page_section function.
 *
 * Accepts an ID for a page section, and renders HTML for that given section.
 *
 * @access   public
 * @param    Page    $obj         (Dialogs $this)
 * @param    int     $section_id  KD_page_section.item_id
 * @return   string  (HTML)
 */
function draw_KD_page_section(Page $obj, $section_id) {
    if (!is_object($obj) || !method_exists($obj, 'query')) {
        return false;
    }

    if( !$section_id ) return false;

    $section_row = $obj->getOneRowSQL("SELECT * 
            FROM `KD_page_section` 
            WHERE item_active=1 
            AND item_id='".(int)$section_id."'
            LIMIT 1
            ");

    if( $section_row['item_id'] != $section_id ) return false;

    $html = '';

    $section_html = '';
    $image_html = '';
    $button_html = '';

    // count the nodes for this section
    $node_array = [];
    $node_result = $obj->query("SELECT * 
        FROM `KD_page_node` 
        WHERE item_active=1 
        AND KD_page_section_item_id='".(int)$section_id."'
        ORDER BY list_sort, item_id
        ");
    while($node_row = $obj->fetchAssoc($node_result)) {
        $node_array[] = $node_row;
    }
    $node_count = count($node_array);

    // Determine whether this section uses a "section type", so that we can find the correct template.
    $section_type_row = array();
    if($section_row['KD_page_section_type_item_id']) {

        $section_type_row = $obj->getOneRowSQL("SELECT * FROM `KD_page_section_type` 
            WHERE item_active=1 
            AND item_id='".(int)$section_row['KD_page_section_type_item_id']."'
            ");

        // if this section refers to a section type, but we didn't find that type, we should bail.
        if( $section_type_row['item_id'] != $section_row['KD_page_section_type_item_id'] ) return false;

        // ! -- With Section Type
        // The Section Type object is useful for defining commonly reusable section templates, so that not every section
        // has its own entry here.  Note that Section Types have a node_count field, tells us how many nodes they are built to accept.
        // by default, a value of '0' means that it should have a loop to render all nodes in the same way.
        $section_file = 'section_type.' . strtolower(trim($section_type_row['css_id'])) .'.inc.php';
        $section_class_name = strtolower(trim(str_replace('-','_',$section_type_row['css_id'])));

    } else {

        // ! -- Without Section Type: Custom Section Template
        // The Sections that don't have a predefined type (as drawn above)
        // will be rendered with their own custom HTML below.
        $section_file = 'section.' . strtolower(trim($section_row['css_id'])) .'.inc.php';
        $section_class_name = strtolower(trim(str_replace('-','_',$section_row['css_id'])));

    } // end if section is based on a specific type.


    // !Get the class file for the section
    if( $section_file && file_exists($obj->document_root . '/sections/' . $section_file )) {
        include( $obj->document_root . '/sections/' . $section_file );
    } else {
        return '<p>No Section template file found (#'.$section_type_row['css_id'].'/'.$section_row['css_id'].')</p>';
    }

    // Instanciate the section class, which is defined in the file we just included.
    if(class_exists($section_class_name)) {
        $sectionClass = new $section_class_name($obj,$section_row,$node_array,$section_type_row);
        $html = $sectionClass->render_html();
    } else {
        return '<p>No Section template class defined (#'.$section_type_row['css_id'].'/'.$section_row['css_id'].')</p>';
    }

    // Allow admin 'edit' button for users
    // who can edit this page and this section.
    if($html) {

        if( $obj->pagecomposer_show_inline_edit_buttons
            && $obj->db_user_row['user_groups'] & $obj->db_page_row['group_write']
            && $obj->db_user_row['user_groups'] & $section_row['group_write']
        ) {
            $edit_button = '
                <a href="/a/KDpg_item_edit.html?lid=3&amp;item_id='.$obj->db_page_row['item_id'].'#section_'.$section_row['item_id'].'" 
                target="_blank" class="btn btn-sm btn-warning btn-edit-section"><i class="fa fa-pencil" aria-hidden="true"></i> Edit this Section</a>
            ';

            // find the end of the last HTML tag in the section, and inject the button there.
            // find the last '</' and inject just before it.
            $insert_location = strrpos($html, '</');
            if( $insert_location !== false ) {
                $html = substr($html, 0, $insert_location) . $edit_button . substr($html, $insert_location);
            }
        }

    }

    return $html;
}



/////////////////////////////////////////////////
/**
 * renderPageFilterOptions function.
 *
 * Used to draw the <select> dropdown on the list of Page Sections
 * (in list template list_KD_page_section_edit.) to allow filtering of sections by page.
 *
 * @access  public
 * @param   Page     $obj
 * @return  string   HTML
 */
function renderPageFilterOptions(Page $obj){
    if (!is_object($obj) || !method_exists($obj, 'query')) {
        return false;
    }

    $result = $obj->query("SELECT p.item_id, p.item_name, p.menu_display, p.page_group_item_name 
    FROM KD_page_section s
    LEFT JOIN KD_page p
    ON s.KD_page_item_id = p.item_id
    WHERE 1 
    GROUP BY p.item_id 
    ORDER BY p.page_group_item_id, p.list_sort");

    $html = '';

    while($row = $obj->fetch_assoc($result)){
        if($page_group != $row['page_group_item_name']){
            if($html != ''){
                $html .='</optgroup>';
            }
            $page_group = $row['page_group_item_name'];
            $html .= '<optgroup label="'.$page_group.'">';
        }
        $selected = '';
        if($_SESSION['page_sections']['filter_1'] == $row['item_id']){
            $selected = 'selected';
        }
        $html .= '<option value="'.$row['item_id'].'" '.$selected.'>'.$row['item_name'].'</option>';
    }

    return $html;

}

/////////////////////////////////////////////////
/**
 * renderPageSectionFilterOptions function.
 *
 * Used to draw the <select> dropdown on the list of Page Nodes
 * (in list template list_KD_page_node_edit.) to allow filtering of sections by page.
 *
 * @access  public
 * @param   Page     $obj
 * @return  string   HTML
 */
function renderPageSectionFilterOptions(Page $obj){
    if (!is_object($obj) || !method_exists($obj, 'query')) {
        return false;
    }

    $result = $obj->query("SELECT p.item_name, p.item_id, p.page_group_item_name
    FROM KD_page_node n
    LEFT JOIN KD_page_section s
    ON s.item_id = n.KD_page_section_item_id
    LEFT JOIN KD_page p
    ON s.KD_page_item_id = p.item_id
    WHERE 1
    GROUP BY p.item_id
    ORDER BY p.page_group_item_id, p.item_name");

    $html = '';

    while($page_row = $obj->fetch_assoc($result)){
        if($page_group != $page_row['page_group_item_name']){
            if($html != ''){
                $html .='</optgroup>';
            }
            $page_group = $page_row['page_group_item_name'];
            $html .= '<optgroup label="'.$page_group.'">';
        }


        $html .= '<option value="';

        $result2 = $obj->query("SELECT item_id FROM KD_page_section WHERE KD_page_item_id='".$page_row['item_id']."'");
        $section_ids = '';
        while($section_row = $obj->fetch_assoc($result2)){
            $section_ids .= $section_row['item_id'].',';

        }
        $section_ids = trim($section_ids,',');

        $selected = '';
        if($_SESSION['page_nodes']['filter_1'] == $section_ids){
            $selected = 'selected';
        }

        $html .= $section_ids.'" '.$selected.'>'.$page_row['item_name'].'</option>';

    }

    return $html;
}


/**
 * update_posted_sections function.
 *
 * Looks for data in $_POST and $_FILES to store into sections based on the given page id.
 * Handles INSERT, UPDATE and DELETE.
 *
 * @access public
 * @param  Db   $obj
 * @param  int  $page_id
 * @return void
 */
function update_posted_sections(Db $obj, int $page_id) {

    $page_row = $obj->getOneRowV2("KD_page","item_id",$page_id);
    if( !$page_row['item_id'] ) {
        return;
    }

    $tableName = "KD_page_section";
    $prefix = "KPS_";
    $tableLid = $obj->getFieldContents('KD_list','item_name',$tableName,'item_id');
    for($i=0;$i<100;$i++) {

        // First, we must handle the uploaded files
        // we the function handleUploadFiles uses values out of $obj, so we have to modify it.
        // it also modifies $_POST, so we have to trick it to give us the proper values without stepping on stuff that's there.
        $old_db_list = $obj->db_KD_list;
        $obj->db_KD_list = $obj->getOneRowV2("KD_list","item_name",$tableName);
        $extraDigit = '';
        for($k=0;$k<=1;$k++) {
            $extraDigit = (($k==0) ? '':$k+1);
            if((isset($_FILES[$prefix.'file_upload' . $extraDigit]['name'][ $i ]))
                && ($_FILES[$prefix.'file_upload' . $extraDigit]['tmp_name'][ $i ])
                && ($_FILES[$prefix.'file_upload' . $extraDigit]['tmp_name'][ $i ] != 'none')
            ) {
                $obj->log_message("handling file: ".$_FILES[$prefix.'file_upload' . $extraDigit]['name'][ $i ]);
                $old_post_val = $_POST['attachment_filename' . $extraDigit];
                handleUploadFile($_FILES[$prefix.'file_upload' . $extraDigit]['tmp_name'][ $i ]
                    ,$_FILES[$prefix.'file_upload' . $extraDigit]['name'][ $i ]
                    ,'attachment_filename' . $extraDigit
                    ,$obj);
                $_POST[$prefix.'attachment_filename' . $extraDigit][ $i ] = $_POST['attachment_filename' . $extraDigit];
                $_POST['attachment_filename' . $extraDigit] = $old_post_val;
                $obj->log_message("new POST attachment_filename: ".$_POST[$prefix.'attachment_filename' . $extraDigit]);
            }
        }
        $obj->db_KD_list = $old_db_list;
        // end uploaded file script

        $sectID = 0;
        $sectName = '';
        if( $_POST[$prefix.'list_sort'][$i]
            || $_POST[$prefix.'item_name'][$i]
            || $_POST[$prefix.'item_id'][$i]
            || $_POST[$prefix.'KD_page_section_type_item_id'][$i]
        ) {

            $sql = "
                KD_page_item_id='".$obj->new_row['item_id']."'
                ,KD_page_item_name='".$obj->real_escape_string($obj->new_row['item_name'])."'
                ,list_sort='".$obj->real_escape_string(stripslashes($_POST[$prefix.'list_sort'][$i]))."'
                ,item_name='".$obj->real_escape_string(stripslashes($_POST[$prefix.'item_name'][$i]))."'
                ,section_notes='".$obj->real_escape_string(stripslashes($_POST[$prefix.'section_notes'][$i]))."'
                ,css_id='".$obj->real_escape_string(stripslashes($_POST[$prefix.'css_id'][$i]))."'
                ,css_class='".$obj->real_escape_string(stripslashes($_POST[$prefix.'css_class'][$i]))."'
                ,section_title='".$obj->real_escape_string(stripslashes($_POST[$prefix.'section_title'][$i]))."'
                ,KD_page_section_type_item_id='".(int)$_POST[$prefix.'KD_page_section_type_item_id'][$i]."'
                ,attachment_filename='".$obj->real_escape_string(stripslashes($_POST[$prefix.'attachment_filename'][$i]))."'
                ,attachment_filename2='".$obj->real_escape_string(stripslashes($_POST[$prefix.'attachment_filename2'][$i]))."'
                ,option_text_layout='".$obj->real_escape_string(stripslashes($_POST[$prefix.'option_text_layout'][$i]))."'
                ,option_background_color='".$obj->real_escape_string(stripslashes($_POST[$prefix.'option_background_color'][$i]))."'
                ,section_css='".$obj->real_escape_string(stripslashes($_POST[$prefix.'section_css'][$i]))."'
                ,show_in_menu='".(int)$_POST[$prefix.'show_in_menu'][$i]."'
                ,lock_nodes='".(int)$_POST[$prefix.'lock_nodes'][$i]."'
                ";
            if((int)$_POST[$prefix.'item_id'][$i]) {
                $sql = "UPDATE `$tableName` SET item_active=item_active, " . $sql . " 
                WHERE item_id=".(int)$_POST[$prefix.'item_id'][$i]." LIMIT 1;";
                $obj->addRevision( $tableLid, (int)$_POST[$prefix.'item_id'][$i] );
            } else {
                $sql = "INSERT INTO `$tableName` SET item_active=1, " . $sql . "
                ".$obj->usual_fields($tableName,array('item_active'));
            }
            if($_POST[$prefix.'delete'][$i]=='Y') {
                $sql = "DELETE FROM $tableName WHERE item_id=".(int)$_POST[$prefix.'item_id'][$i]." LIMIT 1;";
                // remove grandchildren
                $obj->query("DELETE FROM KD_page_node WHERE KD_page_section_item_id=".(int)$_POST[$prefix.'item_id'][$i].";");
                $obj->addRevision( $tableLid, (int)$_POST[$prefix.'item_id'][$i], 'Y' );
            }

            $obj->log_message("after uploading any file for i=$i, SQL: $sql POST: ".print_r($_POST,true));

            //$obj->log_message("SECTION SQL $sql");

            $result = $obj->query($sql);
            if(!$result) {
                $obj->log_message ( "save $tableName sql error $sql" . mysql_error(),'E');
                echo "Error 1007"; // intentionally vague for security reasons.
                exit ();
            }

            if((int)$_POST[$prefix.'item_id'][$i]) {
                $sectID = (int)$_POST[$prefix.'item_id'][$i];
            } else {
                $sectID = $obj->insert_id();
            }
            $sectName = stripslashes($_POST[$prefix.'item_name'][$i]);
            $obj->log_message("adding $tableName as child row for item_id=".$obj->new_row['item_id']." SQL=".$sql);

            update_posted_nodes($obj,$sectID,$prefix,$i);

        } // end if()
    } // end for($i)
}


/**
 * update_posted_nodes function.
 *
 * Looks for data in $_POST and $_FILES to store into nodes based on the given section id.
 * Handles INSERT, UPDATE and DELETE.
 *
 * @access public
 * @param  Db   $obj
 * @param  int  $section_id
 * @return void
 */
function update_posted_nodes(Db $obj, int $section_id, string $prefix = '', int $sectionIndex = 0) {

    //$obj->log_message(__FUNCTION__.'('. var_export(func_get_args(),true) . ')');

    $section_row = $obj->getOneRowV2("KD_page_section","item_id",$section_id);
    $sectName = $section_row['item_name'];
    if( $prefix ) {
        $subIndex = (int)$_POST[$prefix.'item_id'][$sectionIndex] . "_" . (int)$_POST[$prefix.'temp_id'][$sectionIndex];
    } else {
        $subIndex = (int)$_POST['item_id'] . "_" . 0;
    }

    ////////////////////////////////////
    // handle nodes for this section
    // note that the values come in $_POST like so:
    //	Array (
    //		...
    //		KPN_list_sort: Array (				<----- "KPN_" here is the prefix for all fields related to nodes,
    //													which are grouped by section as follows
    //			[23_0] => Array (   			<----- The first part of this represents KD_page_section.item_id
    //													see $subIndex
    //				[0] => '01',
    //				[1] => '02'
    //				...
    //			),
    //			[0_1449682064655] => Array (	<----- The second part of this is a timestamp from Javascript for NEW sections
    //													see $subIndex
    //				[0] => '01',
    //				[1] => '02'
    //				...
    //			)
    //		)
    //		...
    //	)
    $subTableName = "KD_page_node";
    $subPrefix = "KPN_";
    //$subIndex = (int)$_POST[$prefix.'item_id'][$i] . "_" . (int)$_POST[$prefix.'temp_id'][$i];
    $subTableLid = $obj->getFieldContents('KD_list','item_name',$subTableName,'item_id');
    for($j=0;$j<100;$j++) {

        // First, we must handle the uploaded files
        // we the function handleUploadFiles uses values out of $obj, so we have to modify it.
        // it also modifies $_POST, so we have to trick it to give us the proper values without stepping on stuff that's there.
        $old_db_list = $obj->db_KD_list;
        $obj->db_KD_list = $obj->getOneRowV2("KD_list","item_name",$subTableName);
        if(is_array($_FILES[$subPrefix.'file_upload']['name'][$subIndex][$j])) {
            foreach($_FILES[$subPrefix.'file_upload']['name'][$subIndex][$j] as $k=>$unused) {
                $extraDigit = (($k==0) ? '':$k+1);
                if( $_FILES[$subPrefix.'file_upload']['tmp_name'][$subIndex][$j][$k]
                    && $_FILES[$subPrefix.'file_upload']['tmp_name'][$subIndex][$j][$k] != 'none'
                    && file_exists($_FILES[$subPrefix.'file_upload']['tmp_name'][$subIndex][$j][$k])
                ) {
                    $obj->log_message("handling file $k (x:$extraDigit):".$_FILES[$subPrefix.'file_upload']['name'][$subIndex][$j][$k]);
                    $old_post_val = $_POST['attachment_filename' . $extraDigit];
                    handleUploadFile($_FILES[$subPrefix.'file_upload']['tmp_name'][$subIndex][$j][$k]
                        ,$_FILES[$subPrefix.'file_upload']['name'][$subIndex][$j][$k]
                        ,'attachment_filename' . $extraDigit
                        ,$obj);
                    $_POST[$subPrefix.'attachment_filename' . $extraDigit][$subIndex][$j] = $_POST['attachment_filename' . $extraDigit];
                    $_POST['attachment_filename' . $extraDigit] = $old_post_val;
                    $obj->log_message("new POST attachment_filename: ".$_POST[$subPrefix.'attachment_filename' . $extraDigit][$subIndex][$j]);
                }
            }
        } else {
            if((isset($_FILES[$subPrefix.'file_upload'][$subIndex][$j]))
                && ($_FILES[$subPrefix.'file_upload']['name'][$subIndex][$j])
                && ($_FILES[$subPrefix.'file_upload']['tmp_name'][$subIndex][$j] != 'none')
            ) {
                $obj->log_message("handling file: ".$_FILES[$subPrefix.'file_upload']['name'][$subIndex][$j]);
                $old_post_val = $_POST['attachment_filename' . $extraDigit];
                handleUploadFile($_FILES[$subPrefix.'file_upload']['tmp_name'][$subIndex][$j]
                    ,$_FILES[$subPrefix.'file_upload']['name'][$subIndex][$j]
                    ,'attachment_filename'
                    ,$obj);
                $_POST[$subPrefix.'attachment_filename'][$subIndex][$j] = $_POST['attachment_filename' . $extraDigit];
                $_POST['attachment_filename' . $extraDigit] = $old_post_val;
                $obj->log_message("new POST attachment_filename: ".$_POST[$subPrefix.'attachment_filename'][$subIndex][$j]);
            }
        }
        $obj->db_KD_list = $old_db_list;
        // end uploaded file script

        if( $_POST[$subPrefix.'list_sort'][$subIndex][$j]
            || $_POST[$subPrefix.'item_name'][$subIndex][$j]
            || $_POST[$subPrefix.'item_id'][$subIndex][$j]
            || $_POST[$subPrefix.'headline'][$subIndex][$j]
            || $_POST[$subPrefix.'node_text'][$subIndex][$j]
            || $_POST[$subPrefix.'button_text_1'][$subIndex][$j]
            || $_POST[$subPrefix.'button_url_1'][$subIndex][$j]
            || $_POST[$subPrefix.'attachment_filename'][$subIndex][$j]
        ) {

            // Look for any list template tokens in text fields
            $field_array = ['headline','node_text'];
            $field_values = [];
            foreach($field_array as $field_name) {
                $field_values[] = stripslashes($_POST[$subPrefix.$field_name][$subIndex][$j]);
            }
            $list_views = findListTemplatesInStrings($obj, $field_values, true);

            $sql = "
                KD_page_section_item_id='".$section_id."'
                ,KD_page_section_item_name='".$obj->real_escape_string($sectName)."'
                ".((isset($_POST[$subPrefix.'list_sort'][$subIndex][$j]))?"
                    ,list_sort='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'list_sort'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'item_name'][$subIndex][$j]))?"
                    ,item_name='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'item_name'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'headline'][$subIndex][$j]))?"
                    ,headline='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'headline'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'node_text'][$subIndex][$j]))?"
                    ,node_text='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'node_text'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'use_wysiwyg'][$subIndex][$j]))?"
                    ,use_wysiwyg='".(int)$_POST[$subPrefix.'use_wysiwyg'][$subIndex][$j]."'
                ":'')."
                ".((isset($_POST[$subPrefix.'css_id'][$subIndex][$j]))?"
                    ,css_id='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'css_id'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'css_class'][$subIndex][$j]))?"
                    ,css_class='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'css_class'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'button_text_1'][$subIndex][$j]))?"
                    ,button_text_1='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'button_text_1'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'button_url_1'][$subIndex][$j]))?"
                    ,button_url_1='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'button_url_1'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'button_external_1'][$subIndex][$j]))?"
                    ,button_external_1='".(int)$_POST[$subPrefix.'button_external_1'][$subIndex][$j]."'
                ":'')."
                ".((isset($_POST[$subPrefix.'button_text_2'][$subIndex][$j]))?"
                    ,button_text_2='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'button_text_2'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'button_url_2'][$subIndex][$j]))?"
                    ,button_url_2='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'button_url_2'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'button_external_2'][$subIndex][$j]))?"
                    ,button_external_2='".(int)$_POST[$subPrefix.'button_external_2'][$subIndex][$j]."'
                ":'')."
                ".((isset($_POST[$subPrefix.'attachment_filename'][$subIndex][$j]))?"
                    ,attachment_filename='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'attachment_filename'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'attachment_filename2'][$subIndex][$j]))?"
                    ,attachment_filename2='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'attachment_filename2'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'attachment_filename3'][$subIndex][$j]))?"
                    ,attachment_filename3='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'attachment_filename3'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'image_alt_text'][$subIndex][$j]))?"
                    ,image_alt_text='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'image_alt_text'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'image_alt_text_2'][$subIndex][$j]))?"
                    ,image_alt_text_2='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'image_alt_text_2'][$subIndex][$j]))."'
                ":'')."
                ".((isset($_POST[$subPrefix.'image_alt_text_3'][$subIndex][$j]))?"
                    ,image_alt_text_3='".$obj->real_escape_string(stripslashes($_POST[$subPrefix.'image_alt_text_3'][$subIndex][$j]))."'
                ":'')."
                ,list_views = '".$obj->real_escape_string($list_views)."'
                ";
            if((int)$_POST[$subPrefix.'item_id'][$subIndex][$j]) {
                $sql = "UPDATE `$subTableName` SET item_active=item_active, " . $sql . " 
                WHERE item_id=".(int)$_POST[$subPrefix.'item_id'][$subIndex][$j]." LIMIT 1;";
                $obj->addRevision( $subTableLid, (int)$_POST[$subPrefix.'item_id'][$subIndex][$j] );
            } else {
                $sql = "INSERT INTO `$subTableName` SET item_active=1, " . $sql . "
                ".$obj->usual_fields($subTableName,array('item_active'));
            }
            if($_POST[$subPrefix.'delete'][$subIndex][$j]=='Y') {
                $sql = "DELETE FROM $subTableName WHERE item_id=".(int)$_POST[$subPrefix.'item_id'][$subIndex][$j]." LIMIT 1;";
                $obj->addRevision( $subTableLid, (int)$_POST[$subPrefix.'item_id'][$subIndex][$j], 'Y' );
            }

            $obj->log_message("NODE SQL $sql");

            $result = $obj->query($sql);
            if(!$result) {
                $obj->log_message ( "save $subTableName sql error $sql" . $obj->most_recent_error,'E');
                echo "Error 1008"; // intentionally vague for security reasons.
                exit ();
            }

            $obj->log_message("adding $subTableName as child row for item_id=".$obj->new_row['item_id']." SQL=".$sql);
        }
    } // end for($j)

}


/**
 * findListTemplatesInStrings()
 *
 * examines a string (or array of strings) for existence of all known active list template names.
 *
 * returns the list templates referenced, either as an array or a pipe-delimited string.
 *
 * @see    /inc/KD_page.post.inc.php
 * @param  Db           $obj
 * @param  array|string $stringArray
 * @param  bool         $return_piped_string
 * @return mixed        array or pipe-delimited string
 */
function findListTemplatesInStrings(Db $obj, $stringArray = [], bool $return_piped_string = false) {

    if(!is_array($stringArray)) {
        $stringArray = [$stringArray];
    }

    $list_views_array = [];

    $result = $obj->query("SELECT * FROM KD_list_tpl WHERE item_active=1");
    if($result) {
        while($list_tpl_row = $obj->fetchAssoc($result)) {
            // See if this list template(AKA list view) is in any of these TEXT fields
            foreach($stringArray as $string) {

                // Find the list template name in the page
                if(has_list_in_it($string, $list_tpl_row['item_name'])) {

                    // Save the name of the list template
                    $list_views_array[] = $list_tpl_row['item_name'];

                }
            }
        }
    }
    $list_views_array = array_unique($list_views_array);

    if( $return_piped_string ) {
        return ( count($list_views_array) ? '|' . implode('|,|', $list_views_array) . '|' : '' );
    }
    return $list_views_array;

}