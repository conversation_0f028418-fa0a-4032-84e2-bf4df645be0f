<?php
/**
 * File: /home/<USER>/public_html/inc/KD_list_tpl/KD_list_tpl.C_nav_primary.inc.php
 *
 * saved with ListTemplateCodeHandler::generate_list_template_code()
 *
 */
 
Namespace ListTemplate\Template;

//require_once('../lib/AbstractListTemplate.php');

/**
 * Class cust_list_tpl_C_nav_primary
 *
 * @extends     \ListTemplate\AbstractListTemplate
 * @implements  \ListTemplate\ListTemplateInterface
 *
 */
class cust_list_tpl_C_nav_primary 
    extends     \ListTemplate\AbstractListTemplate  
{

    /**
     * $KDlist_name property.
     * 
     * @var     string
     * @access  protected
     */
    /** VIEW_LIST_NAME START **/
    protected $KDlist_name = 'KD_page';
    /** VIEW_LIST_NAME END **/

    // @see abstract class
    // var $KDlist_id 

    // @see abstract class
    // var $KDlist_paging 

    // @see abstract class
    // var $KDlist_paging_at_start 

    // @see abstract class
    // var $KDlist_paging_at_end 

    // @see abstract class
    // var $KDlist_quickedit 

    // @see abstract class
    // var $KDlist_userlimit 

    // @see abstract class
    // var $KDlist_treeview 

    // @see abstract class
    // var $KDlist_debugging 

    // @see abstract class
    // function build_SQL_fragment_view_fields 

    // @see abstract class
    // function build_SQL_fragment_table_name 

    /**
     * build_SQL_fragment_view_where function.
     * 
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_view_where () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** VIEW_WHERE START **/
        $sql_fragment="item_active=1 AND show_in_menu=1 AND page_group_item_id=5 AND position_level=0";
        /** VIEW_WHERE END **/

        return $sql_fragment;
    }

    // @see abstract class
    // function build_SQL_fragment_view_group_by 

    // @see abstract class
    // function build_SQL_fragment_view_having 

    /**
     * build_SQL_fragment_view_sort function.
     * 
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_view_sort () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** VIEW_SORT START **/
        $sql_fragment="list_sort, menu_display";
        /** VIEW_SORT END **/

        return $sql_fragment;
    }

    // @see abstract class
    // function build_SQL_fragment_view_limit 

    // @see abstract class
    // function build_SQL_fragment_search_form 

    // @see abstract class
    // function build_SQL_fragment_list_header 

    /**
     * render_item_format function.
     * 
     * @access public
     * @param  array  $row_array
     * @param  int    $item_count the current list item
     * @return string $html
     */
    public function render_item_format($row_array, $item_count) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        $row = $row_array[$item_count];
        $detail_link = $_SERVER['REQUEST_URI'] .  ((false !== strpos($_SERVER['REQUEST_URI'],'?'))? '&':'?') . $KDlist_id . '=' . $row['item_id'] . addGetVars('&',array("$KDlist_id"));
        $detail_back_link = ((isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'])?$_SERVER['HTTP_REFERER']:'');

        
        /** ITEM_FORMAT START **/
        $isopen = '';
if (
    (
        ($obj->db_page_row['item_name'] == $row['item_name']) &&
        ($obj->db_page_row['page_group_item_id'] == $row['page_group_item_id'])
    ) OR
    (
        ($obj->db_page_row['position_level'] >= $row['position_level']) &&
        (0 == strncmp($obj->db_page_row['list_sort'],$row['list_sort'],strlen($row['list_sort']))) &&
        ($obj->db_page_row['page_group_item_id'] == $row['page_group_item_id'])
    )
) {
    $isopen = 'y';
}
$html = '';
if ($item_count == 0 AND $row['position_level'] == 0) {
    $html = '<ul class="navbar-nav">' . "\n";
}
elseif ($item_count == 0 AND $row['position_level'] == 1) {
    $html = '<ul class="dropdown-menu">' . "\n";
}
elseif ($item_count == 0 AND $row['position_level'] > 1) {
    $html = '<ul class="nested-menu">' . "\n";
}

if ($row['has_child'] == 'Y') {
    $html .= '<li class="nav-item ' . (($row['position_level'] == 0) ? 'dropdown' : '') . (($isopen == 'y') ? ' active' : '') . '">';
    if ($row['position_level'] == 0) {
        $html .= '<a href="#" class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown" aria-expanded="false">' . $row['menu_display'] . '</a>';
    }
    else {
        $html .= '<a href="' . $row['item_name'] . '" class="nav-link" ' . (($row['is_external'] == 'Y') ? 'target="_blank"' : '') . '>' . $row['menu_display'] . '</a>';
    }

    $html .= "\n" . $obj->Show_List('list_C_nav_primary.',array('view_where'=>"
                                                                item_active=1 AND 
                                                                show_in_menu=1 AND 
                                                                list_sort LIKE '".$obj->real_escape_string($row['list_sort']).".%' AND 
                                                                list_sort NOT LIKE '".$obj->real_escape_string($row['list_sort']).".%.%' AND 
                                                                page_group_item_id=" . (int)$obj->db_page_row['page_group_item_id'] . " AND 
                                                                position_level=" . (int)($row['position_level'] + 1)
        ));

    $html .= '</li>' . "\n";
}

else {
    $html .= '<li class="nav-item ' . (($isopen == 'y') ? ' active' : '') . '">';
    $html .= '<a href="/' . $row['item_name'] . '" class="nav-link" ' . (($row['is_external'] == 'Y') ? 'target="_blank"' : '') . '>' . $row['menu_display'] . '</a>';
    $html .= '</li>' . "\n";
}
        /** ITEM_FORMAT END **/

        return $html;
    }
    /**
     * @access public
     * @param  \Db   $obj (default: null)
     * @param  array  $row_array
     * @return string $html
     */
    public function render_list_footer ($row_array) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** LIST_FOOTER START **/
        $html = "</ul>
";
        /** LIST_FOOTER END **/

        return $html;
    }

    // @see abstract class
    // function build_SQL_fragment_empty_msg 

    // @see abstract class
    // function build_SQL_fragment_paging_format 

    // @see abstract class
    // function build_SQL_fragment_detail_format 

    // @see abstract class
    // function build_SQL_fragment_error_msg 
}