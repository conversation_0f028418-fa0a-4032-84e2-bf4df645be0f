<?php
/**
 * File: /home/<USER>/public_html/inc/KD_list_tpl/KD_list_tpl.C_staff_team_detail.inc.php
 *
 * saved with ListTemplateCodeHandler::generate_list_template_code()
 *
 */
 
Namespace ListTemplate\Template;

//require_once('../lib/AbstractListTemplate.php');

/**
 * Class cust_list_tpl_C_staff_team_detail
 *
 * @extends     \ListTemplate\AbstractListTemplate
 * @implements  \ListTemplate\ListTemplateInterface
 *
 */
class cust_list_tpl_C_staff_team_detail 
    extends     \ListTemplate\AbstractListTemplate  
{

    /**
     * $KDlist_name property.
     * 
     * @var     string
     * @access  protected
     */
    /** VIEW_LIST_NAME START **/
    protected $KDlist_name = 'C_team_bios';
    /** VIEW_LIST_NAME END **/

    // @see abstract class
    // var $KDlist_id 

    // @see abstract class
    // var $KDlist_paging 

    // @see abstract class
    // var $KDlist_paging_at_start 

    // @see abstract class
    // var $KDlist_paging_at_end 

    // @see abstract class
    // var $KDlist_quickedit 

    // @see abstract class
    // var $KDlist_userlimit 

    // @see abstract class
    // var $KDlist_treeview 

    // @see abstract class
    // var $KDlist_debugging 

    // @see abstract class
    // function build_SQL_fragment_view_fields 

    /**
     * build_SQL_fragment_table_name function.
     * 
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_table_name () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** TABLE_NAME START **/
        $sql_fragment="C_team_bios";
        /** TABLE_NAME END **/

        return $sql_fragment;
    }

    /**
     * build_SQL_fragment_view_where function.
     * 
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_view_where () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** VIEW_WHERE START **/
        $sql_fragment="C_team_bios.item_active=1 AND C_team_bios.slug='" . $obj->real_escape_string($obj->phantom_row['slug']) . "'";
        /** VIEW_WHERE END **/

        return $sql_fragment;
    }

    // @see abstract class
    // function build_SQL_fragment_view_group_by 

    // @see abstract class
    // function build_SQL_fragment_view_having 

    /**
     * build_SQL_fragment_view_sort function.
     * 
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_view_sort () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** VIEW_SORT START **/
        $sql_fragment="list_sort";
        /** VIEW_SORT END **/

        return $sql_fragment;
    }

    // @see abstract class
    // function build_SQL_fragment_view_limit 

    // @see abstract class
    // function build_SQL_fragment_search_form 
    /**
     * @access public
     * @param  \Db   $obj (default: null)
     * @param  array  $row_array
     * @return string $html
     */
    public function render_list_header ($row_array) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** LIST_HEADER START **/
        $html.='
        ';
        /** LIST_HEADER END **/

        return $html;
    }

    /**
     * render_item_format function.
     * 
     * @access public
     * @param  array  $row_array
     * @param  int    $item_count the current list item
     * @return string $html
     */
    public function render_item_format($row_array, $item_count) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        $row = $row_array[$item_count];
        $detail_link = $_SERVER['REQUEST_URI'] .  ((false !== strpos($_SERVER['REQUEST_URI'],'?'))? '&':'?') . $KDlist_id . '=' . $row['item_id'] . addGetVars('&',array("$KDlist_id"));
        $detail_back_link = ((isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'])?$_SERVER['HTTP_REFERER']:'');

        
        /** ITEM_FORMAT START **/
        $item_image = '';
        if ($row['attachment_filename']) {
            $item_image = '
            <img src="/images/staff_team_bios/' . $row['attachment_filename'] . '" alt="' . $row['item_name']. ', ' . $row['title'] . '">
            ';
        }
        else {
            $item_image = '
            <img src="/images/staff_team_bios/FIT_placeholder_square.png" alt="' . $row['item_name']. ', ' . $row['title'] . '">
            ';
        }
        $item_title = '';
        if ($row['title']) {
            $item_title = '
            <h2 class="fs-16 text-uppercase fw-semibold">' . $row['title'] . '</h2>
            ';
        }
        $item_company = '';
        if ($row['company_name']) {
            $item_company = '
            <div class="mb-2 fs-16"><strong class="text-uppercase fw-semibold">Company:</strong> ' . $row['company_name'] . '</div>
            ';
        }
        $item_phone = '';
        if ($row['phone']) {
            $item_phone = '
            <div class="mb-2 fs-16"><strong class="text-uppercase fw-semibold">Phone:</strong> ' . $row['phone'] . '</div>
            ';
        }
        $item_email = '';
        if ($row['email']) {
            $item_email = '
            <div class="mb-2 fs-16"><strong class="text-uppercase fw-semibold">Email:</strong> <a href="mailto:' . $row['email'] . '">' . $row['email'] . '</a></div>
            ';
        }
        $item_bio = '';
        if ($row['bio']) {
            $item_bio = '
            <div class="staff-detail-bio">
                ' . $row['bio'] . '
            </div>
            ';
        }
        $item_why = '';
        if ($row['why_i_give']) {
            $item_why = '
            <div class="staff-detail-why mt-5 pt-5 border-top border-5 border-gray-200">
                <h2 class="h3 mb-5">Why I give...</h2>
                <div class="item-quote">
                    ' . $row['why_i_give'] . '
                </div>
            </div>
            ';
        }
        $html.='
        <div class="staff-detail row justify-content-center align-items-start">
            <div class="col-12 col-xl-10">
                <div class="row justify-content-between align-items-start">
                    <div class="col-12 col-lg-4">
                        <div class="staff-list-item-image ratio ratio-1x1 rounded-3 overflow-hidden mb-5">
                            ' . $item_image . '
                        </div>
                        <div class="staff-detail-info">
                            ' . $item_company . '
                            ' . $item_phone . '
                            ' . $item_email . '
                        </div>
                    </div>
                    <div class="col-12 col-lg-8">
                        <div class="detail-header mb-5">
                            <h1 class="">' . $row['item_name'] . '</h1>
                            ' . $item_title . '
                        </div>
                        ' . $item_bio . '
                        ' . $item_why . '
                    </div>
                </div>
                <div class="mt-5">
                    <a href="/our-team" class="btn btn-sm btn-primary link-return"><i class="fa-solid fa-angles-left me-2" aria-hidden="true"></i> Return to OUR TEAM</a>
                </div>
            </div>
        </div>
        ';
        /** ITEM_FORMAT END **/

        return $html;
    }
    /**
     * @access public
     * @param  \Db   $obj (default: null)
     * @param  array  $row_array
     * @return string $html
     */
    public function render_list_footer ($row_array) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** LIST_FOOTER START **/
        $html.="
        ";
        /** LIST_FOOTER END **/

        return $html;
    }
    /**
     * @access public
     * @param  \Db   $obj (default: null)
     * @param  array  $row_array
     * @return string $html
     */
    public function render_empty_msg ($row_array) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** EMPTY_MSG START **/
        $html.="<p>*** No Items ***</p>";
        /** EMPTY_MSG END **/

        return $html;
    }

    // @see abstract class
    // function build_SQL_fragment_paging_format 

    // @see abstract class
    // function build_SQL_fragment_detail_format 

    // @see abstract class
    // function build_SQL_fragment_error_msg 
}