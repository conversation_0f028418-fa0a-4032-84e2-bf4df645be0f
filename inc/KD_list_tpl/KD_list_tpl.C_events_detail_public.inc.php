<?php
/**
 * File: /home/<USER>/public_html/inc/KD_list_tpl/KD_list_tpl.C_events_detail_public.inc.php
 *
 * saved with ListTemplateCodeHandler::generate_list_template_code()
 *
 */
 
Namespace ListTemplate\Template;

//require_once('../lib/AbstractListTemplate.php');

/**
 * Class cust_list_tpl_C_events_detail_public
 *
 * @extends     \ListTemplate\AbstractListTemplate
 * @implements  \ListTemplate\ListTemplateInterface
 *
 */
class cust_list_tpl_C_events_detail_public 
    extends     \ListTemplate\AbstractListTemplate  
{

    /**
     * $KDlist_name property.
     * 
     * @var     string
     * @access  protected
     */
    /** VIEW_LIST_NAME START **/
    protected $KDlist_name = 'C_events';
    /** VIEW_LIST_NAME END **/

    // @see abstract class
    // var $KDlist_id 

    // @see abstract class
    // var $KDlist_paging 

    // @see abstract class
    // var $KDlist_paging_at_start 

    // @see abstract class
    // var $KDlist_paging_at_end 

    // @see abstract class
    // var $KDlist_quickedit 

    // @see abstract class
    // var $KDlist_userlimit 

    // @see abstract class
    // var $KDlist_treeview 

    // @see abstract class
    // var $KDlist_debugging 

    // @see abstract class
    // function build_SQL_fragment_view_fields 

    /**
     * build_SQL_fragment_table_name function.
     * 
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_table_name () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** TABLE_NAME START **/
        $sql_fragment="C_events";
        /** TABLE_NAME END **/

        return $sql_fragment;
    }

    /**
     * build_SQL_fragment_view_where function.
     * 
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_view_where () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** VIEW_WHERE START **/
        $sql_fragment="item_active=1 AND slug='" . $obj->real_escape_string($obj->phantom_row['slug']) . "'";
        /** VIEW_WHERE END **/

        return $sql_fragment;
    }

    // @see abstract class
    // function build_SQL_fragment_view_group_by 

    // @see abstract class
    // function build_SQL_fragment_view_having 

    /**
     * build_SQL_fragment_view_sort function.
     * 
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_view_sort () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** VIEW_SORT START **/
        $sql_fragment="start_date DESC, end_date DESC, item_name";
        /** VIEW_SORT END **/

        return $sql_fragment;
    }

    /**
     * build_SQL_fragment_view_limit function.
     * 
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_view_limit () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** VIEW_LIMIT START **/
        $sql_fragment="1";
        /** VIEW_LIMIT END **/

        return $sql_fragment;
    }

    // @see abstract class
    // function build_SQL_fragment_search_form 
    /**
     * @access public
     * @param  \Db   $obj (default: null)
     * @param  array  $row_array
     * @return string $html
     */
    public function render_list_header ($row_array) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** LIST_HEADER START **/
        $html.='
        <div class="events-detail">
        ';
        /** LIST_HEADER END **/

        return $html;
    }

    /**
     * render_item_format function.
     * 
     * @access public
     * @param  array  $row_array
     * @param  int    $item_count the current list item
     * @return string $html
     */
    public function render_item_format($row_array, $item_count) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        $row = $row_array[$item_count];
        $detail_link = $_SERVER['REQUEST_URI'] .  ((false !== strpos($_SERVER['REQUEST_URI'],'?'))? '&':'?') . $KDlist_id . '=' . $row['item_id'] . addGetVars('&',array("$KDlist_id"));
        $detail_back_link = ((isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'])?$_SERVER['HTTP_REFERER']:'');

        
        /** ITEM_FORMAT START **/
        $item_date = '';
        if ($row['start_date'] && $row['end_date']) {
            $item_date = reformat_date('M d Y',$row['start_date']) . ' - ' . reformat_date('M d Y',$row['end_date']);
        }
        else {
            $item_date = reformat_date('M d Y',$row['start_date']);
        }
        $item_image = '';
        if ($row['attachment_filename5']) {
            $item_image = '<img src="/images/events/' . $row['attachment_filename5'] . '" class="img-fluid rounded" alt="' . $row['item_name']. '">';
        }

        $html.='
        <section class="section">
            <div class="container-xl">
                <div class="mb-5">
                    <a href="/events#item' . $row['item_id'] . '" class="btn btn-sm btn-primary link-return"><i class="fa-solid fa-angles-left me-2" aria-hidden="true"></i> Return to ALL EVENTS</a>
                </div>
                <div class="events-detail row justify-content-start align-items-start rounded overflow-hidden">
                    <div class="events-list-item-info col-12 col-lg-6 col-xl-7">
                        <h1 class="mb-4">
                            <small class="fs-16 text-uppercase fw-semibold">' . $item_date . '</small><br>
                            ' . $row['item_name'] . '
                        </h1>
                        ' . $row['location'] . '
                        ' . $row['public_content'] . '
                    </div>
                    <div class="events-list-item-image col-12 col-lg-6 col-xl-5 mt-4 mt-lg-0">
                        ' . $item_image . '
                    </div>
                </div>
                <div class="mt-5">
                    <a href="/events#item' . $row['item_id'] . '" class="btn btn-sm btn-primary link-return"><i class="fa-solid fa-angles-left me-2" aria-hidden="true"></i> Return to ALL EVENTS</a>
                </div>
            </div>
        </section>
        ';
        /** ITEM_FORMAT END **/

        return $html;
    }
    /**
     * @access public
     * @param  \Db   $obj (default: null)
     * @param  array  $row_array
     * @return string $html
     */
    public function render_list_footer ($row_array) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** LIST_FOOTER START **/
        $html.='
        </div>
        ';
        /** LIST_FOOTER END **/

        return $html;
    }
    /**
     * @access public
     * @param  \Db   $obj (default: null)
     * @param  array  $row_array
     * @return string $html
     */
    public function render_empty_msg ($row_array) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** EMPTY_MSG START **/
        $html.="<p>*** No Items ***</p>";
        /** EMPTY_MSG END **/

        return $html;
    }

    // @see abstract class
    // function build_SQL_fragment_paging_format 

    // @see abstract class
    // function build_SQL_fragment_detail_format 

    // @see abstract class
    // function build_SQL_fragment_error_msg 
}