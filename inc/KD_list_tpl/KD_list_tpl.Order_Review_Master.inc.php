<?php
/**
 * File: /home/<USER>/public_html/inc/KD_list_tpl/KD_list_tpl.Order_Review_Master.inc.php
 *
 * saved with ListTemplateCodeHandler::generate_list_template_code()
 *
 */

Namespace ListTemplate\Template;

//require_once('../lib/AbstractListTemplate.php');

/**
 * Class cust_list_tpl_Order_Review_Master
 *
 * @extends     \ListTemplate\AbstractListTemplate
 * @implements  \ListTemplate\ListTemplateInterface
 *
 */
class cust_list_tpl_Order_Review_Master
    extends     \ListTemplate\AbstractListTemplate
{

    /**
     * $KDlist_name property.
     *
     * @var     string
     * @access  protected
     */
    /** VIEW_LIST_NAME START **/
    protected $KDlist_name = 'KD_Orders';
    /** VIEW_LIST_NAME END **/

    // @see abstract class
    // var $KDlist_id

    // @see abstract class
    // var $KDlist_paging

    // @see abstract class
    // var $KDlist_paging_at_start

    // @see abstract class
    // var $KDlist_paging_at_end

    // @see abstract class
    // var $KDlist_quickedit

    // @see abstract class
    // var $KDlist_userlimit

    // @see abstract class
    // var $KDlist_treeview

    // @see abstract class
    // var $KDlist_debugging

    // @see abstract class
    // function build_SQL_fragment_view_fields

    /**
     * build_SQL_fragment_table_name function.
     *
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_table_name () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();

        /** TABLE_NAME START **/
		$sql_fragment="KD_Orders";
		/** TABLE_NAME END **/

        return $sql_fragment;
    }

    /**
     * build_SQL_fragment_view_where function.
     *
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_view_where () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();

        /** VIEW_WHERE START **/
        if($_SESSION['checkout']['KD_Orders_item_id']) {
            $sql_fragment="item_id='" .  (int)$_SESSION['checkout']['KD_Orders_item_id'] . "' ";
        } else {
            $sql_fragment="item_id='" .  (int)$_SESSION['last_order_id'] . "' ";
        }
        if($obj->db_user_row['item_id']) {
            $sql_fragment .= " AND KD_user_item_id='" . (int)$obj->db_user_row['item_id'] . "'";
        }
		/** VIEW_WHERE END **/

        return $sql_fragment;
    }

    // @see abstract class
    // function build_SQL_fragment_view_group_by

    // @see abstract class
    // function build_SQL_fragment_view_having

    // @see abstract class
    // function build_SQL_fragment_view_sort

    /**
     * build_SQL_fragment_view_limit function.
     *
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_view_limit () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();

        /** VIEW_LIMIT START **/
		$sql_fragment="1";
		/** VIEW_LIMIT END **/

        return $sql_fragment;
    }

    // @see abstract class
    // function build_SQL_fragment_search_form

    // @see abstract class
    // function build_SQL_fragment_list_header

    /**
     * render_item_format function.
     *
     * @access public
     * @param  array  $row_array
     * @param  int    $item_count the current list item
     * @return string $html
     */
    public function render_item_format($row_array, $item_count) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();

        $row = $row_array[$item_count];
        $detail_link = $_SERVER['REQUEST_URI'] .  ((false !== strpos($_SERVER['REQUEST_URI'],'?'))? '&':'?') . $KDlist_id . '=' . $row['item_id'] . addGetVars('&',array("$KDlist_id"));
        $detail_back_link = ((isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'])?$_SERVER['HTTP_REFERER']:'');


        /** ITEM_FORMAT START **/
		$html ="<h2>Order# " . $row['item_id'] . "</h2>

<div class=\"row section\">
    <div class=\"col-sm-6\">
        <h2>Billing Info</h2>
        <p class=\"customer-data\">
            <span class=\"customer-data-label\">First Name:</span> " . $row['first_name_bill'] . "<br>
            <span class=\"customer-data-label\">Last Name:</span> " . $row['last_name_bill'] . "<br>
            <span class=\"customer-data-label\">Address:</span> " . $row['address1_bill'] . "<br>
            <span class=\"customer-data-label\">Address2:</span> " . $row['address2_bill'] . "<br>
            <span class=\"customer-data-label\">City:</span> " . $row['city_bill'] . "<br>
            <span class=\"customer-data-label\">State:</span> " . $row['state_bill'] . "<br>
            <span class=\"customer-data-label\">Zip:</span> " . $row['zip_bill'] . "</p>
    </div>
    <div class=\"col-sm-6\">
        <h2>Shipping Info</h2>
        <p class=\"customer-data\">
            <span class=\"customer-data-label\">First Name:</span> " . $row['first_name_ship'] . "<br>
            <span class=\"customer-data-label\">Last Name:</span> " . $row['last_name_ship'] . "<br>
            <span class=\"customer-data-label\">Address:</span> " . $row['address1_ship'] . "<br>
            <span class=\"customer-data-label\">Address2:</span> " . $row['address2_ship'] . "<br>
            <span class=\"customer-data-label\">City:</span> " . $row['city_ship'] . "<br>
            <span class=\"customer-data-label\">State:</span> " . $row['state_ship'] . "<br>
            <span class=\"customer-data-label\">Zip:</span> " . $row['zip_ship'] . "</p>
    </div>
</div>

<div class=\"section\">
    <div class=\"table-responsive\">
        <table cellpadding=\"0\" cellspacing=\"0\" class=\"table cart-table\">
            <thead>
                <tr>
                    <th>Product</th>
                    <th>Quantity</th>
                    <th>Price each</th>
                    <th>Sub-total</th>
                </tr>
            </thead>
            " .  $obj->Show_List('list_Order_Review_Detail.',array('view_where'=>"KD_Orders_item_id='" . (int)$row['item_id'] . "'")) . "
            <tr>
                <td colspan=\"2\">&nbsp;</td>
                <th>Subtotal:</th>
                <td>" .  format_money($row['cart_total'],true,true). "</td>
            </tr>
            " .  (($row['shipping_charge']) ? '
            <tr>
                <td colspan=\"2\" style=\"border: none;\">&nbsp;</td>
                <td style=\"border: none;\">Shipping:</td>
                <td style=\"border: none;\">' . format_money($row['shipping_charge']) . '</td>
            </tr>
            ' : '') . "
            " .  (($row['tax']) ? '
            <tr>
                <td colspan=\"2\" style=\"border: none;\">&nbsp;</td>
                <td style=\"border: none;\">Tax:</td>
                <td style=\"border: none;\">' . format_money($row['tax']) . '</td>
            </tr>
            ' : '') . "
            " .  (($row['promo_discount']) ? '
            <tr>
                <td colspan=\"2\" style=\"border: none;\">&nbsp;</td>
                <td style=\"border: none;\">Promo Credit:</td>
                <td style=\"border: none;\">- ' . format_money($row['promo_discount']) . '</td>
            </tr>
            ' : '') . "
            <tr>
                <td colspan=\"2\">&nbsp;</td>
                <th>Total:</th>
                <td><strong>" .  format_money($row['grand_total'],true,true) . "</strong></td>
            </tr>
        </table>
    </div>
</div>

";
		/** ITEM_FORMAT END **/

        return $html;
    }

    // @see abstract class
    // function build_SQL_fragment_list_footer

    // @see abstract class
    // function build_SQL_fragment_empty_msg

    // @see abstract class
    // function build_SQL_fragment_paging_format

    // @see abstract class
    // function build_SQL_fragment_detail_format

    // @see abstract class
    // function build_SQL_fragment_error_msg
}