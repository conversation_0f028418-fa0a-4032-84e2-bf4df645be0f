<?php
/**
 * File: /home/<USER>/public_html/inc/KD_list_tpl/KD_list_tpl.C_staff_team_groups_nav.inc.php
 *
 * saved with ListTemplateCodeHandler::generate_list_template_code()
 *
 */
 
Namespace ListTemplate\Template;

//require_once('../lib/AbstractListTemplate.php');

/**
 * Class cust_list_tpl_C_staff_team_groups_nav
 *
 * @extends     \ListTemplate\AbstractListTemplate
 * @implements  \ListTemplate\ListTemplateInterface
 *
 */
class cust_list_tpl_C_staff_team_groups_nav 
    extends     \ListTemplate\AbstractListTemplate  
{

    /**
     * $KDlist_name property.
     * 
     * @var     string
     * @access  protected
     */
    /** VIEW_LIST_NAME START **/
    protected $KDlist_name = 'C_staff_team_groups';
    /** VIEW_LIST_NAME END **/

    // @see abstract class
    // var $KDlist_id 

    // @see abstract class
    // var $KDlist_paging 

    // @see abstract class
    // var $KDlist_paging_at_start 

    // @see abstract class
    // var $KDlist_paging_at_end 

    // @see abstract class
    // var $KDlist_quickedit 

    // @see abstract class
    // var $KDlist_userlimit 

    // @see abstract class
    // var $KDlist_treeview 

    // @see abstract class
    // var $KDlist_debugging 

    // @see abstract class
    // function build_SQL_fragment_view_fields 

    /**
     * build_SQL_fragment_table_name function.
     * 
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_table_name () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** TABLE_NAME START **/
        $sql_fragment="C_staff_team_groups";
        /** TABLE_NAME END **/

        return $sql_fragment;
    }

    /**
     * build_SQL_fragment_view_where function.
     * 
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_view_where () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** VIEW_WHERE START **/
        $sql_fragment="item_active=1";
        /** VIEW_WHERE END **/

        return $sql_fragment;
    }

    // @see abstract class
    // function build_SQL_fragment_view_group_by 

    // @see abstract class
    // function build_SQL_fragment_view_having 

    /**
     * build_SQL_fragment_view_sort function.
     * 
     * @access public
     * @return string $sql_fragment
     */
    public function build_SQL_fragment_view_sort () {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** VIEW_SORT START **/
        $sql_fragment="list_sort";
        /** VIEW_SORT END **/

        return $sql_fragment;
    }

    // @see abstract class
    // function build_SQL_fragment_view_limit 

    // @see abstract class
    // function build_SQL_fragment_search_form 
    /**
     * @access public
     * @param  \Db   $obj (default: null)
     * @param  array  $row_array
     * @return string $html
     */
    public function render_list_header ($row_array) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** LIST_HEADER START **/
        $html.='
        <div class="staff-group-nav mb-5 pb-5">
            <p>Jump to a team category:</p>
            <div class="row flex-wrap justify-content-start align-items-start">
        ';
        /** LIST_HEADER END **/

        return $html;
    }

    /**
     * render_item_format function.
     * 
     * @access public
     * @param  array  $row_array
     * @param  int    $item_count the current list item
     * @return string $html
     */
    public function render_item_format($row_array, $item_count) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        $row = $row_array[$item_count];
        $detail_link = $_SERVER['REQUEST_URI'] .  ((false !== strpos($_SERVER['REQUEST_URI'],'?'))? '&':'?') . $KDlist_id . '=' . $row['item_id'] . addGetVars('&',array("$KDlist_id"));
        $detail_back_link = ((isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'])?$_SERVER['HTTP_REFERER']:'');

        
        /** ITEM_FORMAT START **/
        $html.='
        <a href="#staff_group_' . $row['item_id'] . '" class="col-12 col-sm-6 col-lg-4 col-xxl-3">' . $row['item_name'] . '</a>
        ';
        /** ITEM_FORMAT END **/

        return $html;
    }
    /**
     * @access public
     * @param  \Db   $obj (default: null)
     * @param  array  $row_array
     * @return string $html
     */
    public function render_list_footer ($row_array) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** LIST_FOOTER START **/
        $html.='
            </div>
        </div>
        ';
        /** LIST_FOOTER END **/

        return $html;
    }
    /**
     * @access public
     * @param  \Db   $obj (default: null)
     * @param  array  $row_array
     * @return string $html
     */
    public function render_empty_msg ($row_array) {
        list($obj, $sql_fragment, $KDlist_id, $KDlist_alt_info) = $this->internal_class_vars();
        
        /** EMPTY_MSG START **/
        $html.="<p>*** No Items ***</p>";
        /** EMPTY_MSG END **/

        return $html;
    }

    // @see abstract class
    // function build_SQL_fragment_paging_format 

    // @see abstract class
    // function build_SQL_fragment_detail_format 

    // @see abstract class
    // function build_SQL_fragment_error_msg 
}