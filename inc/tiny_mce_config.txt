<script type="text/javascript" src="/tinymce/tinymce.min.js"></script>
<script type="text/javascript">
$(document).ready(function(){

    // NOTE: we have moved this to a function, so we can call it more than once (if new WYSIWYG elements are added to the page, etc.)
    tinymce_init();

});

tinymce_init = function() {
    
    //console.log('tinymce_init() textareas: ' + $("textarea.mceEditor:not(.mceInitialized)").length );
	$("textarea.mceEditor:not(.mceInitialized)").each(function(){
    	if( !$(this).attr("id") ) {
        	$(this).attr("id", uniqueid() );
    	}
	    $(this).addClass("mceInitStart");
	    //console.log('id added, initStart added ' + $(this).attr("id") );
    });

	// set base URL
	var url, url_arr, url_base;
	if( $("base").length ) {
		url_base = $("base").attr("href");
	} else {
		url = window.location.href;
		url_arr = url.split("/");
		url_base = url_arr[0] + "//" + url_arr[2];
	}

    tinymce.init({
	    
	    setup: function (editor) {
	        editor.on('change', function () {
//		        console.log('changed TMCE');
	            editor.save();
	        });
	    },
	    init_instance_callback : function(editor) {
    	    $("#"+editor.id).removeClass("mceInitStart").addClass("mceInitialized");
            console.log("Editor: " + editor.id + " is now mceInitialized.");
        },
    
        // use absolute URLs for internal links and images
        relative_urls: false,
        remove_script_host: false,
        document_base_url: url_base,
        // set content_css and body_id in order to pull in front-end styles as needed
        content_css : [
            "/assets_2025/css/public.css?" + new Date().getTime(),
            "/assets_2025/css/tinymce.css?" + new Date().getTime()
        ],
        body_id: "editorcontent",
        selector: "textarea.mceEditor:not(.mceInitialized)",
        plugins : [
            "advlist autolink lists link image charmap print preview anchor",
            "searchreplace visualblocks code fullscreen table",
            "insertdatetime media table contextmenu paste"
        ],
        extended_valid_elements: "span[class|style|title|aria-label|arial-hidden|aria-labeledby]",
        table_responsive_width: true,
        table_class_list: [
            {title: 'None', value: ''},
            {title: 'Styled Table', value: 'table'},
            {title: 'Striped Table', value: 'table table-striped'},
            {title: 'Bordered Table', value: 'table table-bordered'},
            {title: 'Borderless Table', value: 'table table-borderless'},
            {title: 'Compact Styled Table', value: 'table table-sm'},
            {title: 'Compact Striped Table', value: 'table table-sm table-striped'},
            {title: 'Compact Bordered Table', value: 'table table-sm table-bordered'},
            {title: 'Compact Borderless Table', value: 'table table-sm table-borderless'},
        ],
        table_cell_class_list: [
            {title: 'None', value: ''},
            {title: 'Colored Header Cell', value: 'bg-gray-200'}
        ],
        style_formats: [
            {title: 'Headers', items: [
                {title: 'Header 1', block : 'h1'},
                {title: 'Header 2', block : 'h2'},
                {title: 'Header 3', block : 'h3'},
                {title: 'Header 4', block : 'h4'},
                {title: 'Header 5', block : 'h5'},
                {title: 'Header 6', block : 'h6'},
            ]},
            {title: 'Blocks', items: [
                {title: 'Paragraph', block : 'p'},
                {title: 'Div', block : 'div'},
                {title: 'Block Right', block: 'div', wrapper: true, classes: 'float-lg-end block-set'},
                {title: 'Block Left', block: 'div', wrapper: true, classes: 'float-lg-start block-set'}
            ]},
            {title: 'Text Styles', items: [
                {title: 'Text Uppercase',   inline: 'span', classes: 'text-uppercase'    },
                {title: 'Text Small',       inline: 'span', classes: 'small'        },
                {title: 'Text Muted',       inline: 'span', classes: 'text-muted'   },
                {title: 'Text Blue',        inline: 'span', classes: 'text-primary'   },
                {title: 'Text Red',         inline: 'span', classes: 'text-secondary'   },
            ]},
            {title: 'Buttons/Links', items: [
                {title: 'Button, Blue',             selector: 'a', classes: 'btn btn-primary'               },
                {title: 'Button, Red',              selector: 'a', classes: 'btn btn-secondary'             },
                {title: 'Button, Outline Blue',     selector: 'a', classes: 'btn btn-outline-primary'       },
                {title: 'Button, Outline Red',      selector: 'a', classes: 'btn btn-outline-secondary'     },
            ]}
        ],
        image_advtab : true,
        image_dimensions: false,
        image_class_list: [
            {title: 'Default (responsive)', value: 'img-fluid'},
            {title: 'Float Right', value: 'img-fluid img-float float-lg-end'},
            {title: 'Float-Left', value: 'img-fluid img-float float-lg-start'}
        ],
        paste_word_valid_elements: "b,strong,i,em,h1,h2,h3,h4,ol,li,p",
        menubar: "edit insert view format table tools",
        toolbar1 : "undo redo | bold italic | bullist numlist | indent outdent | alignleft aligncenter alignright alignjustify | link unlink | image | table | styleselect removeformat pastetext ",
        file_picker_callback: elFinderBrowser
    });
    
}


// Generate unique IDs for use as pseudo-private/protected names.
// Similar in concept to
// <http://wiki.ecmascript.org/doku.php?id=strawman:names>.
//
// The goals of this function are twofold:
// 
// * Provide a way to generate a string guaranteed to be unique when compared
//   to other strings generated by this function.
// * Make the string complex enough that it is highly unlikely to be
//   accidentally duplicated by hand (this is key if you're using `ID`
//   as a private/protected name on an object).
//
// Use:
//
//     var privateName = ID();
//     var o = { 'public': 'foo' };
//     o[privateName] = 'bar';
var uniqueid = function () {
  // Math.random should be unique because of its seeding algorithm.
  // Convert it to base 36 (numbers + letters), and grab the first 9 characters
  // after the decimal.
  return '_' + Math.random().toString(36).substr(2, 9);
};

//replace moxiemanager by elfinder-solution [FX] 2022-09-09
function elFinderBrowser (callback, value, meta) {
    //console.log('elFinderBrowser triggered - inc/tiny_mce_config.txt');
    tinymce.activeEditor.windowManager.open({
        file: '/elfinder/elfinder.html',// use an absolute path!
        title: 'elFinder ',// elFinder V2.1
        width: 900,
        height: 450,
        resizable: 'yes'
    }, {
        oninsert: function (file, fm) {
            var url, reg, info;

            // URL normalization
            url = fm.convAbsUrl(file.url);

            // Make file info
            info = file.name + ' (' + fm.formatSize(file.size) + ')';

            // Provide file and text for the link dialog
            if (meta.filetype == 'file') {
                callback(url, {text: info, title: info});
            }

            // Provide image and alt text for the image dialog
            if (meta.filetype == 'image') {
                callback(url, {alt: info});
            }

            // Provide alternative source and posted for the media dialog
            if (meta.filetype == 'media') {
                callback(url);
            }
        }
    });
    return false;
}

</script>
