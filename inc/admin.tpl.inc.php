<?php
/**
 * File: admin.tpl.inc.php
 *
 *
 */
header("X-Frame-Options: DENY");
?><!DOCTYPE html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>         <html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en"> <!--<![endif]-->
<head>
<meta charset="utf-8">
<meta name="robots" content="noindex">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title><?php echo (($this->db_page_row['head_title'])?$this->db_page_row['head_title']:$this->db_page_row['menu_display']) . ' &middot; ' . $this->End_User_URL . ' &laquo;&raquo; Dialogs Framework'; ?></title>

<!--
<link rel="stylesheet" href="/cssD/bootstrap.css">
-->
<link rel="stylesheet" href="/cssD/admin.css">
<style>
html {
    font-size: initial;
}
textarea.form-control {
    height: 500px;
}

</style>
<link rel="stylesheet" href="/cssD/jasny-bootstrap.css">
<!--
<link rel="stylesheet" href="/cssD/jasny-bootstrap.min.css">
-->
<?php
if ($this->favicon_backend) {echo '<link rel="shortcut icon" href="'.$this->favicon_backend.'">' . "\n"; }
?>

<script src="/jsD/vendor/modernizr-2.6.2-respond-1.1.0.min.js"></script>

<script src="/jsD/vendor/jquery.js"></script>

<script src="/jsD/vendor/bootstrap.js"></script>
<script src="/jsD/vendor/jasny-bootstrap.min.js"></script>
<script src="/jsD/vendor/parsley.js"></script>
<script src="/jsD/admin.js"></script>

<script src="/jsD/admin-link-selector.js"></script>
<link rel="stylesheet" href="/cssD/admin-link-selector.css">

<script src="/jsD/admin_nav_search.js"></script>
<!-- Add a search to the <select> element -->
<script src="/js/vendor/bootstrap-select.min.js"></script>
<link href="/css/bootstrap-select.min.css" rel="stylesheet" />
<script>
$(document).ready(function () {
    $('.selectpicker').selectpicker();
});
</script>

<?php
echo $this->db_page_row['insert_head'];
echo $this->inspector_head();
?>
    <script>
        <?php echo create_csrf_token_for_js();  ?>
    </script>

</head>
<body>
<!--[if lt IE 7]>
    <p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/" rel="noopener">upgrade your browser</a> to improve your experience.</p>
<![endif]-->

<!-- #SITE_HEADER -->
<header role="banner" class="navbar navbar-default navbar-static-top" id="site_header">
    <div class="container-fluid">
        <div class="navbar-header">
            <a href="#site_main" class="skip">Skip to main content</a>
            <a class="navbar-brand" href="/a/"><img src="/imgD/skin_logo_dialogs_header.png" border="0" alt="Dialogs Framework"></a>
            <button type="button" class="btn navbar-toggle toggle-sidebar" data-placement="left" data-disablescrolling="true" data-target="#site_nav" data-toggle="offcanvas">
                Menu
                <span class="glyphicon glyphicon-menu-hamburger"></span>
            </button>
        </div>
        <div class="collapse navbar-collapse" id="nav_user">
            <nav role="navigation" aria-label="User" class="navbar-right usernav-header">
                <ul class="nav navbar-nav">
                    <li><a class="client-brand" href="<?php
                    if($this->SSL_enabled) {
                        echo 'https://';
                    } else {
                        echo 'http://';
                    }
                    echo str_replace('http://','',$this->End_User_URL);
                    ?>" title="Go to <?php echo $this->End_User_URL; ?>" target="_blank" rel="noopener"><img src="<?php echo $this->image_logo_backend; ?>" border="0" alt="<?php echo $this->End_User_Name; ?>"></a></li>
                    <?php
                    if($this->authenticated) {
                        echo $this->Show_List('list_Favorites_bar_bootstrap.',array());
                        echo '
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><span class="glyphicon glyphicon-cog"></span> Settings for ' . $this->db_user_row['display_name'] . ' <b class="caret"></b></a>
                            <ul class="dropdown-menu">
                                <li><a href="/a/KDpg_user_prefs.html">User Settings</a></li>
                                ' . (($this->db_user_row['user_groups'] & 4) ? '<li><a href="/a/KDpg_Edit_Settings.html">Site Settings</a></li>' : '') . '
                                <li><a href="/a/KDpg_logout.html" title="Logout">Logout</a></li>
                                <li class="divider" role="separator"></li>
                                <li><span class="menu-text"><em>Auto-Logout ' . $this->session_expire_time . '</em></span></li>
                            </ul>
                        </li>
                        ';
                    } else {
                        echo '<li><a href="/a/KDpg_login.html">Login</a></li>';
                    }
                    ?>
                </ul>
            </nav>
        </div><!--/.navbar-collapse -->
    </div>
</header>
<!-- /#SITE_HEADER -->

<!-- #SITE_MAIN -->
<main role="main" class="container-fluid" id="site_main">
    <div class="row">

        <!-- #SITE_NAV left column -->
        <div class="offcanvas-xs col-sm-3 col-lg-2 navbar-offcanvas" id="site_nav">
           <!--commented by fx 20170403 for LD: 14830965 dashboard only show the Pages-->
<?php
                echo '<p class="form-group nav-search">
                <input type="text" class="form-control input-sm" id="admin_nav_search" placeholder="Search Navigation">
            </p>' . "\n";
?>
            <nav role="navigation" aria-label="User" class="panel panel-default usernav-sidebar">
                <ul class="nav">
                    <li><a class="client-brand" href="<?php
                    if($this->SSL_enabled) {
                        echo 'https://';
                    } else {
                        echo 'http://';
                    }
                    echo str_replace('http://','',$this->End_User_URL);
                    ?>" title="Go to <?php echo $this->End_User_URL; ?>" target="_blank"><img src="<?php echo $this->image_logo_backend; ?>" border="0" alt="<?php echo $this->End_User_Name; ?>" class="img-responsive"></a></li>
                    <?php
                    if($this->authenticated) {
                        echo $this->Show_List('list_Favorites_bar_bootstrap_mobile.',array());
                        echo '
                        <li>
                            <a href="#settings_menu" class="collapsed" data-toggle="collapse" aria-expanded="false" aria-controls="settings_menu"><span class="glyphicon glyphicon-cog"></span> Settings for ' . $this->db_user_row['display_name'] . ' <b class="caret"></b></a>
                            <ul id="settings_menu" class="collapse">
                                <li><a href="/a/KDpg_user_prefs.html">User Settings</a></li>
                                <li><a href="/a/KDpg_Edit_Settings.html">Site Settings</a></li>
                                <li><a href="/a/KDpg_logout.html" title="Logout">Logout</a></li>
                                <li class="divider" role="separator"></li>
                                <li><span class="menu-text"><em>Auto-Logout ' . $this->session_expire_time . '</em></span></li>
                            </ul>
                        </li>
                        ';
                    } else {
                        echo '<li><a href="/a/KDpg_login.html">Login</a></li>';
                    }
                    ?>
                </ul>
            </nav>



            <?php
            echo '<!-- NAV -->';
            echo $this->Show_List('list_KD_nav_sidebar.');
            ?>

            <?php
            if(isset($this->db_user_row['item_name']) && $this->db_user_row['item_name']) {
            ?>

                <div class="panel panel-default dialogs-feed"></div>

                <div class="panel panel-default sidebar-extras">
                    <div class="list-group">
                        <div class="list-group-item">Auto Logout <?php echo $this->session_expire_time; ?></div>
                        <?php
                        if(function_exists('sys_getloadavg')) {
                            $load_average = sys_getloadavg();
                            foreach($load_average as $i=>$one_number) {
                                $load_average[$i] = round($one_number,2);
                            }
                            echo '<div class="list-group-item"><a href="http://en.wikipedia.org/wiki/Load_%28computing%29" target="_blank" rel="noopener">LoadAvg</a> : ' .
                            (($load_average[0] >= 1.0)?
                                '<span style="color:red;">' . implode(' ',$load_average) . '</span>'
                                :implode(' ',$load_average)
                                ) . ' ' ;
                            echo '</div>';
                        }
                        if($this->db_user_row['user_groups'] & 4){
                          echo '<div class="list-group-item">Debug Tools &nbsp;
                            <span id="debug_toggle" class="'.getDebugToggleState($this).'">
                              <span class="toggle-switch"></span>
                            </span><br />
                            <small class="debug-toggle-description '.getDebugToggleState($this).'">All log_message() calls will be logged<br />
                            <a href="/a/KDpg_Session_Debug.html">Session Debug</a> available</small>
                          </div>';
                        }
                        ?>
                    </div>
                </div>
            <?php
            }
            ?>
        </div>
        <!-- /#SITE_NAV left column -->

        <!-- #SITE_CONTENT right column -->
        <div class="col-xs-12 col-sm-9 col-lg-10" id="site_content">
            <?php
            if ($this->db_page_row['header']) {
                echo ('<h1>' . $this->db_page_row['header'] . '</h1>' . "\n");
            }
            echo $this->db_page_row['body'];
            ?>
        </div>

        <!-- /#SITE_CONTENT right column -->

    </div>
</main>
<!-- /#SITE_MAIN -->

<!-- #SITE_FOOTER -->
<footer role="contentinfo" class="container-fluid" id="site_footer">
    <div class="row footer-legal">
        <div class="col-xs-12">
            <p>
            <?php echo $this->inspector_link('Powered by Dialogs') . ' v' . $this->version . '.'; ?>
            <br>
            &copy; <?php echo (date("Y")); ?>
            Dialogs Apps, Inc. All rights reserved. Dialogs and Dialogs Framework are trademarks of Dialogs Apps, Inc.
            </p>
        </div>
    </div>
</footer>
<!-- /#SITE_FOOTER -->


<script src="/jsD/vendor/jquery.numberedtextarea.js"></script>

<?php
echo $this->db_page_row['insert_body'];
?>

<?php
if($_GET['lid'] && $_GET['item_id']) {
    $table_name = $this->getFieldContents('KD_list','item_id',(int)$_GET['lid'],'item_table');
    echo '<p><a href="/a/DBX_Edit_Row.html?action=view&table_name=' . $table_name . '&item_id=' . (int)$_GET['item_id']. '" target="_blank">DBX</a></p>' . "\n";
}
?>
<!-- call bootstrap toasts from js like this: toast('Hi! this will disappear in .5 seconds!','Hi',500); -->
<div id="toastmodal" class="modal" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title"></h4>
            </div>
            <div class="modal-body"></div>
        </div>
    </div>
</div>

</body>
</html>
