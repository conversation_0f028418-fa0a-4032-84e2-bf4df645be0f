<?php
/**
 * register.inc.php
 * @var page $this
 */

// Process registration form submission
if (isset($_POST['frm_email']) && isset($_POST['frm_team_number'])) {
    // Sanitize email input
    $_POST['frm_email'] = filter_var($_POST['frm_email'], FILTER_SANITIZE_EMAIL);
    // Sanitize team number input
    $_POST['frm_team_number'] = preg_replace('/[^0-9]/', '', $_POST['frm_team_number']);
    
    // Validate email
    if (!filter_var($_POST['frm_email'], FILTER_VALIDATE_EMAIL)) {
        $this->db_page_row ['body'] .= '
        <div class="alert alert-danger">
            <span class="fw-bold">Invalid Email!</span> Please enter a valid email address.
        </div>
        ';
    }
    // Validate team number
    else if (empty($_POST['frm_team_number'])) {
        $this->db_page_row ['body'] .= '
        <div class="alert alert-danger">
            <span class="fw-bold">Invalid Team Number!</span> Please enter a valid team number.
        </div>
        ';
    }
    else {

        $email = $_POST['frm_email'];
        $team_number = $_POST['frm_team_number'];

        // Check if there's a KD_user row with that email
        $user_row = $this->getOneRowSQL("SELECT * FROM KD_user WHERE user_email = '" . addslashes($email) . "' LIMIT 1;");
        if ($user_row) {
            $this->db_page_row ['body'] .= '
            <div class="alert alert-info">
                <span class="fw-bold">Email already registered.</span> Please use a different email address or login with your existing account.
            </div>
            ';
        } else {

            // Check if there is a team row with that team_number
            $team_row = $this->getOneRowSQL("SELECT * FROM C_team WHERE team_number = '" . addslashes($team_number) . "' LIMIT 1;");

            $email_match = false;
            if ($team_row) {
                if (($team_row['lead_coach1_email'] && strcasecmp($team_row['lead_coach1_email'], $email) === 0) ||
                    ($team_row['lead_coach1_alt_email'] && strcasecmp($team_row['lead_coach1_alt_email'], $email) === 0) ||
                    ($team_row['lead_coach2_email'] && strcasecmp($team_row['lead_coach2_email'], $email) === 0) ||
                    ($team_row['lead_coach2_alt_email'] && strcasecmp($team_row['lead_coach2_alt_email'], $email) === 0) ||
                    ($team_row['admin_email'] && strcasecmp($team_row['admin_email'], $email) === 0)) {
                    $email_match = true;
                }
            }

            if ($team_row && $email_match) {

                // User creation
                $user_row = create_KD_user_for_team($this, $email);
                if(!$user_row) {
                    $this->db_page_row ['body'] .= '
                    <div class="alert alert-danger">
                        Failed to create KD_user for ' . $email . '.
                    </div>
                    ';
                }


                // Create user team rows
                createUserTeamRowsForUser($this, $user_row);

                // Send email to user
                sendAccountCreationEmailToUser($this, $user_row);

                $success_message = '<div class="alert alert-success">
                    <span class="fw-bold">Registration Successful!</span> Your account has been created. Please check your email for further instructions.
                    </div>';
                $this->log_message('New KD_user created - Email: ' . $email . ', Team: ' . $team_number . ' from ' . $_SERVER['REMOTE_ADDR'], 'R');
            } else {
                $this->db_page_row ['body'] .= '
                <div class="alert alert-danger">
                    We couldn\'t find a team number associated with that email address.
                </div>
                ';
            }
        }
    }
}

$form_body = '
<div class="container-max-sm mt-5">
    <h2 class="mb-4">Create Your User Account at <em>FIRST</em> In Texas</h2>
    <form name="frm_register" action="" method="post" role="form" class="mt-4">
        <div class="mb-3">
            <label for="frm_emailID" class="form-label">Email</label>
            <input type="email" name="frm_email" id="frm_emailID" value="' . ((isset($_POST['frm_email']) && $_POST['frm_email'])?htmlspecialchars( $_POST['frm_email'], ENT_COMPAT, 'UTF-8'):'') . '" class="form-control" required>
        </div>
        <div class="mb-3">
            <label for="frm_team_numberID" class="form-label">Team Number</label>
            <input type="number" name="frm_team_number" id="frm_team_numberID" value="' . ((isset($_POST['frm_team_number']) && $_POST['frm_team_number'])?htmlspecialchars( $_POST['frm_team_number'], ENT_COMPAT, 'UTF-8'):'') . '" class="form-control" required>
        </div>
        <div class="d-flex flex-column gap-3 flex-sm-row justify-content-between align-items-center mt-4">
            <input type="submit" name="btnRegister" value="Submit" class="btn btn-primary pull-left">
            <p class="mt-4">Already have an account? <a href="/login">Login</a></p>
        </div>
        ' . create_csrf_token() . '
    </form>
</div>
';
            
if (isset($success_message) && $success_message) {
    $form_body = $success_message;
}

$this->db_page_row ['body'] .= '
<div class="mt-5">
    ' . $form_body . '
</div>
';
