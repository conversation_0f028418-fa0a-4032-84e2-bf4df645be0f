<?php
/**
 * login.inc.php
 * @var page $this
 */

try {
    // Skip showing an SSL error because ssl is on
    if(isset($_SERVER['HTTPS']) && strtolower($_SERVER ['HTTPS']) == 'on') {
        throw new Exception("No need to redirect", __LINE__);
    }

    // Skip showing an SSL error because the setting is off
    if(!isset($this->SSL_enabled) || !$this->SSL_enabled) {
        throw new Exception("No need to redirect", __LINE__);
    }

    // Avoid infinite redirect loops where HTTP_REFERER = SCRIPT_URI
    if(!isset($_SERVER['HTTP_REFERER']) || !$_SERVER['HTTP_REFERER']) {
        throw new Exception("Cannot redirect", __LINE__);
    }
    if(!isset($_SERVER['SCRIPT_URI']) || !$_SERVER['SCRIPT_URI']) {
        throw new Exception("Cannot redirect", __LINE__);
    }
    if(isset($_SERVER['HTTP_REFERER'])
        && $_SERVER['HTTP_REFERER']
        && isset($_SERVER['SCRIPT_URI'])
        && $_SERVER['SCRIPT_URI']
        && $_SERVER['HTTP_REFERER'] != $_SERVER['SCRIPT_URI']) {
        throw new Exception("Cannot redirect", __LINE__);
    }
    if(isset($_SERVER['HTTP_REFERER']) && false !== strpos($_SERVER ['HTTP_REFERER'], 'KDpg_error.html')) {
        throw new Exception("Cannot redirect", __LINE__);
    }

    // Redirect with a SSL message
    $_SESSION ['KD_error_autosend'] = true;
    $_SESSION ['KD_error_autosend_label'] = 'The SSL login page';
    $SSL_location = 'https://' . substr ( $_SERVER ['SCRIPT_URI'], 7 );
    $_SESSION ['error_returnto'] = $SSL_location;
    $_SESSION ['KD_error'] = '<div class="alert alert-warning">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
            <p>You have attempted to access a non-<a href="http://en.wikipedia.org/wiki/Ssl" class="alert-link">SSL</a> login page. If this fails because the site does not have SSL available you may login without it (although it is not recommended):</p> 
            <p><a href="' . $_SERVER ['SCRIPT_URI'] . '" class="btn btn-warning">Insecure Login</a></p>
            </div>';
    header ( 'Location: /a/KDpg_error.html' );
    exit ();
} catch (Exception $e) {
    // do nothing
}


// Process a regular login.
if (isset($_POST['frm_login_name']) && isset($_POST['frm_password'])) {
    $_POST['frm_login_name'] = substr(preg_replace('/[^-a-zA-Z0-9_+.@]/','',$_POST['frm_login_name']),0,128);
    $ret = $this->authenticate($_POST ['frm_login_name'],$_POST['frm_password'],((isset($_POST['yubikey_otp']) && $_POST['yubikey_otp'])?$_POST['yubikey_otp']:''));
    if ($ret) {

        $this->log_message ( 'User ' . $this->db_user_row ['item_name'] . ' ' . $this->db_user_row ['item_id'] . ' Logged in ' . $_SERVER ['REMOTE_ADDR'], 'L' );

        // set last_login in KD_user
        $this->query("UPDATE KD_user SET last_login=NOW() WHERE item_id='".(int)$this->db_user_row ['item_id']."'");

        $this->begin_user_session();

    } else {
        $error_message = '<div class="alert alert-danger mt-4">
            <p class="mb-0">Wrong email or password. Please try again.</p> 
            </div>';
        $this->log_message ( "Invalid login for user " . $_POST ['frm_login_name'] . " from " . $_SERVER ['REMOTE_ADDR'] );
    }
}
// Email to reset a forgotten password.
if ((isset ( $_POST ['frm_login_name2'] )) && ($_POST ['frm_login_name2'])) {

    $KD_user_item_name = substr(preg_replace('/[^-a-zA-Z0-9_+.@]/','',$_POST['frm_login_name2']),0,128);
    $sent_count = 0;
    $sql = "SELECT * FROM KD_user WHERE item_active=1 AND (item_name='" . $this->real_escape_string($KD_user_item_name) . "' OR user_email='" . $this->real_escape_string($KD_user_item_name) . "')";
    $result = $this->query ( $sql );
    if ($result) {
        $num_accounts = $this->num_rows ( $result );
        if ($num_accounts == 0) {
            $this->db_page_row ['body'] .= "<div class='alert alert-danger'>
                <button type='button' class='close' data-dismiss='alert' aria-hidden='true'>&times;</button>
                <p>Sorry, no accounts found.</p>
                </div>";
        } else {
            while ( $user_row = $this->fetchAssoc ( $result ) ) {
                if ($user_row ['user_email']) {
                    $email_tpl = $this->getOneRowV2 ( 'KD_email_tpl', 'item_name', 'Lost_Password' );
                    if($email_tpl){
                        // customize the message
                        $tokens = array('$host'=>$_SERVER['HTTP_HOST'],'$item_name'=>$user_row['item_name'],'$display_name'=>$user_row['display_name'],'$user_email'=>$user_row['user_email']);
                        $fields_to_customize=array('Subject','Body');
                        foreach ($fields_to_customize as $one_field) {
                            foreach($tokens as $n=>$v) {
                                $email_tpl[$one_field] = str_replace($n,$v,$email_tpl[$one_field]);
                            }
                        }
                        $message = $email_tpl ['Body'];
                        $subject = $email_tpl ['Subject'];
                        //$to            = $email_tpl ['Mail_To'];
                        $from = $email_tpl ['Mail_From'];

                        if (!$from) {
                            $from = 'info@' . domain_name();
                        }

                        $lost_pw_link = pbkdf2('sha256', random_int(0,2147483647), random_int(0,2147483647), 1000, 32, false);// ($algorithm, $password, $salt, $count, $key_length(in bytes), $raw_output)
                        if($this->SSL_enabled){
                            $href = 'https://';
                        } else {
                            $href = 'http://';
                        }
                        $href .= $_SERVER ['HTTP_HOST'] . "/a/KDpg_Lost_Password.html?lost_pw_link=$lost_pw_link";
                        $this->query("UPDATE KD_user set modified=NOW(),lost_pw_link='$lost_pw_link' WHERE item_id=" . (int)$user_row ['item_id'] . " LIMIT 1" );
                        $message = str_replace ( '$href', $href, $message );

                        $ret = mail_wrapper($this
                            , $user_row['user_email']
                            , $subject
                            , $message
                            , $from);


                        $sent_count++;
                    } else {
                        $this->log_message('Missing email template: Lost_Password','E');
                    }
                }
            }
        }
    }
    if ($sent_count) {
        $this->db_page_row ['body'] .= "<div class='alert alert-success'>
            <button type='button' class='close' data-dismiss='alert' aria-hidden='true'>&times;</button>
            <p>A link has been sent to your email address which will allow you to reset your password.
            ".(($this->pw_reset_max_age)?"The link will only be active for {$this->pw_reset_max_age} minutes.":'')."
            </p>
            </div>";
    } else {
        $this->db_page_row ['body'] .= "<div class='alert alert-danger'>
            <button type='button' class='close' data-dismiss='alert' aria-hidden='true'>&times;</button>
            <p>Sorry, no email address found.</p>
            </div>";
    }
}

$this->db_page_row ['body'] .= '

<script type="text/javascript">
$(document).ready(function () {
  $frm_password = $("input[name=frm_password]")
  $visible_password = $("input[name=visible_password]")
    $frm_password.keyup(function(){
        $visible_password.val($frm_password.val());
    });
    $frm_password.change(function(){
        $visible_password.val($frm_password.val());
    });

    $visible_password.keyup(function(){
        $frm_password.val($visible_password.val());
    });
    $visible_password.change(function(){
        $frm_password.val($visible_password.val());
    });
    $("#show_pass").click(function(){
        if($("#show_pass").prop("checked")) {
            $visible_password.show();
            $frm_password.hide();
        } else {
            $visible_password.hide();
            $frm_password.show();
        }
    });

});

function show_login_form() {
    $("#login_form").show();
    $("#forgot_password_form").hide();
}

function show_forgot_password_form() {
    $("#login_form").hide();
    $("#forgot_password_form").show();
}

$(document).ready(function() {
    $("#show_forgot_password_form").click(function() {
        show_forgot_password_form();
    });
    $("#show_login_form").click(function() {
        show_login_form();
    });
});

</script>

<div class="container-max-xl mt-5">
    <div class="d-flex flex-column flex-lg-row justify-content-between align-items-start pt-0 pt-lg-5">
        <div class="w-100 w-lg-45">

            ' . $error_message . '
        
            <div id="login_form" class="" style="display: block;">
                <h2 class="mb-4">Log In</h2>
                <form name="frm_login" action="" method="post" role="form" class="mt-4">
                    <div class="mb-3">
                        <label for="frm_login_nameID" class="form-label">Email</label>
                        <input type="text" name="frm_login_name" id="frm_login_nameID" class="form-control" value="' . ((isset($_POST['frm_login_name']) && $_POST['frm_login_name'])?htmlspecialchars( $_POST['frm_login_name'], ENT_COMPAT, 'UTF-8'):'') . '">
                    </div>
                    <div class="mb-3">
                        <label for="password_field" class="form-label">Password</label>
                        <input type="text" name="visible_password" class="form-control" style="display:none;" value="">
                        <input type="password" name="frm_password" id="password_field"class="form-control" value="" autocomplete="off" >
                        <div class="form-check mt-2">
                            <input type="checkbox" id="show_pass" class="form-check-input">
                            <label class="form-check-label" for="show_pass">Show Passwords</label>
                        </div>
                    </div>
                    <div class="d-flex flex-column gap-3 flex-sm-row justify-content-between align-items-center mt-4">
                        <input type="submit" name="btnSubmit" value="Login" class="btn btn-primary">
                        <a class="btn btn-outline-primary" href="#" id="show_forgot_password_form">Forgot Password?</a>
                    </div>
                    ' . create_csrf_token() . '
                </form>
            </div>
            <div id="forgot_password_form" style="display: none;">
                <h2 class="mb-4">Forgot Password</h2>
                <p>Enter your email address to receive an email with instructions to reset your password.</p>
                <form name="frm_login" action="" method="post" class="mt-4">
                    <div class="mb-3">
                        <label for="frm_login_name2ID" class="form-label">Email</label>
                        <input type="text" name="frm_login_name2" id="frm_login_name2ID" class="form-control" value="' . ((isset($_POST['frm_login_name']) && $_POST['frm_login_name'])?htmlspecialchars($_POST['frm_login_name'], ENT_COMPAT, 'UTF-8'):'') . '">
                    </div>
                     <div class="d-flex flex-column gap-3 flex-sm-row justify-content-between align-items-center mt-4">
                        <input type="submit" name="btnSubmit" value="Send Email" class="btn btn-primary">
                        <a href="#" class="btn btn-outline-primary" id="show_login_form">Back to Login</a>
                    </div>
                    ' . create_csrf_token() . '
                </form>
            </div>
        </div>
        <div class="w-100 w-lg-45 mt-5 mt-lg-0 pt-5 pt-lg-0">
            <h2 class="mb-4">Create Account</h2>
            <p>If you are a new user, please create an account to get started.</p>
            <p class="mt-4"><a href="/create-account" class="btn btn-primary">Create Account</a></p>
        </div>
    </div>
</div>
';

