<?php
/**
 * File: public.tpl.inc.php
 *
 *
 */
header("X-Frame-Options: DENY");
?><!DOCTYPE html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>         <html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en"> <!--<![endif]-->
<head>
<meta charset="utf-8">
<meta name="robots" content="all">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<?php //meta data
if (isset($this->phantom_row['meta_keywords']) && $this->phantom_row['meta_keywords'] != '') {
    echo ('<meta name="keywords" content="' . htmlspecialchars( $this->phantom_row['meta_keywords'], ENT_COMPAT ) . '">' . "\n");
} elseif ($this->db_page_row['meta_keywords'] != '') {
    echo ('<meta name="keywords" content="' . htmlspecialchars( $this->db_page_row['meta_keywords'], ENT_COMPAT ) . '">' . "\n");
} else {
    echo ('<meta name="keywords" content="' . htmlspecialchars( $this->meta_keywords, ENT_COMPAT ) . '">' . "\n");
}
if (isset($this->phantom_row['meta_description']) && $this->phantom_row['meta_description'] != '') {
    echo ('<meta name="description" content="' . htmlspecialchars( $this->phantom_row['meta_description'], ENT_COMPAT ) . '">' . "\n");
    echo ('<meta property="og:description" content="' . htmlspecialchars( $this->phantom_row['meta_description'], ENT_COMPAT ) . '">' . "\n");
} elseif ($this->db_page_row['meta_description'] != '') {
    echo ('<meta name="description" content="' . htmlspecialchars( $this->db_page_row['meta_description'], ENT_COMPAT ) . '">' . "\n");
    echo ('<meta property="og:description" content="' . htmlspecialchars( $this->db_page_row['meta_description'], ENT_COMPAT ) . '">' . "\n");
} else {
    echo ('<meta name="description" content="' . htmlspecialchars( $this->meta_description, ENT_COMPAT ) . '">' . "\n");
    echo ('<meta property="og:description" content="' . htmlspecialchars( $this->meta_description, ENT_COMPAT ) . '">' . "\n");
}
?>
<title><?php // title data
if (isset($this->phantom_row['page_title']) && $this->phantom_row['page_title'] != '') {
    echo ( $this->phantom_row['page_title'] . ' - ');
} elseif (isset($this->db_page_row['head_title']) && $this->db_page_row['head_title'] != '') {
    echo ( $this->db_page_row['head_title'] . ' - ');
} elseif (isset($this->db_page_row['menu_display']) && $this->db_page_row['menu_display'] != '') {
    echo ( $this->db_page_row['menu_display'] . ' - ');
}
echo htmlspecialchars( $this->End_User_Name, ENT_COMPAT );
?></title>
<meta property="og:title" content="<?php // title data
if(isset($this->phantom_row['page_title']) && $this->phantom_row['page_title'] != '') {
    echo(htmlspecialchars( $this->phantom_row['page_title'], ENT_COMPAT ) . ' - ');
} elseif(isset($this->db_page_row['head_title']) && $this->db_page_row['head_title'] != '') {
    echo(htmlspecialchars( $this->db_page_row['head_title'], ENT_COMPAT ) . ' - ');
} elseif(isset($this->db_page_row['menu_display']) && $this->db_page_row['menu_display'] != '') {
    echo(htmlspecialchars( $this->db_page_row['menu_display'], ENT_COMPAT ) . ' - ');
}
echo htmlspecialchars( $this->End_User_Name, ENT_COMPAT );
?>">
<?php
$canonical_url = (($this->SSL_enabled == 1) ? 'https://' : 'http://') . $this->End_User_URL;
if(isset($this->phantom_row['item_id']) && $this->phantom_row['item_id']) {

    // get the "page group" from the URL, for phantom pages
    // FROM class.page.inc.php
    $request_uri = $this->real_escape_string($_SERVER['REQUEST_URI']);

    $url_parts = parse_url($request_uri);
    //$this->log_message(print_r($url_parts,true) );

    $url_parts['path'] = trim($url_parts['path'],'/');
    if(strlen($url_parts['path'])) {
        $numSlashes = countChars($url_parts['path'],'/');
        //$this->log_message("\$numSlashes:$numSlashes" );
        if($numSlashes > 0) {
            $path_parts = explode('/',$url_parts['path']);
            $last_part = array_pop($path_parts); // chop off last part
            $first_part = implode('/',$path_parts);
        }
    }
    $phantom_page_group_row = $this->getOneRowV2("KD_page_group","item_name","/$first_part/");
    if( $phantom_page_group_row['item_name'] ) {
        $canonical_url .= $phantom_page_group_row['item_name'] . $this->phantom_row[ $phantom_page_group_row['phantom_page_field'] ];
    } else {
        $canonical_url .= "/$first_part/" . $this->phantom_row['slug'];
    }

} else {

    if( $this->db_page_row['item_name'] == 'home' ) {
        $canonical_url .= $this->db_page_row['page_group_item_name'];
    } else {
        $canonical_url .= $this->db_page_row['page_group_item_name'] . $this->db_page_row['item_name'];
    }

}
echo '<link rel="canonical" href="'.$canonical_url.'" />';
?>

<!-- boostrap 5.3.1 - temporary settings until Jen builds a proper front-end -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/css/bootstrap.min.css" />
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js"></script>


<link href="/css/font-awesome.min.css" rel="stylesheet">
<link href="/css/C_public.css" rel="stylesheet">

<?php
// favicon and touch icons
if ($this->favicon_front)                   {echo '<link rel="shortcut icon" href="'. $this->favicon_front .'">' . "\n"; }
if ($this->icon_180)                        {echo '<link rel="apple-touch-icon" sizes="180x180" href="' . $this->icon_180 . '">' . "\n";}
if ($this->icon_32)                         {echo '<link rel="icon" type="image/png" sizes="32x32" href="' . $this->icon_32 . '">' . "\n";}
if ($this->icon_16)                         {echo '<link rel="icon" type="image/png" sizes="16x16" href="' . $this->icon_16 . '">' . "\n";}
if ($this->icon_192 AND $this->icon_512)    {echo '<link rel="manifest" href="/manifest.json">' . "\n";}
if ($this->theme_color_android)             {echo '<meta name="theme-color" content="' . $this->theme_color_android . '">' . "\n";}
if ($this->icon_svg)                        {echo '<link rel="mask-icon" href="' . $this->icon_svg . '" color="' . $this->icon_color_safari_pinned . '">' . "\n";}
?>

<script src="/js/vendor/modernizr-2.6.2-respond-1.1.0.min.js"></script>
<script type="text/javascript" src="/jsD/vendor/jquery.js"></script>
<!-- <script src="/js/vendor/bootstrap.js"></script> -->
<script src="/js/public.js"></script>

<?php
echo $this->db_page_row['insert_head'] . "\n";
?>
<script>
    <?php echo create_csrf_token_for_js();  ?>
</script>

</head>
<body class="<?php if ($this->db_page_row['css_class'] != '') {echo ($this->db_page_row['css_class']);} ?>">
<!--[if lt IE 7]>
    <p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/" rel="noopener">upgrade your browser</a> to improve your experience.</p>
<![endif]-->
<?php
if ($this->image_social_1) {echo '<img src="//' . $this->End_User_URL . $this->image_social_1 . '" alt="" style="display: none;">' . "\n";}
if ($this->image_social_2) {echo '<img src="//' . $this->End_User_URL . $this->image_social_2 . '" alt="" style="display: none;">' . "\n";}
?>

<header role="banner" id="site_header" class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <a href="#site_main" class="skip">Skip to main content</a>
        <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#nav_primary" aria-expanded="false">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <a href="/home" id="site_name" class="navbar-brand"><img src="<?php echo $this->image_logo_frontend; ?>" alt="<?php echo $this->End_User_Name; ?>"></a>
        </div>
        <nav role="navigation" aria-label="Primary" id="nav_primary" class="collapse navbar-collapse navbar-right">
            <?php
            echo $this->Show_List('list_C_nav_primary.');
            ?>
        </nav>
    </div>
</header>

<main role="main" id="site_main">

            <?php

            // SECTIONAL PAGES - pull in sections content
            if($this->db_page_row['page_type'] == 'landing') {
            
                // see cust_functions_pagecomposer.inc.php
                echo draw_page_sections($this);
            }
            
            // OTHER INTERIOR PAGES 
            else {

                ?>
    <div class="container">
        <div class="row">
            <?php

                // LANDING PAGE
                if ($this->db_page_row['item_name'] == 'home') {

                    echo '<div class="col-xs-12">' . "\n";

                    echo '<div id="billboards">' . "\n";
                    echo $this->Show_List('list_C_billboards_master.');
                    echo '</div>' . "\n";

                    echo '<div class="row">' . "\n";

                    echo '<div id="content" class="col-md-8">' . "\n";
                    if ($this->db_page_row['header']) {
                        echo '<h1>' . $this->db_page_row['header'] . '</h1>' . "\n";
                    }
                    echo $this->db_page_row['body'] . "\n";
                    echo '</div>' . "\n";

                    echo '<div id="sidebar" class="col-md-4">' . "\n";
                    echo $this->db_page_row['sidebar'] . "\n";
                    echo '</div>' . "\n";

                    echo '</div>' . "\n";

                    echo '</div>' . "\n";

                }

                // OTHER INTERIOR PAGES
                else {

                    echo '<div id="content" class="col-md-8 col-md-push-4">' . "\n";
                    if ($this->db_page_row['header']) {
                        echo '<h1>' . $this->db_page_row['header'] . '</h1>' . "\n";
                    }
                    echo $this->db_page_row['body'] . "\n";
                    echo '</div>' . "\n";

                    echo '<div id="sidebar" class="col-md-4 col-md-pull-8">' . "\n";
                    echo $this->db_page_row['sidebar'] . "\n";
                    echo '</div>' . "\n";

                }
            }

            ?>

        </div>
    </div>
</main>

<footer role="contentinfo" id="site_footer">
    <div class="container">
        <div class="row">
            <div class="col-sm-6 col-sm-push-6 social">
                <?php
                if ($this->social_facebook) {echo '<a href="' . $this->social_facebook  . '" target="_blank"><span class="sr-only">Facebook</span><i class="fa fa-2x fa-facebook-square"></i></a>' . "\n";}
                if ($this->social_twitter) {echo '<a href="' . $this->social_twitter  . '" target="_blank"><span class="sr-only">Twitter</span><i class="fa fa-2x fa-twitter-square"></i></a>' . "\n";}
                if ($this->social_gplus) {echo '<a href="' . $this->social_gplus  . '" target="_blank"><span class="sr-only">Google+</span><i class="fa fa-2x fa-google-plus-square"></i></a>' . "\n";}
                if ($this->social_youtube) {echo '<a href="' . $this->social_youtube  . '" target="_blank"><span class="sr-only">YouTube</span><i class="fa fa-2x fa-youtube-square"></i></a>' . "\n";}
                if ($this->social_pinterest) {echo '<a href="' . $this->social_pinterest  . '" target="_blank"><span class="sr-only">Pinterest</span><i class="fa fa-2x fa-pinterest-square"></i></a>' . "\n";}
                if ($this->social_linkedin) {echo '<a href="' . $this->social_linkedin  . '" target="_blank"><span class="sr-only">LinkedIn</span><i class="fa fa-2x fa-linkedin-square"></i></a>' . "\n";}
                if ($this->social_instagram) {echo '<a href="' . $this->social_instagram  . '" target="_blank"><span class="sr-only">Instagram</span><i class="fa fa-2x fa-instagram"></i></a>' . "\n";}
                ?>
            </div>
            <div class="col-sm-6 col-sm-pull-6 small legal">
                <p>&copy; <?php echo(date("Y")) . ' ' . $this->End_User_Name ?>. All Rights Reserved.<br>
                <?php echo $this->inspector_link('<img src="/imgD/Powered-by-Dialogs-Logo-White.png" alt="Powered by Dialogs">'); ?></p>
            </div>
        </div>
    </div>
</footer>

<?php
echo $this->db_page_row['insert_body'] . "\n";
echo $this->inspector_head() . "\n";
echo $this->analytics_code . "\n";
?>

</body>
</html>
