<?php

// Query the C_events table to get event data for item_id = 6
$event = $this->getOneRowV2("C_events", "item_id", $_GET['id']);

// Set page title to event name
if (!empty($event['item_name'])) {
    $this->db_page_row['title'] = $event['item_name'];
}

//
// BUILD SOME PIECES FOR USE
//

// Event Dates
$event_date = '';
$event_start_date = '';
$event_end_date = '';
if (!empty($event['start_date']) && !empty($event['end_date'])) {
    $event_start_date = reformat_date('M d Y',$event['start_date']);
    $event_end_date = reformat_date('M d Y',$event['end_date']);
    $event_date = $event_start_date . ' - ' . $event_end_date;
}
elseif (!empty($event['start_date'])) {
    $event_start_date = reformat_date('M d Y',$event['start_date']);
    $event_date = $event_start_date;
}
$event_hero_date = '<div class="mb-2 fs-16 text-uppercase fw-semibold">' . $event_date . '</div>';

// Hero Background Image
$event_hero_image = '';
if (!empty($event['attachment_filename5'])) {
    $event_hero_image .= '<img src="/images/events/' . htmlspecialchars($event['attachment_filename5']) . '" alt="" class="object-fit-cover" style="width: 100%; min-height: 100%;">';
}

// Hero Headline
$event_hero_headline = '';
if (!empty($event['hero_headline'])) {
    $event_hero_headline .= '<h2 class="mb-4">' . htmlspecialchars($event['hero_headline']) . '</h2>';
}

// Hero Content
$event_hero_content = '';
if (!empty($event['hero_content'])) {
    $event_hero_content .= '<p>' . htmlspecialchars($event['hero_content']) . '</p>';
}

// Hero CTA
$event_hero_cta = '';
if (!empty($event['hero_cta_text']) && !empty($event['hero_cta_url'])) {
    $event_hero_cta .= '<a href="' . htmlspecialchars($event['hero_cta_url']) . '" class="mt-auto link-white fs-16 fw-semibold text-uppercase text-decoration-none" target="_blank" rel="noopener noreferrer nofollow">' . htmlspecialchars($event['hero_cta_text']) . ' &#10142;</a>';
}

// Event Code
$event_code = '';
if (!empty($event['event_code'])) {
    $event_code = '<p><strong class="text-uppercase fw-semibold">Event Code:</strong> ' . htmlspecialchars($event['event_code']) . '</p>';
}

// Year
$event_year = '';
if (!empty($event['event_year'])) {
    $event_year = '<p><strong class="text-uppercase fw-semibold">Year:</strong> ' . htmlspecialchars($event['event_year']) . '</p>';
}

// Dates
$event_detail_start_date = '';
$event_detail_end_date = '';
if ($event_start_date) {
    $event_detail_start_date = '<p><strong class="text-uppercase fw-semibold">Start Date:</strong> ' . $event_start_date . '</p>';
}
if ($event_end_date) {
    $event_detail_end_date = '<p><strong class="text-uppercase fw-semibold">End Date:</strong> ' . $event_end_date . '</p>';
}

// Location
$event_location = '';
if (!empty($event['location'])) {
    $event_location = '<p><strong class="text-uppercase fw-semibold">Location:</strong></p> ' . $event['location'] . '';
}

// DOCUMENT - Consent and Release Form
$event_docs_consent_release_form = '';
if (!empty($event['attachment_filename7'])) {
    $event_docs_consent_release_form = '<p><a target="_blank" rel="noopener noreferrer nofollow" href="/images/events/' . htmlspecialchars($event['attachment_filename7']) . '" class="btn btn-sm btn-outline-blue text-wrap">Consent and Release Form</a></p>';
}

// DOCUMENT - Consent and Release Form Systems Manual
$event_docs_consent_release_form_systems_manual = '';
if (!empty($event['attachment_filename6'])) {
    $event_docs_consent_release_form_systems_manual = '<p><a target="_blank" rel="noopener noreferrer nofollow" href="/images/events/' . htmlspecialchars($event['attachment_filename6']) . '" class="btn btn-sm btn-outline-blue text-wrap">Consent and Release Form Systems Manual</a></p>';
}

// DOCUMENT - Agenda
$event_docs_agenda = '';
if (!empty($event['attachment_filename2'])) {
    $event_docs_agenda = '<p><a target="_blank" rel="noopener noreferrer nofollow" href="/images/events/' . htmlspecialchars($event['attachment_filename2']) . '" class="btn btn-sm btn-outline-blue text-wrap">Agenda</a></p>';
}

// DOCUMENT - Pit Map
$event_docs_pit_map = '';
if (!empty($event['attachment_filename3'])) {
    $event_docs_pit_map = '<p><a target="_blank" rel="noopener noreferrer nofollow" href="/images/events/' . htmlspecialchars($event['attachment_filename3']) . '" class="btn btn-sm btn-outline-blue text-wrap">Pit Map</a></p>';
}

// DOCUMENT - Team List
$event_docs_team_list = '';
if (!empty($event['attachment_filename4'])) {
    $event_docs_team_list = '<p><a target="_blank" rel="noopener noreferrer nofollow" href="/images/events/' . htmlspecialchars($event['attachment_filename4']) . '" class="btn btn-sm btn-outline-blue text-wrap">Team List</a></p>';
}

// DOCUMENTS - Block
$event_docs_block = '';
if ($event_docs_consent_release_form || $event_docs_consent_release_form_systems_manual || $event_docs_agenda || $event_docs_pit_map || $event_docs_team_list) {
    $event_docs_block = '
    <div class="col-12 col-lg-6 mb-5">
        <div class="card border border-gray-500 rounded-3 h-100" style="--bs-border-opacity: .3;">
            <div class="card-header">
                <h3 class="h4 mb-0">Documents</h3>
            </div>
            <div class="card-body fs-16">
                ' . $event_docs_consent_release_form . '
                ' . $event_docs_consent_release_form_systems_manual . '
                ' . $event_docs_agenda . '
                ' . $event_docs_pit_map . '
                ' . $event_docs_team_list . '
            </div>
        </div>
    </div>
    ';
}

// Public Details
$event_public_details = '';
if (!empty($event['public_content'])) {
    $event_public_details = '
    <div class="col-12 mb-5">
        <div class="card border border-gray-500 rounded-3 h-100" style="--bs-border-opacity: .3;">
            <div class="card-header">
                <h3 class="h4 mb-0">Public Details</h3>
            </div>
            <div class="card-body fs-16">
                ' . $event['public_content'] . '
            </div>
        </div>
    </div>
    ';
}

// Coach Packet
$event_coach_packet = '';
if (!empty($event['coach_packet_content'])) {
    $event_coach_packet = '
    <div class="col-12 mb-5">
        <div class="card border border-gray-500 rounded-3 h-100" style="--bs-border-opacity: .3;">
            <div class="card-header">
                <h3 class="h4 mb-0">Coach Packet</h3>
            </div>
            <div class="card-body fs-16">
                ' . $event['coach_packet_content'] . '
            </div>
        </div>
    </div>
    ';
}

// Sponsors
$event_sponsors = '';
if (!empty($event['sponsors'])) {
    $event_sponsors = '
    <div class="col-12 mb-5">
        <div class="card border border-gray-500 rounded-3 h-100" style="--bs-border-opacity: .3;">
            <div class="card-header">
                <h3 class="h4 mb-0">Sponsors</h3>
            </div>
            <div class="card-body fs-16">
                ' . $event['sponsors'] . '
            </div>
        </div>
    </div>
    ';
}

// Hotel Booking
$event_hotel_booking = '';
if (!empty($event['hotel_booking'])) {
    $event_hotel_booking = '
    <div class="col-12 mb-5">
        <div class="card border border-gray-500 rounded-3 h-100" style="--bs-border-opacity: .3;">
            <div class="card-header">
                <h3 class="h4 mb-0">Hotel and Booking Information</h3>
            </div>
            <div class="card-body fs-16">
                ' . $event['hotel_booking'] . '
            </div>
        </div>
    </div>
    ';
}


// Build HTML for event content display using our portal_branded.css file,
// which is a grid and utilities-only version of bootstrap 5.
// This requires using the portal-branded class on the parent element.
// Adding one additional nested container to add some padding on large screens.
$html = '
<div class="portal-branded">
<div class="px-lg-4">
';

// Event Content Header
$html .= '
<div class="d-flex flex-column flex-md-row justify-content-between align-items-start my-5">
    <a href="/a/dashboard-team" class="btn btn-sm btn-primary order-md-2 mb-4 mb-md-0 ms-md-5">Return to Dashboard</a>
    <h1 class="h2 my-0 order-md-1">' . htmlspecialchars($event['item_name']) . '</h1>
</div> 
';

// Hero Section
$html .= '
<div class="d-flex flex-column flex-lg-row justify-content-between align-items-stretch rounded-3 overflow-hidden my-5">
    <div class="w-100 w-lg-50 text-bg-blue">
        ' . $event_hero_image . '
    </div>
    <div class="w-100 w-lg-50 text-bg-blue p-5">
        <div class="mb-5">
            ' . $event_hero_date . '
            ' . $event_hero_headline . '
            ' . $event_hero_content . '
        </div>
        ' . $event_hero_cta . '
    </div>
</div>
';

// Event Details
$html .= '
<div class="my-5">
    <div class="row flex-wrap justify-content-between align-items-stretch">
        <div class="col-12 col-lg-6 mb-5">
            <div class="card border border-gray-500 rounded-3 h-100" style="--bs-border-opacity: .3;">
                <div class="card-header">
                    <h3 class="h4 mb-0">Event Information</h3>
                </div>
                <div class="card-body fs-16">
                    ' . $event_code . '
                    ' . $event_year . '
                    ' . $event_detail_start_date . '
                    ' . $event_detail_end_date . '
                    ' . $event_location . '
                </div>
            </div>
        </div>
        ' . $event_docs_block . '
        ' . $event_public_details . '
        ' . $event_coach_packet . '
        ' . $event_sponsors . '
        ' . $event_hotel_booking . '
    </div>
</div>
';

// CLOSE .portal-branded and nested padding div
$html .= '
</div>
</div>
';

// Add the HTML to the page body
$this->db_page_row['body'] = $html;

