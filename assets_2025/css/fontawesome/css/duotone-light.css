/*!
 * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 * Copyright 2024 Fonticons, Inc.
 */
:root, :host {
  --fa-style-family-duotone: 'Font Awesome 6 Duotone';
  --fa-font-duotone-light: normal 300 1em/1 'Font Awesome 6 Duotone'; }

@font-face {
  font-family: 'Font Awesome 6 Duotone';
  font-style: normal;
  font-weight: 300;
  font-display: block;
  src: url("../webfonts/fa-duotone-light-300.woff2") format("woff2"), url("../webfonts/fa-duotone-light-300.ttf") format("truetype"); }

.fadl,
.fa-duotone.fa-light {
  position: relative;
  font-weight: 300;
  letter-spacing: normal; }

.fadl::before,
.fa-duotone.fa-light::before {
  position: absolute;
  color: var(--fa-primary-color, inherit);
  opacity: var(--fa-primary-opacity, 1); }

.fadl::after,
.fa-duotone.fa-light::after {
  color: var(--fa-secondary-color, inherit);
  opacity: var(--fa-secondary-opacity, 0.4); }

.fa-swap-opacity .fadl::before,
.fa-swap-opacity .fa-duotone.fa-light::before,
.fadl.fa-swap-opacity::before,
.fa-duotone.fa-swap-opacity::before,
.fa-duotone.fa-light.fa-swap-opacity::before {
  opacity: var(--fa-secondary-opacity, 0.4); }

.fa-swap-opacity .fadl::after,
.fa-swap-opacity .fa-duotone.fa-light::after,
.fadl.fa-swap-opacity::after,
.fa-duotone.fa-swap-opacity::after,
.fa-duotone.fa-light.fa-swap-opacity::after {
  opacity: var(--fa-primary-opacity, 1); }

.fadl.fa-inverse,
.fa-duotone.fa-light.fa-inverse {
  color: var(--fa-inverse, #fff); }

.fadl.fa-stack-1x,
.fadl.fa-stack-2x,
.fa-duotone.fa-light.fa-stack-1x,
.fa-duotone.fa-light.fa-stack-2x {
  position: absolute; }

/*# sourceMappingURL=duotone-light.css.map */