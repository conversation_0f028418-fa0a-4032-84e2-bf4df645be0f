{"version": 3, "sources": ["sharp-duotone-light.css"], "names": [], "mappings": "AAAA;;;;EAIE;AACF;EACE,+DAA+D;EAC/D,8EAA8E,EAAE;;AAElF;EACE,2CAA2C;EAC3C,kBAAkB;EAClB,gBAAgB;EAChB,mBAAmB;EACnB,8IAA8I,EAAE;;AAElJ;;EAEE,kBAAkB;EAClB,gBAAgB;EAChB,sBAAsB,EAAE;;AAE1B;;EAEE,kBAAkB;EAClB,uCAAuC;EACvC,qCAAqC,EAAE;;AAEzC;;EAEE,yCAAyC;EACzC,yCAAyC,EAAE;;AAE7C;;;;;EAKE,yCAAyC,EAAE;;AAE7C;;;;;EAKE,qCAAqC,EAAE;;AAEzC;;EAEE,8BAA8B,EAAE;;AAElC;;;;EAIE,kBAAkB,EAAE", "file": "sharp-duotone-light.css", "sourcesContent": ["/*!\n * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license (Commercial License)\n * Copyright 2024 Fonticons, Inc.\n */\n:root, :host {\n  --fa-style-family-sharp-duotone: 'Font Awesome 6 Sharp Duotone';\n  --fa-font-sharp-duotone-light: normal 300 1em/1 'Font Awesome 6 Sharp Duotone'; }\n\n@font-face {\n  font-family: 'Font Awesome 6 Sharp Duotone';\n  font-style: normal;\n  font-weight: 300;\n  font-display: block;\n  src: url(\"../webfonts/fa-sharp-duotone-light-300.woff2\") format(\"woff2\"), url(\"../webfonts/fa-sharp-duotone-light-300.ttf\") format(\"truetype\"); }\n\n.fasdl,\n.fa-sharp-duotone.fa-light {\n  position: relative;\n  font-weight: 300;\n  letter-spacing: normal; }\n\n.fasdl::before,\n.fa-sharp-duotone.fa-light::before {\n  position: absolute;\n  color: var(--fa-primary-color, inherit);\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fasdl::after,\n.fa-sharp-duotone.fa-light::after {\n  color: var(--fa-secondary-color, inherit);\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fasdl::before,\n.fa-swap-opacity .fa-sharp-duotone.fa-light::before,\n.fasdl.fa-swap-opacity::before,\n.fa-sharp-duotone.fa-swap-opacity::before,\n.fa-sharp-duotone.fa-light.fa-swap-opacity::before {\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fasdl::after,\n.fa-swap-opacity .fa-sharp-duotone.fa-light::after,\n.fasdl.fa-swap-opacity::after,\n.fa-sharp-duotone.fa-swap-opacity::after,\n.fa-sharp-duotone.fa-light.fa-swap-opacity::after {\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fasdl.fa-inverse,\n.fa-sharp-duotone.fa-light.fa-inverse {\n  color: var(--fa-inverse, #fff); }\n\n.fasdl.fa-stack-1x,\n.fasdl.fa-stack-2x,\n.fa-sharp-duotone.fa-light.fa-stack-1x,\n.fa-sharp-duotone.fa-light.fa-stack-2x {\n  position: absolute; }\n"]}