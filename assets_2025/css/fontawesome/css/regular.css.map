{"version": 3, "sources": ["regular.css"], "names": [], "mappings": "AAAA;;;;EAIE;AACF;EACE,+CAA+C;EAC/C,wDAAwD,EAAE;;AAE5D;EACE,iCAAiC;EACjC,kBAAkB;EAClB,gBAAgB;EAChB,mBAAmB;EACnB,sHAAsH,EAAE;;AAE1H;;EAEE,gBAAgB,EAAE", "file": "regular.css", "sourcesContent": ["/*!\n * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license (Commercial License)\n * Copyright 2024 Fonticons, Inc.\n */\n:root, :host {\n  --fa-style-family-classic: 'Font Awesome 6 Pro';\n  --fa-font-regular: normal 400 1em/1 'Font Awesome 6 Pro'; }\n\n@font-face {\n  font-family: 'Font Awesome 6 Pro';\n  font-style: normal;\n  font-weight: 400;\n  font-display: block;\n  src: url(\"../webfonts/fa-regular-400.woff2\") format(\"woff2\"), url(\"../webfonts/fa-regular-400.ttf\") format(\"truetype\"); }\n\n.far,\n.fa-regular {\n  font-weight: 400; }\n"]}