{"version": 3, "sources": ["sharp-thin.css"], "names": [], "mappings": "AAAA;;;;EAIE;AACF;EACE,+CAA+C;EAC/C,6DAA6D,EAAE;;AAEjE;EACE,mCAAmC;EACnC,kBAAkB;EAClB,gBAAgB;EAChB,mBAAmB;EACnB,4HAA4H,EAAE;;AAEhI;;EAEE,gBAAgB,EAAE", "file": "sharp-thin.css", "sourcesContent": ["/*!\n * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license (Commercial License)\n * Copyright 2024 Fonticons, Inc.\n */\n:root, :host {\n  --fa-style-family-sharp: 'Font Awesome 6 Sharp';\n  --fa-font-sharp-thin: normal 100 1em/1 'Font Awesome 6 Sharp'; }\n\n@font-face {\n  font-family: 'Font Awesome 6 Sharp';\n  font-style: normal;\n  font-weight: 100;\n  font-display: block;\n  src: url(\"../webfonts/fa-sharp-thin-100.woff2\") format(\"woff2\"), url(\"../webfonts/fa-sharp-thin-100.ttf\") format(\"truetype\"); }\n\n.fast,\n.fa-thin {\n  font-weight: 100; }\n"]}