{"version": 3, "sources": ["duotone-thin.css"], "names": [], "mappings": "AAAA;;;;EAIE;AACF;EACE,mDAAmD;EACnD,iEAAiE,EAAE;;AAErE;EACE,qCAAqC;EACrC,kBAAkB;EAClB,gBAAgB;EAChB,mBAAmB;EACnB,gIAAgI,EAAE;;AAEpI;;EAEE,kBAAkB;EAClB,gBAAgB;EAChB,sBAAsB,EAAE;;AAE1B;;EAEE,kBAAkB;EAClB,uCAAuC;EACvC,qCAAqC,EAAE;;AAEzC;;EAEE,yCAAyC;EACzC,yCAAyC,EAAE;;AAE7C;;;;;EAKE,yCAAyC,EAAE;;AAE7C;;;;;EAKE,qCAAqC,EAAE;;AAEzC;;EAEE,8BAA8B,EAAE;;AAElC;;;;EAIE,kBAAkB,EAAE", "file": "duotone-thin.css", "sourcesContent": ["/*!\n * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license (Commercial License)\n * Copyright 2024 Fonticons, Inc.\n */\n:root, :host {\n  --fa-style-family-duotone: 'Font Awesome 6 Duotone';\n  --fa-font-duotone-thin: normal 100 1em/1 'Font Awesome 6 Duotone'; }\n\n@font-face {\n  font-family: 'Font Awesome 6 Duotone';\n  font-style: normal;\n  font-weight: 100;\n  font-display: block;\n  src: url(\"../webfonts/fa-duotone-thin-100.woff2\") format(\"woff2\"), url(\"../webfonts/fa-duotone-thin-100.ttf\") format(\"truetype\"); }\n\n.fadt,\n.fa-duotone.fa-thin {\n  position: relative;\n  font-weight: 100;\n  letter-spacing: normal; }\n\n.fadt::before,\n.fa-duotone.fa-thin::before {\n  position: absolute;\n  color: var(--fa-primary-color, inherit);\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fadt::after,\n.fa-duotone.fa-thin::after {\n  color: var(--fa-secondary-color, inherit);\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fadt::before,\n.fa-swap-opacity .fa-duotone.fa-thin::before,\n.fadt.fa-swap-opacity::before,\n.fa-duotone.fa-swap-opacity::before,\n.fa-duotone.fa-thin.fa-swap-opacity::before {\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fadt::after,\n.fa-swap-opacity .fa-duotone.fa-thin::after,\n.fadt.fa-swap-opacity::after,\n.fa-duotone.fa-swap-opacity::after,\n.fa-duotone.fa-thin.fa-swap-opacity::after {\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fadt.fa-inverse,\n.fa-duotone.fa-thin.fa-inverse {\n  color: var(--fa-inverse, #fff); }\n\n.fadt.fa-stack-1x,\n.fadt.fa-stack-2x,\n.fa-duotone.fa-thin.fa-stack-1x,\n.fa-duotone.fa-thin.fa-stack-2x {\n  position: absolute; }\n"]}