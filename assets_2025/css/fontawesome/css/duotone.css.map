{"version": 3, "sources": ["duotone.css"], "names": [], "mappings": "AAAA;;;;EAIE;AACF;EACE,mDAAmD;EACnD,4DAA4D,EAAE;;AAEhE;EACE,qCAAqC;EACrC,kBAAkB;EAClB,gBAAgB;EAChB,mBAAmB;EACnB,sHAAsH,EAAE;;AAE1H;;EAEE,kBAAkB;EAClB,gBAAgB;EAChB,sBAAsB,EAAE;;AAE1B;;EAEE,kBAAkB;EAClB,uCAAuC;EACvC,qCAAqC,EAAE;;AAEzC;;EAEE,yCAAyC;EACzC,yCAAyC,EAAE;;AAE7C;;;;EAIE,yCAAyC,EAAE;;AAE7C;;;;EAIE,qCAAqC,EAAE;;AAEzC;;EAEE,8BAA8B,EAAE;;AAElC;;EAEE,kBAAkB,EAAE", "file": "duotone.css", "sourcesContent": ["/*!\n * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license (Commercial License)\n * Copyright 2024 Fonticons, Inc.\n */\n:root, :host {\n  --fa-style-family-duotone: 'Font Awesome 6 Duotone';\n  --fa-font-duotone: normal 900 1em/1 'Font Awesome 6 Duotone'; }\n\n@font-face {\n  font-family: 'Font Awesome 6 Duotone';\n  font-style: normal;\n  font-weight: 900;\n  font-display: block;\n  src: url(\"../webfonts/fa-duotone-900.woff2\") format(\"woff2\"), url(\"../webfonts/fa-duotone-900.ttf\") format(\"truetype\"); }\n\n.fad,\n.fa-duotone {\n  position: relative;\n  font-weight: 900;\n  letter-spacing: normal; }\n\n.fad::before,\n.fa-duotone::before {\n  position: absolute;\n  color: var(--fa-primary-color, inherit);\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fad::after,\n.fa-duotone::after {\n  color: var(--fa-secondary-color, inherit);\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fad::before,\n.fa-swap-opacity .fa-duotone::before,\n.fad.fa-swap-opacity::before,\n.fa-duotone.fa-swap-opacity::before {\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fad::after,\n.fa-swap-opacity .fa-duotone::after,\n.fad.fa-swap-opacity::after,\n.fa-duotone.fa-swap-opacity::after {\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fad.fa-inverse,\n.fa-duotone.fa-inverse {\n  color: var(--fa-inverse, #fff); }\n\n.fad.fa-stack-1x, .fad.fa-stack-2x,\n.fa-duotone.fa-stack-1x, .fa-duotone.fa-stack-2x {\n  position: absolute; }\n"]}