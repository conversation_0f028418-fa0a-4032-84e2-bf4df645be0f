{"version": 3, "sources": ["sharp-duotone-regular.css"], "names": [], "mappings": "AAAA;;;;EAIE;AACF;EACE,+DAA+D;EAC/D,gFAAgF,EAAE;;AAEpF;EACE,2CAA2C;EAC3C,kBAAkB;EAClB,gBAAgB;EAChB,mBAAmB;EACnB,kJAAkJ,EAAE;;AAEtJ;;EAEE,kBAAkB;EAClB,gBAAgB;EAChB,sBAAsB,EAAE;;AAE1B;;EAEE,kBAAkB;EAClB,uCAAuC;EACvC,qCAAqC,EAAE;;AAEzC;;EAEE,yCAAyC;EACzC,yCAAyC,EAAE;;AAE7C;;;;;EAKE,yCAAyC,EAAE;;AAE7C;;;;;EAKE,qCAAqC,EAAE;;AAEzC;;EAEE,8BAA8B,EAAE;;AAElC;;;;EAIE,kBAAkB,EAAE", "file": "sharp-duotone-regular.css", "sourcesContent": ["/*!\n * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license (Commercial License)\n * Copyright 2024 Fonticons, Inc.\n */\n:root, :host {\n  --fa-style-family-sharp-duotone: 'Font Awesome 6 Sharp Duotone';\n  --fa-font-sharp-duotone-regular: normal 400 1em/1 'Font Awesome 6 Sharp Duotone'; }\n\n@font-face {\n  font-family: 'Font Awesome 6 Sharp Duotone';\n  font-style: normal;\n  font-weight: 400;\n  font-display: block;\n  src: url(\"../webfonts/fa-sharp-duotone-regular-400.woff2\") format(\"woff2\"), url(\"../webfonts/fa-sharp-duotone-regular-400.ttf\") format(\"truetype\"); }\n\n.fasdr,\n.fa-sharp-duotone.fa-regular {\n  position: relative;\n  font-weight: 400;\n  letter-spacing: normal; }\n\n.fasdr::before,\n.fa-sharp-duotone.fa-regular::before {\n  position: absolute;\n  color: var(--fa-primary-color, inherit);\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fasdr::after,\n.fa-sharp-duotone.fa-regular::after {\n  color: var(--fa-secondary-color, inherit);\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fasdr::before,\n.fa-swap-opacity .fa-sharp-duotone.fa-regular::before,\n.fasdr.fa-swap-opacity::before,\n.fa-sharp-duotone.fa-swap-opacity::before,\n.fa-sharp-duotone.fa-regular.fa-swap-opacity::before {\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fasdr::after,\n.fa-swap-opacity .fa-sharp-duotone.fa-regular::after,\n.fasdr.fa-swap-opacity::after,\n.fa-sharp-duotone.fa-swap-opacity::after,\n.fa-sharp-duotone.fa-regular.fa-swap-opacity::after {\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fasdr.fa-inverse,\n.fa-sharp-duotone.fa-regular.fa-inverse {\n  color: var(--fa-inverse, #fff); }\n\n.fasdr.fa-stack-1x,\n.fasdr.fa-stack-2x,\n.fa-sharp-duotone.fa-regular.fa-stack-1x,\n.fa-sharp-duotone.fa-regular.fa-stack-2x {\n  position: absolute; }\n"]}