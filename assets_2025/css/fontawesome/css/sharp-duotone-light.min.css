/*!
 * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 * Copyright 2024 Fonticons, Inc.
 */
:host,:root{--fa-style-family-sharp-duotone:"Font Awesome 6 Sharp Duotone";--fa-font-sharp-duotone-light:normal 300 1em/1 "Font Awesome 6 Sharp Duotone"}@font-face{font-family:"Font Awesome 6 Sharp Duotone";font-style:normal;font-weight:300;font-display:block;src:url(../webfonts/fa-sharp-duotone-light-300.woff2) format("woff2"),url(../webfonts/fa-sharp-duotone-light-300.ttf) format("truetype")}.fa-sharp-duotone.fa-light,.fasdl{position:relative;font-weight:300;letter-spacing:normal}.fa-sharp-duotone.fa-light:before,.fasdl:before{position:absolute;color:var(--fa-primary-color,inherit);opacity:var(--fa-primary-opacity,1)}.fa-sharp-duotone.fa-light:after,.fasdl:after{color:var(--fa-secondary-color,inherit);opacity:var(--fa-secondary-opacity,.4)}.fa-sharp-duotone.fa-light.fa-swap-opacity:before,.fa-sharp-duotone.fa-swap-opacity:before,.fa-swap-opacity .fa-sharp-duotone.fa-light:before,.fa-swap-opacity .fasdl:before,.fasdl.fa-swap-opacity:before{opacity:var(--fa-secondary-opacity,.4)}.fa-sharp-duotone.fa-light.fa-swap-opacity:after,.fa-sharp-duotone.fa-swap-opacity:after,.fa-swap-opacity .fa-sharp-duotone.fa-light:after,.fa-swap-opacity .fasdl:after,.fasdl.fa-swap-opacity:after{opacity:var(--fa-primary-opacity,1)}.fa-sharp-duotone.fa-light.fa-inverse,.fasdl.fa-inverse{color:var(--fa-inverse,#fff)}.fa-sharp-duotone.fa-light.fa-stack-1x,.fa-sharp-duotone.fa-light.fa-stack-2x,.fasdl.fa-stack-1x,.fasdl.fa-stack-2x{position:absolute}
/*# sourceMappingURL=sharp-duotone-light.min.css.map */