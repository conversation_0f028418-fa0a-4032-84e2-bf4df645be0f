{"version": 3, "sources": ["sharp-duotone-solid.css"], "names": [], "mappings": "AAAA;;;;EAIE;AACF;EACE,+DAA+D;EAC/D,8EAA8E,EAAE;;AAElF;EACE,2CAA2C;EAC3C,kBAAkB;EAClB,gBAAgB;EAChB,mBAAmB;EACnB,8IAA8I,EAAE;;AAElJ;;;EAGE,kBAAkB;EAClB,gBAAgB;EAChB,sBAAsB,EAAE;;AAE1B;;;EAGE,kBAAkB;EAClB,uCAAuC;EACvC,qCAAqC,EAAE;;AAEzC;;;EAGE,yCAAyC;EACzC,yCAAyC,EAAE;;AAE7C;;;;;;EAME,yCAAyC,EAAE;;AAE7C;;;;;;EAME,qCAAqC,EAAE;;AAEzC;;;EAGE,8BAA8B,EAAE;;AAElC;;;;;;EAME,kBAAkB,EAAE", "file": "sharp-duotone-solid.css", "sourcesContent": ["/*!\n * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license (Commercial License)\n * Copyright 2024 Fonticons, Inc.\n */\n:root, :host {\n  --fa-style-family-sharp-duotone: 'Font Awesome 6 Sharp Duotone';\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 'Font Awesome 6 Sharp Duotone'; }\n\n@font-face {\n  font-family: 'Font Awesome 6 Sharp Duotone';\n  font-style: normal;\n  font-weight: 900;\n  font-display: block;\n  src: url(\"../webfonts/fa-sharp-duotone-solid-900.woff2\") format(\"woff2\"), url(\"../webfonts/fa-sharp-duotone-solid-900.ttf\") format(\"truetype\"); }\n\n.fasds,\n.fa-sharp-duotone,\n.fa-sharp-duotone.fa-solid {\n  position: relative;\n  font-weight: 900;\n  letter-spacing: normal; }\n\n.fasds::before,\n.fa-sharp-duotone::before,\n.fa-sharp-duotone.fa-solid::before {\n  position: absolute;\n  color: var(--fa-primary-color, inherit);\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fasds::after,\n.fa-sharp-duotone::after,\n.fa-sharp-duotone.fa-solid::after {\n  color: var(--fa-secondary-color, inherit);\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fasds::before,\n.fa-swap-opacity .fa-sharp-duotone::before,\n.fa-swap-opacity .fa-sharp-duotone.fa-solid::before,\n.fasds.fa-swap-opacity::before,\n.fa-sharp-duotone.fa-swap-opacity::before,\n.fa-sharp-duotone.fa-solid.fa-swap-opacity::before {\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fasds::after,\n.fa-swap-opacity .fa-sharp-duotone::after,\n.fa-swap-opacity .fa-sharp-duotone.fa-solid::after,\n.fasds.fa-swap-opacity::after,\n.fa-sharp-duotone.fa-swap-opacity::after,\n.fa-sharp-duotone.fa-solid.fa-swap-opacity::after {\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fasds.fa-inverse,\n.fa-sharp-duotone.fa-inverse,\n.fa-sharp-duotone.fa-solid.fa-inverse {\n  color: var(--fa-inverse, #fff); }\n\n.fasds.fa-stack-1x,\n.fasds.fa-stack-2x,\n.fa-sharp-duotone.fa-stack-1x,\n.fa-sharp-duotone.fa-solid.fa-stack-1x,\n.fa-sharp-duotone.fa-stack-2x,\n.fa-sharp-duotone.fa-solid.fa-stack-2x {\n  position: absolute; }\n"]}