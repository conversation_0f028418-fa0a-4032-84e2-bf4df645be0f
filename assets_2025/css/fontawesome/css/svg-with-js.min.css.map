{"version": 3, "sources": ["svg-with-js.min.css"], "names": [], "mappings": "AAAA;;;;EAIE;AACF,YAAY,qDAAqD,CAAC,uDAAuD,CAAC,qDAAqD,CAAC,oDAAoD,CAAC,2DAA2D,CAAC,mEAAmE,CAAC,iEAAiE,CAAC,gEAAgE,CAAC,yDAAyD,CAAC,6DAA6D,CAAC,+DAA+D,CAAC,6DAA6D,CAAC,4DAA4D,CAAC,6EAA6E,CAAC,+EAA+E,CAAC,6EAA6E,CAAC,4EAA4E,CAAC,4DAA4D,gBAAgB,CAAC,kBAAkB,CAAC,gBAAgB,sCAAsC,CAAC,UAAU,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,uBAAuB,mBAAmB,CAAC,sBAAsB,gBAAgB,CAAC,sBAAsB,wBAAwB,CAAC,sBAAsB,oBAAoB,CAAC,sBAAsB,qBAAqB,CAAC,uBAAuB,uBAAuB,CAAC,6BAA6B,uCAAuC,CAAC,UAAU,CAAC,8BAA8B,sCAAsC,CAAC,UAAU,CAAC,sBAAsB,4BAA4B,CAAC,SAAS,CAAC,sBAAsB,+BAA+B,CAAC,8BAA8B,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,mCAAmC,oBAAoB,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,WAAW,oBAAoB,CAAC,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,SAAS,CAAC,8BAA8B,8BAA8B,CAAC,gBAAgB,QAAQ,CAAC,OAAO,CAAC,8BAA8B,CAAC,8BAA8B,CAAC,mBAAmB,2DAA2D,CAAC,iDAAiD,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,2CAA2C,CAAC,yCAAyC,CAAC,2CAA2C,CAAC,eAAe,CAAC,4CAA4C,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,4CAA4C,CAAC,0BAA0B,CAAC,wBAAwB,yBAAyB,CAAC,uBAAuB,CAAC,QAAQ,CAAC,2CAA2C,CAAC,6BAA6B,CAAC,uBAAuB,yBAAyB,CAAC,qBAAqB,CAAC,UAAU,CAAC,QAAQ,CAAC,2CAA2C,CAAC,4BAA4B,CAAC,qBAAqB,mBAAmB,CAAC,uBAAuB,CAAC,2CAA2C,CAAC,0BAA0B,CAAC,oBAAoB,qBAAqB,CAAC,UAAU,CAAC,mBAAmB,CAAC,2CAA2C,CAAC,yBAAyB,CAAC,OAAO,aAAa,CAAC,OAAO,aAAa,CAAC,OAAO,aAAa,CAAC,OAAO,aAAa,CAAC,OAAO,aAAa,CAAC,OAAO,aAAa,CAAC,OAAO,aAAa,CAAC,OAAO,aAAa,CAAC,OAAO,aAAa,CAAC,QAAQ,cAAc,CAAC,QAAQ,gBAAgB,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,OAAO,eAAe,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,OAAO,gBAAgB,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,OAAO,gBAAgB,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,OAAO,eAAe,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,QAAQ,aAAa,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,OAAO,iBAAiB,CAAC,YAAY,CAAC,OAAO,oBAAoB,CAAC,qCAAqC,CAAC,cAAc,CAAC,UAAU,iBAAiB,CAAC,OAAO,qCAAqC,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,4BAA4B,CAAC,mBAAmB,CAAC,WAAW,0CAA0C,CAAC,4FAA4F,CAAC,iDAAiD,CAAC,cAAc,UAAU,CAAC,uCAAuC,CAAC,eAAe,WAAW,CAAC,sCAAsC,CAAC,SAAS,sBAAsB,CAAC,4CAA4C,CAAC,wDAAwD,CAAC,kDAAkD,CAAC,sEAAsE,CAAC,gEAAgE,CAAC,WAAW,wBAAwB,CAAC,4CAA4C,CAAC,wDAAwD,CAAC,kDAAkD,CAAC,sEAAsE,CAAC,gFAAgF,CAAC,SAAS,sBAAsB,CAAC,sEAAsE,CAAC,4EAA4E,CAAC,uBAAuB,4CAA4C,CAAC,wDAAwD,CAAC,kDAAkD,CAAC,cAAc,2BAA2B,CAAC,sEAAsE,CAAC,4EAA4E,CAAC,SAAS,sBAAsB,CAAC,4CAA4C,CAAC,wDAAwD,CAAC,kDAAkD,CAAC,sEAAsE,CAAC,gEAAgE,CAAC,UAAU,uBAAuB,CAAC,kDAAkD,CAAC,sEAAsE,CAAC,2DAA2D,CAAC,mBAAmB,4CAA4C,CAAC,wDAAwD,CAAC,SAAS,sBAAsB,CAAC,kDAAkD,CAAC,sEAAsE,CAAC,2DAA2D,CAAC,iBAAiB,gCAAgC,CAAC,yBAAyB,sBAAsB,CAAC,wDAAwD,CAAC,kDAAkD,CAAC,sEAAsE,CAAC,6DAA6D,CAAC,uCAAuC,gGAAgG,oBAAoB,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC,mBAAmB,OAAO,kBAAkB,CAAC,IAAI,0CAA0C,CAAC,CAAC,qBAAqB,GAAG,gCAAgC,CAAC,IAAI,mGAAmG,CAAC,IAAI,6HAA6H,CAAC,IAAI,mGAAmG,CAAC,IAAI,+DAA+D,CAAC,IAAI,gCAAgC,CAAC,GAAG,gCAAgC,CAAC,CAAC,mBAAmB,IAAI,iCAAiC,CAAC,CAAC,wBAAwB,MAAM,sCAAsC,CAAC,kBAAkB,CAAC,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC,mBAAmB,IAAI,yGAAyG,CAAC,CAAC,oBAAoB,GAAG,wBAAwB,CAAC,GAAG,uBAAuB,CAAC,OAAO,wBAAwB,CAAC,QAAQ,uBAAuB,CAAC,IAAI,wBAAwB,CAAC,IAAI,uBAAuB,CAAC,IAAI,wBAAwB,CAAC,IAAI,uBAAuB,CAAC,OAAO,sBAAsB,CAAC,CAAC,mBAAmB,GAAG,sBAAsB,CAAC,GAAG,uBAAuB,CAAC,CAAC,cAAc,uBAAuB,CAAC,eAAe,wBAAwB,CAAC,eAAe,wBAAwB,CAAC,oBAAoB,oBAAoB,CAAC,kBAAkB,oBAAoB,CAAC,mDAAmD,mBAAmB,CAAC,cAAc,0CAA0C,CAAC,UAAU,oBAAoB,CAAC,qBAAqB,CAAC,UAAU,CAAC,iBAAiB,CAAC,WAAW,CAAC,0BAA0B,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,oCAAoC,CAAC,4BAA4B,UAAU,CAAC,YAAY,CAAC,4BAA4B,UAAU,CAAC,WAAW,CAAC,YAAY,4BAA4B,CAAC,sFAAsF,iBAAiB,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,cAAc,CAAC,4BAA4B,yCAAyC,CAAC,mCAAmC,CAAC,8BAA8B,2CAA2C,CAAC,0EAA0E,sCAAsC,CAAC,8CAA8C,mCAAmC,CAAC,oEAAoE,SAAS", "file": "svg-with-js.min.css", "sourcesContent": ["/*!\n * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license (Commercial License)\n * Copyright 2024 Fonticons, Inc.\n */\n:host,:root{--fa-font-solid:normal 900 1em/1 \"Font Awesome 6 Pro\";--fa-font-regular:normal 400 1em/1 \"Font Awesome 6 Pro\";--fa-font-light:normal 300 1em/1 \"Font Awesome 6 Pro\";--fa-font-thin:normal 100 1em/1 \"Font Awesome 6 Pro\";--fa-font-duotone:normal 900 1em/1 \"Font Awesome 6 Duotone\";--fa-font-duotone-regular:normal 400 1em/1 \"Font Awesome 6 Duotone\";--fa-font-duotone-light:normal 300 1em/1 \"Font Awesome 6 Duotone\";--fa-font-duotone-thin:normal 100 1em/1 \"Font Awesome 6 Duotone\";--fa-font-brands:normal 400 1em/1 \"Font Awesome 6 Brands\";--fa-font-sharp-solid:normal 900 1em/1 \"Font Awesome 6 Sharp\";--fa-font-sharp-regular:normal 400 1em/1 \"Font Awesome 6 Sharp\";--fa-font-sharp-light:normal 300 1em/1 \"Font Awesome 6 Sharp\";--fa-font-sharp-thin:normal 100 1em/1 \"Font Awesome 6 Sharp\";--fa-font-sharp-duotone-solid:normal 900 1em/1 \"Font Awesome 6 Sharp Duotone\";--fa-font-sharp-duotone-regular:normal 400 1em/1 \"Font Awesome 6 Sharp Duotone\";--fa-font-sharp-duotone-light:normal 300 1em/1 \"Font Awesome 6 Sharp Duotone\";--fa-font-sharp-duotone-thin:normal 100 1em/1 \"Font Awesome 6 Sharp Duotone\"}svg.svg-inline--fa:not(:host),svg.svg-inline--fa:not(:root){overflow:visible;box-sizing:initial}.svg-inline--fa{display:var(--fa-display,inline-block);height:1em;overflow:visible;vertical-align:-.125em}.svg-inline--fa.fa-2xs{vertical-align:.1em}.svg-inline--fa.fa-xs{vertical-align:0}.svg-inline--fa.fa-sm{vertical-align:-.07143em}.svg-inline--fa.fa-lg{vertical-align:-.2em}.svg-inline--fa.fa-xl{vertical-align:-.25em}.svg-inline--fa.fa-2xl{vertical-align:-.3125em}.svg-inline--fa.fa-pull-left{margin-right:var(--fa-pull-margin,.3em);width:auto}.svg-inline--fa.fa-pull-right{margin-left:var(--fa-pull-margin,.3em);width:auto}.svg-inline--fa.fa-li{width:var(--fa-li-width,2em);top:.25em}.svg-inline--fa.fa-fw{width:var(--fa-fw-width,1.25em)}.fa-layers svg.svg-inline--fa{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0}.fa-layers-counter,.fa-layers-text{display:inline-block;position:absolute;text-align:center}.fa-layers{display:inline-block;height:1em;position:relative;text-align:center;vertical-align:-.125em;width:1em}.fa-layers svg.svg-inline--fa{transform-origin:center center}.fa-layers-text{left:50%;top:50%;transform:translate(-50%,-50%);transform-origin:center center}.fa-layers-counter{background-color:var(--fa-counter-background-color,#ff253a);border-radius:var(--fa-counter-border-radius,1em);box-sizing:border-box;color:var(--fa-inverse,#fff);line-height:var(--fa-counter-line-height,1);max-width:var(--fa-counter-max-width,5em);min-width:var(--fa-counter-min-width,1.5em);overflow:hidden;padding:var(--fa-counter-padding,.25em .5em);right:var(--fa-right,0);text-overflow:ellipsis;top:var(--fa-top,0);transform:scale(var(--fa-counter-scale,.25));transform-origin:top right}.fa-layers-bottom-right{bottom:var(--fa-bottom,0);right:var(--fa-right,0);top:auto;transform:scale(var(--fa-layers-scale,.25));transform-origin:bottom right}.fa-layers-bottom-left{bottom:var(--fa-bottom,0);left:var(--fa-left,0);right:auto;top:auto;transform:scale(var(--fa-layers-scale,.25));transform-origin:bottom left}.fa-layers-top-right{top:var(--fa-top,0);right:var(--fa-right,0);transform:scale(var(--fa-layers-scale,.25));transform-origin:top right}.fa-layers-top-left{left:var(--fa-left,0);right:auto;top:var(--fa-top,0);transform:scale(var(--fa-layers-scale,.25));transform-origin:top left}.fa-1x{font-size:1em}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-6x{font-size:6em}.fa-7x{font-size:7em}.fa-8x{font-size:8em}.fa-9x{font-size:9em}.fa-10x{font-size:10em}.fa-2xs{font-size:.625em;line-height:.1em;vertical-align:.225em}.fa-xs{font-size:.75em;line-height:.08333em;vertical-align:.125em}.fa-sm{font-size:.875em;line-height:.07143em;vertical-align:.05357em}.fa-lg{font-size:1.25em;line-height:.05em;vertical-align:-.075em}.fa-xl{font-size:1.5em;line-height:.04167em;vertical-align:-.125em}.fa-2xl{font-size:2em;line-height:.03125em;vertical-align:-.1875em}.fa-fw{text-align:center;width:1.25em}.fa-ul{list-style-type:none;margin-left:var(--fa-li-margin,2.5em);padding-left:0}.fa-ul>li{position:relative}.fa-li{left:calc(var(--fa-li-width, 2em)*-1);position:absolute;text-align:center;width:var(--fa-li-width,2em);line-height:inherit}.fa-border{border-radius:var(--fa-border-radius,.1em);border:var(--fa-border-width,.08em) var(--fa-border-style,solid) var(--fa-border-color,#eee);padding:var(--fa-border-padding,.2em .25em .15em)}.fa-pull-left{float:left;margin-right:var(--fa-pull-margin,.3em)}.fa-pull-right{float:right;margin-left:var(--fa-pull-margin,.3em)}.fa-beat{animation-name:fa-beat;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,ease-in-out)}.fa-bounce{animation-name:fa-bounce;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,cubic-bezier(.28,.84,.42,1))}.fa-fade{animation-name:fa-fade;animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,cubic-bezier(.4,0,.6,1))}.fa-beat-fade,.fa-fade{animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s)}.fa-beat-fade{animation-name:fa-beat-fade;animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,cubic-bezier(.4,0,.6,1))}.fa-flip{animation-name:fa-flip;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,ease-in-out)}.fa-shake{animation-name:fa-shake;animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,linear)}.fa-shake,.fa-spin{animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal)}.fa-spin{animation-name:fa-spin;animation-duration:var(--fa-animation-duration,2s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,linear)}.fa-spin-reverse{--fa-animation-direction:reverse}.fa-pulse,.fa-spin-pulse{animation-name:fa-spin;animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,steps(8))}@media (prefers-reduced-motion:reduce){.fa-beat,.fa-beat-fade,.fa-bounce,.fa-fade,.fa-flip,.fa-pulse,.fa-shake,.fa-spin,.fa-spin-pulse{animation-delay:-1ms;animation-duration:1ms;animation-iteration-count:1;transition-delay:0s;transition-duration:0s}}@keyframes fa-beat{0%,90%{transform:scale(1)}45%{transform:scale(var(--fa-beat-scale,1.25))}}@keyframes fa-bounce{0%{transform:scale(1) translateY(0)}10%{transform:scale(var(--fa-bounce-start-scale-x,1.1),var(--fa-bounce-start-scale-y,.9)) translateY(0)}30%{transform:scale(var(--fa-bounce-jump-scale-x,.9),var(--fa-bounce-jump-scale-y,1.1)) translateY(var(--fa-bounce-height,-.5em))}50%{transform:scale(var(--fa-bounce-land-scale-x,1.05),var(--fa-bounce-land-scale-y,.95)) translateY(0)}57%{transform:scale(1) translateY(var(--fa-bounce-rebound,-.125em))}64%{transform:scale(1) translateY(0)}to{transform:scale(1) translateY(0)}}@keyframes fa-fade{50%{opacity:var(--fa-fade-opacity,.4)}}@keyframes fa-beat-fade{0%,to{opacity:var(--fa-beat-fade-opacity,.4);transform:scale(1)}50%{opacity:1;transform:scale(var(--fa-beat-fade-scale,1.125))}}@keyframes fa-flip{50%{transform:rotate3d(var(--fa-flip-x,0),var(--fa-flip-y,1),var(--fa-flip-z,0),var(--fa-flip-angle,-180deg))}}@keyframes fa-shake{0%{transform:rotate(-15deg)}4%{transform:rotate(15deg)}8%,24%{transform:rotate(-18deg)}12%,28%{transform:rotate(18deg)}16%{transform:rotate(-22deg)}20%{transform:rotate(22deg)}32%{transform:rotate(-12deg)}36%{transform:rotate(12deg)}40%,to{transform:rotate(0deg)}}@keyframes fa-spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}.fa-rotate-90{transform:rotate(90deg)}.fa-rotate-180{transform:rotate(180deg)}.fa-rotate-270{transform:rotate(270deg)}.fa-flip-horizontal{transform:scaleX(-1)}.fa-flip-vertical{transform:scaleY(-1)}.fa-flip-both,.fa-flip-horizontal.fa-flip-vertical{transform:scale(-1)}.fa-rotate-by{transform:rotate(var(--fa-rotate-angle,0))}.fa-stack{display:inline-block;vertical-align:middle;height:2em;position:relative;width:2.5em}.fa-stack-1x,.fa-stack-2x{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;z-index:var(--fa-stack-z-index,auto)}.svg-inline--fa.fa-stack-1x{height:1em;width:1.25em}.svg-inline--fa.fa-stack-2x{height:2em;width:2.5em}.fa-inverse{color:var(--fa-inverse,#fff)}.fa-sr-only,.fa-sr-only-focusable:not(:focus),.sr-only,.sr-only-focusable:not(:focus){position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.svg-inline--fa .fa-primary{fill:var(--fa-primary-color,currentColor);opacity:var(--fa-primary-opacity,1)}.svg-inline--fa .fa-secondary{fill:var(--fa-secondary-color,currentColor)}.svg-inline--fa .fa-secondary,.svg-inline--fa.fa-swap-opacity .fa-primary{opacity:var(--fa-secondary-opacity,.4)}.svg-inline--fa.fa-swap-opacity .fa-secondary{opacity:var(--fa-primary-opacity,1)}.svg-inline--fa mask .fa-primary,.svg-inline--fa mask .fa-secondary{fill:#000}"]}