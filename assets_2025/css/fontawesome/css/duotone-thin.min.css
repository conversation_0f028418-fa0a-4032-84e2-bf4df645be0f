/*!
 * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 * Copyright 2024 Fonticons, Inc.
 */
:host,:root{--fa-style-family-duotone:"Font Awesome 6 Duotone";--fa-font-duotone-thin:normal 100 1em/1 "Font Awesome 6 Duotone"}@font-face{font-family:"Font Awesome 6 Duotone";font-style:normal;font-weight:100;font-display:block;src:url(../webfonts/fa-duotone-thin-100.woff2) format("woff2"),url(../webfonts/fa-duotone-thin-100.ttf) format("truetype")}.fa-duotone.fa-thin,.fadt{position:relative;font-weight:100;letter-spacing:normal}.fa-duotone.fa-thin:before,.fadt:before{position:absolute;color:var(--fa-primary-color,inherit);opacity:var(--fa-primary-opacity,1)}.fa-duotone.fa-thin:after,.fadt:after{color:var(--fa-secondary-color,inherit)}.fa-duotone.fa-swap-opacity:before,.fa-duotone.fa-thin.fa-swap-opacity:before,.fa-duotone.fa-thin:after,.fa-swap-opacity .fa-duotone.fa-thin:before,.fa-swap-opacity .fadt:before,.fadt.fa-swap-opacity:before,.fadt:after{opacity:var(--fa-secondary-opacity,.4)}.fa-duotone.fa-swap-opacity:after,.fa-duotone.fa-thin.fa-swap-opacity:after,.fa-swap-opacity .fa-duotone.fa-thin:after,.fa-swap-opacity .fadt:after,.fadt.fa-swap-opacity:after{opacity:var(--fa-primary-opacity,1)}.fa-duotone.fa-thin.fa-inverse,.fadt.fa-inverse{color:var(--fa-inverse,#fff)}.fa-duotone.fa-thin.fa-stack-1x,.fa-duotone.fa-thin.fa-stack-2x,.fadt.fa-stack-1x,.fadt.fa-stack-2x{position:absolute}
/*# sourceMappingURL=duotone-thin.min.css.map */