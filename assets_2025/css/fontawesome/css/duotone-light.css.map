{"version": 3, "sources": ["duotone-light.css"], "names": [], "mappings": "AAAA;;;;EAIE;AACF;EACE,mDAAmD;EACnD,kEAAkE,EAAE;;AAEtE;EACE,qCAAqC;EACrC,kBAAkB;EAClB,gBAAgB;EAChB,mBAAmB;EACnB,kIAAkI,EAAE;;AAEtI;;EAEE,kBAAkB;EAClB,gBAAgB;EAChB,sBAAsB,EAAE;;AAE1B;;EAEE,kBAAkB;EAClB,uCAAuC;EACvC,qCAAqC,EAAE;;AAEzC;;EAEE,yCAAyC;EACzC,yCAAyC,EAAE;;AAE7C;;;;;EAKE,yCAAyC,EAAE;;AAE7C;;;;;EAKE,qCAAqC,EAAE;;AAEzC;;EAEE,8BAA8B,EAAE;;AAElC;;;;EAIE,kBAAkB,EAAE", "file": "duotone-light.css", "sourcesContent": ["/*!\n * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license (Commercial License)\n * Copyright 2024 Fonticons, Inc.\n */\n:root, :host {\n  --fa-style-family-duotone: 'Font Awesome 6 Duotone';\n  --fa-font-duotone-light: normal 300 1em/1 'Font Awesome 6 Duotone'; }\n\n@font-face {\n  font-family: 'Font Awesome 6 Duotone';\n  font-style: normal;\n  font-weight: 300;\n  font-display: block;\n  src: url(\"../webfonts/fa-duotone-light-300.woff2\") format(\"woff2\"), url(\"../webfonts/fa-duotone-light-300.ttf\") format(\"truetype\"); }\n\n.fadl,\n.fa-duotone.fa-light {\n  position: relative;\n  font-weight: 300;\n  letter-spacing: normal; }\n\n.fadl::before,\n.fa-duotone.fa-light::before {\n  position: absolute;\n  color: var(--fa-primary-color, inherit);\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fadl::after,\n.fa-duotone.fa-light::after {\n  color: var(--fa-secondary-color, inherit);\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fadl::before,\n.fa-swap-opacity .fa-duotone.fa-light::before,\n.fadl.fa-swap-opacity::before,\n.fa-duotone.fa-swap-opacity::before,\n.fa-duotone.fa-light.fa-swap-opacity::before {\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fadl::after,\n.fa-swap-opacity .fa-duotone.fa-light::after,\n.fadl.fa-swap-opacity::after,\n.fa-duotone.fa-swap-opacity::after,\n.fa-duotone.fa-light.fa-swap-opacity::after {\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fadl.fa-inverse,\n.fa-duotone.fa-light.fa-inverse {\n  color: var(--fa-inverse, #fff); }\n\n.fadl.fa-stack-1x,\n.fadl.fa-stack-2x,\n.fa-duotone.fa-light.fa-stack-1x,\n.fa-duotone.fa-light.fa-stack-2x {\n  position: absolute; }\n"]}