{"version": 3, "sources": ["duotone-regular.css"], "names": [], "mappings": "AAAA;;;;EAIE;AACF;EACE,mDAAmD;EACnD,oEAAoE,EAAE;;AAExE;EACE,qCAAqC;EACrC,kBAAkB;EAClB,gBAAgB;EAChB,mBAAmB;EACnB,sIAAsI,EAAE;;AAE1I;;EAEE,kBAAkB;EAClB,gBAAgB;EAChB,sBAAsB,EAAE;;AAE1B;;EAEE,kBAAkB;EAClB,uCAAuC;EACvC,qCAAqC,EAAE;;AAEzC;;EAEE,yCAAyC;EACzC,yCAAyC,EAAE;;AAE7C;;;;;EAKE,yCAAyC,EAAE;;AAE7C;;;;;EAKE,qCAAqC,EAAE;;AAEzC;;EAEE,8BAA8B,EAAE;;AAElC;;;;EAIE,kBAAkB,EAAE", "file": "duotone-regular.css", "sourcesContent": ["/*!\n * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license (Commercial License)\n * Copyright 2024 Fonticons, Inc.\n */\n:root, :host {\n  --fa-style-family-duotone: 'Font Awesome 6 Duotone';\n  --fa-font-duotone-regular: normal 400 1em/1 'Font Awesome 6 Duotone'; }\n\n@font-face {\n  font-family: 'Font Awesome 6 Duotone';\n  font-style: normal;\n  font-weight: 400;\n  font-display: block;\n  src: url(\"../webfonts/fa-duotone-regular-400.woff2\") format(\"woff2\"), url(\"../webfonts/fa-duotone-regular-400.ttf\") format(\"truetype\"); }\n\n.fadr,\n.fa-duotone.fa-regular {\n  position: relative;\n  font-weight: 400;\n  letter-spacing: normal; }\n\n.fadr::before,\n.fa-duotone.fa-regular::before {\n  position: absolute;\n  color: var(--fa-primary-color, inherit);\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fadr::after,\n.fa-duotone.fa-regular::after {\n  color: var(--fa-secondary-color, inherit);\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fadr::before,\n.fa-swap-opacity .fa-duotone.fa-regular::before,\n.fadr.fa-swap-opacity::before,\n.fa-duotone.fa-swap-opacity::before,\n.fa-duotone.fa-regular.fa-swap-opacity::before {\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fadr::after,\n.fa-swap-opacity .fa-duotone.fa-regular::after,\n.fadr.fa-swap-opacity::after,\n.fa-duotone.fa-swap-opacity::after,\n.fa-duotone.fa-regular.fa-swap-opacity::after {\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fadr.fa-inverse,\n.fa-duotone.fa-regular.fa-inverse {\n  color: var(--fa-inverse, #fff); }\n\n.fadr.fa-stack-1x,\n.fadr.fa-stack-2x,\n.fa-duotone.fa-regular.fa-stack-1x,\n.fa-duotone.fa-regular.fa-stack-2x {\n  position: absolute; }\n"]}