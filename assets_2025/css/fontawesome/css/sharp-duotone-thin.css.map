{"version": 3, "sources": ["sharp-duotone-thin.css"], "names": [], "mappings": "AAAA;;;;EAIE;AACF;EACE,+DAA+D;EAC/D,6EAA6E,EAAE;;AAEjF;EACE,2CAA2C;EAC3C,kBAAkB;EAClB,gBAAgB;EAChB,mBAAmB;EACnB,4IAA4I,EAAE;;AAEhJ;;EAEE,kBAAkB;EAClB,gBAAgB;EAChB,sBAAsB,EAAE;;AAE1B;;EAEE,kBAAkB;EAClB,uCAAuC;EACvC,qCAAqC,EAAE;;AAEzC;;EAEE,yCAAyC;EACzC,yCAAyC,EAAE;;AAE7C;;;;;EAKE,yCAAyC,EAAE;;AAE7C;;;;;EAKE,qCAAqC,EAAE;;AAEzC;;EAEE,8BAA8B,EAAE;;AAElC;;;;EAIE,kBAAkB,EAAE", "file": "sharp-duotone-thin.css", "sourcesContent": ["/*!\n * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license (Commercial License)\n * Copyright 2024 Fonticons, Inc.\n */\n:root, :host {\n  --fa-style-family-sharp-duotone: 'Font Awesome 6 Sharp Duotone';\n  --fa-font-sharp-duotone-thin: normal 100 1em/1 'Font Awesome 6 Sharp Duotone'; }\n\n@font-face {\n  font-family: 'Font Awesome 6 Sharp Duotone';\n  font-style: normal;\n  font-weight: 100;\n  font-display: block;\n  src: url(\"../webfonts/fa-sharp-duotone-thin-100.woff2\") format(\"woff2\"), url(\"../webfonts/fa-sharp-duotone-thin-100.ttf\") format(\"truetype\"); }\n\n.fasdt,\n.fa-sharp-duotone.fa-thin {\n  position: relative;\n  font-weight: 100;\n  letter-spacing: normal; }\n\n.fasdt::before,\n.fa-sharp-duotone.fa-thin::before {\n  position: absolute;\n  color: var(--fa-primary-color, inherit);\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fasdt::after,\n.fa-sharp-duotone.fa-thin::after {\n  color: var(--fa-secondary-color, inherit);\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fasdt::before,\n.fa-swap-opacity .fa-sharp-duotone.fa-thin::before,\n.fasdt.fa-swap-opacity::before,\n.fa-sharp-duotone.fa-swap-opacity::before,\n.fa-sharp-duotone.fa-thin.fa-swap-opacity::before {\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.fa-swap-opacity .fasdt::after,\n.fa-swap-opacity .fa-sharp-duotone.fa-thin::after,\n.fasdt.fa-swap-opacity::after,\n.fa-sharp-duotone.fa-swap-opacity::after,\n.fa-sharp-duotone.fa-thin.fa-swap-opacity::after {\n  opacity: var(--fa-primary-opacity, 1); }\n\n.fasdt.fa-inverse,\n.fa-sharp-duotone.fa-thin.fa-inverse {\n  color: var(--fa-inverse, #fff); }\n\n.fasdt.fa-stack-1x,\n.fasdt.fa-stack-2x,\n.fa-sharp-duotone.fa-thin.fa-stack-1x,\n.fa-sharp-duotone.fa-thin.fa-stack-2x {\n  position: absolute; }\n"]}