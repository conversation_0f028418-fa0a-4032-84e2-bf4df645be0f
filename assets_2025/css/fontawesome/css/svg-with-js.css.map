{"version": 3, "sources": ["svg-with-js.css"], "names": [], "mappings": "AAAA;;;;EAIE;AACF;EACE,sDAAsD;EACtD,wDAAwD;EACxD,sDAAsD;EACtD,qDAAqD;EACrD,4DAA4D;EAC5D,oEAAoE;EACpE,kEAAkE;EAClE,iEAAiE;EACjE,0DAA0D;EAC1D,8DAA8D;EAC9D,gEAAgE;EAChE,8DAA8D;EAC9D,6DAA6D;EAC7D,8EAA8E;EAC9E,gFAAgF;EAChF,8EAA8E;EAC9E,6EAA6E,EAAE;;AAEjF;EACE,iBAAiB;EACjB,uBAAuB,EAAE;;AAE3B;EACE,wCAAwC;EACxC,WAAW;EACX,iBAAiB;EACjB,uBAAuB,EAAE;EACzB;IACE,qBAAqB,EAAE;EACzB;IACE,mBAAmB,EAAE;EACvB;IACE,0BAA0B,EAAE;EAC9B;IACE,sBAAsB,EAAE;EAC1B;IACE,uBAAuB,EAAE;EAC3B;IACE,yBAAyB,EAAE;EAC7B;IACE,0CAA0C;IAC1C,WAAW,EAAE;EACf;IACE,yCAAyC;IACzC,WAAW,EAAE;EACf;IACE,8BAA8B;IAC9B,WAAW,EAAE;EACf;IACE,iCAAiC,EAAE;;AAEvC;EACE,SAAS;EACT,OAAO;EACP,YAAY;EACZ,kBAAkB;EAClB,QAAQ;EACR,MAAM,EAAE;;AAEV;EACE,qBAAqB;EACrB,kBAAkB;EAClB,kBAAkB,EAAE;;AAEtB;EACE,qBAAqB;EACrB,WAAW;EACX,kBAAkB;EAClB,kBAAkB;EAClB,uBAAuB;EACvB,UAAU,EAAE;EACZ;IACE,+BAA+B,EAAE;;AAErC;EACE,SAAS;EACT,QAAQ;EACR,gCAAgC;EAChC,+BAA+B,EAAE;;AAEnC;EACE,6DAA6D;EAC7D,mDAAmD;EACnD,sBAAsB;EACtB,8BAA8B;EAC9B,6CAA6C;EAC7C,2CAA2C;EAC3C,6CAA6C;EAC7C,gBAAgB;EAChB,gDAAgD;EAChD,yBAAyB;EACzB,uBAAuB;EACvB,qBAAqB;EACrB,+CAA+C;EAC/C,2BAA2B,EAAE;;AAE/B;EACE,2BAA2B;EAC3B,yBAAyB;EACzB,SAAS;EACT,8CAA8C;EAC9C,8BAA8B,EAAE;;AAElC;EACE,2BAA2B;EAC3B,uBAAuB;EACvB,WAAW;EACX,SAAS;EACT,8CAA8C;EAC9C,6BAA6B,EAAE;;AAEjC;EACE,qBAAqB;EACrB,yBAAyB;EACzB,8CAA8C;EAC9C,2BAA2B,EAAE;;AAE/B;EACE,uBAAuB;EACvB,WAAW;EACX,qBAAqB;EACrB,8CAA8C;EAC9C,0BAA0B,EAAE;;AAE9B;EACE,cAAc,EAAE;;AAElB;EACE,cAAc,EAAE;;AAElB;EACE,cAAc,EAAE;;AAElB;EACE,cAAc,EAAE;;AAElB;EACE,cAAc,EAAE;;AAElB;EACE,cAAc,EAAE;;AAElB;EACE,cAAc,EAAE;;AAElB;EACE,cAAc,EAAE;;AAElB;EACE,cAAc,EAAE;;AAElB;EACE,eAAe,EAAE;;AAEnB;EACE,kBAAkB;EAClB,kBAAkB;EAClB,uBAAuB,EAAE;;AAE3B;EACE,iBAAiB;EACjB,sBAAsB;EACtB,uBAAuB,EAAE;;AAE3B;EACE,kBAAkB;EAClB,sBAAsB;EACtB,yBAAyB,EAAE;;AAE7B;EACE,iBAAiB;EACjB,mBAAmB;EACnB,wBAAwB,EAAE;;AAE5B;EACE,gBAAgB;EAChB,sBAAsB;EACtB,wBAAwB,EAAE;;AAE5B;EACE,cAAc;EACd,sBAAsB;EACtB,yBAAyB,EAAE;;AAE7B;EACE,kBAAkB;EAClB,aAAa,EAAE;;AAEjB;EACE,qBAAqB;EACrB,uCAAuC;EACvC,eAAe,EAAE;EACjB;IACE,kBAAkB,EAAE;;AAExB;EACE,wCAAwC;EACxC,kBAAkB;EAClB,kBAAkB;EAClB,8BAA8B;EAC9B,oBAAoB,EAAE;;AAExB;EACE,0CAA0C;EAC1C,6CAA6C;EAC7C,2CAA2C;EAC3C,4CAA4C;EAC5C,sDAAsD,EAAE;;AAE1D;EACE,WAAW;EACX,0CAA0C,EAAE;;AAE9C;EACE,YAAY;EACZ,yCAAyC,EAAE;;AAE7C;EACE,uBAAuB;EACvB,8CAA8C;EAC9C,0DAA0D;EAC1D,oDAAoD;EACpD,wEAAwE;EACxE,kEAAkE,EAAE;;AAEtE;EACE,yBAAyB;EACzB,8CAA8C;EAC9C,0DAA0D;EAC1D,oDAAoD;EACpD,wEAAwE;EACxE,wFAAwF,EAAE;;AAE5F;EACE,uBAAuB;EACvB,8CAA8C;EAC9C,0DAA0D;EAC1D,oDAAoD;EACpD,wEAAwE;EACxE,mFAAmF,EAAE;;AAEvF;EACE,4BAA4B;EAC5B,8CAA8C;EAC9C,0DAA0D;EAC1D,oDAAoD;EACpD,wEAAwE;EACxE,mFAAmF,EAAE;;AAEvF;EACE,uBAAuB;EACvB,8CAA8C;EAC9C,0DAA0D;EAC1D,oDAAoD;EACpD,wEAAwE;EACxE,kEAAkE,EAAE;;AAEtE;EACE,wBAAwB;EACxB,8CAA8C;EAC9C,0DAA0D;EAC1D,oDAAoD;EACpD,wEAAwE;EACxE,6DAA6D,EAAE;;AAEjE;EACE,uBAAuB;EACvB,8CAA8C;EAC9C,0DAA0D;EAC1D,oDAAoD;EACpD,wEAAwE;EACxE,6DAA6D,EAAE;;AAEjE;EACE,iCAAiC,EAAE;;AAErC;;EAEE,uBAAuB;EACvB,0DAA0D;EAC1D,oDAAoD;EACpD,wEAAwE;EACxE,+DAA+D,EAAE;;AAEnE;EACE;;;;;;;;;IASE,qBAAqB;IACrB,uBAAuB;IACvB,4BAA4B;IAC5B,oBAAoB;IACpB,uBAAuB,EAAE,EAAE;;AAE/B;EACE;IACE,mBAAmB,EAAE;EACvB;IACE,4CAA4C,EAAE,EAAE;;AAEpD;EACE;IACE,oCAAoC,EAAE;EACxC;IACE,wGAAwG,EAAE;EAC5G;IACE,oIAAoI,EAAE;EACxI;IACE,wGAAwG,EAAE;EAC5G;IACE,qEAAqE,EAAE;EACzE;IACE,oCAAoC,EAAE;EACxC;IACE,oCAAoC,EAAE,EAAE;;AAE5C;EACE;IACE,oCAAoC,EAAE,EAAE;;AAE5C;EACE;IACE,yCAAyC;IACzC,mBAAmB,EAAE;EACvB;IACE,UAAU;IACV,kDAAkD,EAAE,EAAE;;AAE1D;EACE;IACE,iHAAiH,EAAE,EAAE;;AAEzH;EACE;IACE,yBAAyB,EAAE;EAC7B;IACE,wBAAwB,EAAE;EAC5B;IACE,yBAAyB,EAAE;EAC7B;IACE,wBAAwB,EAAE;EAC5B;IACE,yBAAyB,EAAE;EAC7B;IACE,wBAAwB,EAAE;EAC5B;IACE,yBAAyB,EAAE;EAC7B;IACE,wBAAwB,EAAE;EAC5B;IACE,uBAAuB,EAAE,EAAE;;AAE/B;EACE;IACE,uBAAuB,EAAE;EAC3B;IACE,yBAAyB,EAAE,EAAE;;AAEjC;EACE,wBAAwB,EAAE;;AAE5B;EACE,yBAAyB,EAAE;;AAE7B;EACE,yBAAyB,EAAE;;AAE7B;EACE,uBAAuB,EAAE;;AAE3B;EACE,uBAAuB,EAAE;;AAE3B;;EAEE,wBAAwB,EAAE;;AAE5B;EACE,4CAA4C,EAAE;;AAEhD;EACE,qBAAqB;EACrB,sBAAsB;EACtB,WAAW;EACX,kBAAkB;EAClB,YAAY,EAAE;;AAEhB;;EAEE,SAAS;EACT,OAAO;EACP,YAAY;EACZ,kBAAkB;EAClB,QAAQ;EACR,MAAM;EACN,sCAAsC,EAAE;;AAE1C;EACE,WAAW;EACX,aAAa,EAAE;;AAEjB;EACE,WAAW;EACX,YAAY,EAAE;;AAEhB;EACE,8BAA8B,EAAE;;AAElC;;EAEE,kBAAkB;EAClB,UAAU;EACV,WAAW;EACX,UAAU;EACV,YAAY;EACZ,gBAAgB;EAChB,sBAAsB;EACtB,mBAAmB;EACnB,eAAe,EAAE;;AAEnB;;EAEE,kBAAkB;EAClB,UAAU;EACV,WAAW;EACX,UAAU;EACV,YAAY;EACZ,gBAAgB;EAChB,sBAAsB;EACtB,mBAAmB;EACnB,eAAe,EAAE;;AAEnB;EACE,2CAA2C;EAC3C,qCAAqC,EAAE;;AAEzC;EACE,6CAA6C;EAC7C,yCAAyC,EAAE;;AAE7C;EACE,yCAAyC,EAAE;;AAE7C;EACE,qCAAqC,EAAE;;AAEzC;;EAEE,WAAW,EAAE", "file": "svg-with-js.css", "sourcesContent": ["/*!\n * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license (Commercial License)\n * Copyright 2024 Fonticons, Inc.\n */\n:root, :host {\n  --fa-font-solid: normal 900 1em/1 'Font Awesome 6 Pro';\n  --fa-font-regular: normal 400 1em/1 'Font Awesome 6 Pro';\n  --fa-font-light: normal 300 1em/1 'Font Awesome 6 Pro';\n  --fa-font-thin: normal 100 1em/1 'Font Awesome 6 Pro';\n  --fa-font-duotone: normal 900 1em/1 'Font Awesome 6 Duotone';\n  --fa-font-duotone-regular: normal 400 1em/1 'Font Awesome 6 Duotone';\n  --fa-font-duotone-light: normal 300 1em/1 'Font Awesome 6 Duotone';\n  --fa-font-duotone-thin: normal 100 1em/1 'Font Awesome 6 Duotone';\n  --fa-font-brands: normal 400 1em/1 'Font Awesome 6 Brands';\n  --fa-font-sharp-solid: normal 900 1em/1 'Font Awesome 6 Sharp';\n  --fa-font-sharp-regular: normal 400 1em/1 'Font Awesome 6 Sharp';\n  --fa-font-sharp-light: normal 300 1em/1 'Font Awesome 6 Sharp';\n  --fa-font-sharp-thin: normal 100 1em/1 'Font Awesome 6 Sharp';\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 'Font Awesome 6 Sharp Duotone';\n  --fa-font-sharp-duotone-regular: normal 400 1em/1 'Font Awesome 6 Sharp Duotone';\n  --fa-font-sharp-duotone-light: normal 300 1em/1 'Font Awesome 6 Sharp Duotone';\n  --fa-font-sharp-duotone-thin: normal 100 1em/1 'Font Awesome 6 Sharp Duotone'; }\n\nsvg.svg-inline--fa:not(:root), svg.svg-inline--fa:not(:host) {\n  overflow: visible;\n  box-sizing: content-box; }\n\n.svg-inline--fa {\n  display: var(--fa-display, inline-block);\n  height: 1em;\n  overflow: visible;\n  vertical-align: -.125em; }\n  .svg-inline--fa.fa-2xs {\n    vertical-align: 0.1em; }\n  .svg-inline--fa.fa-xs {\n    vertical-align: 0em; }\n  .svg-inline--fa.fa-sm {\n    vertical-align: -0.07143em; }\n  .svg-inline--fa.fa-lg {\n    vertical-align: -0.2em; }\n  .svg-inline--fa.fa-xl {\n    vertical-align: -0.25em; }\n  .svg-inline--fa.fa-2xl {\n    vertical-align: -0.3125em; }\n  .svg-inline--fa.fa-pull-left {\n    margin-right: var(--fa-pull-margin, 0.3em);\n    width: auto; }\n  .svg-inline--fa.fa-pull-right {\n    margin-left: var(--fa-pull-margin, 0.3em);\n    width: auto; }\n  .svg-inline--fa.fa-li {\n    width: var(--fa-li-width, 2em);\n    top: 0.25em; }\n  .svg-inline--fa.fa-fw {\n    width: var(--fa-fw-width, 1.25em); }\n\n.fa-layers svg.svg-inline--fa {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0; }\n\n.fa-layers-counter, .fa-layers-text {\n  display: inline-block;\n  position: absolute;\n  text-align: center; }\n\n.fa-layers {\n  display: inline-block;\n  height: 1em;\n  position: relative;\n  text-align: center;\n  vertical-align: -.125em;\n  width: 1em; }\n  .fa-layers svg.svg-inline--fa {\n    transform-origin: center center; }\n\n.fa-layers-text {\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  transform-origin: center center; }\n\n.fa-layers-counter {\n  background-color: var(--fa-counter-background-color, #ff253a);\n  border-radius: var(--fa-counter-border-radius, 1em);\n  box-sizing: border-box;\n  color: var(--fa-inverse, #fff);\n  line-height: var(--fa-counter-line-height, 1);\n  max-width: var(--fa-counter-max-width, 5em);\n  min-width: var(--fa-counter-min-width, 1.5em);\n  overflow: hidden;\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\n  right: var(--fa-right, 0);\n  text-overflow: ellipsis;\n  top: var(--fa-top, 0);\n  transform: scale(var(--fa-counter-scale, 0.25));\n  transform-origin: top right; }\n\n.fa-layers-bottom-right {\n  bottom: var(--fa-bottom, 0);\n  right: var(--fa-right, 0);\n  top: auto;\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: bottom right; }\n\n.fa-layers-bottom-left {\n  bottom: var(--fa-bottom, 0);\n  left: var(--fa-left, 0);\n  right: auto;\n  top: auto;\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: bottom left; }\n\n.fa-layers-top-right {\n  top: var(--fa-top, 0);\n  right: var(--fa-right, 0);\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: top right; }\n\n.fa-layers-top-left {\n  left: var(--fa-left, 0);\n  right: auto;\n  top: var(--fa-top, 0);\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: top left; }\n\n.fa-1x {\n  font-size: 1em; }\n\n.fa-2x {\n  font-size: 2em; }\n\n.fa-3x {\n  font-size: 3em; }\n\n.fa-4x {\n  font-size: 4em; }\n\n.fa-5x {\n  font-size: 5em; }\n\n.fa-6x {\n  font-size: 6em; }\n\n.fa-7x {\n  font-size: 7em; }\n\n.fa-8x {\n  font-size: 8em; }\n\n.fa-9x {\n  font-size: 9em; }\n\n.fa-10x {\n  font-size: 10em; }\n\n.fa-2xs {\n  font-size: 0.625em;\n  line-height: 0.1em;\n  vertical-align: 0.225em; }\n\n.fa-xs {\n  font-size: 0.75em;\n  line-height: 0.08333em;\n  vertical-align: 0.125em; }\n\n.fa-sm {\n  font-size: 0.875em;\n  line-height: 0.07143em;\n  vertical-align: 0.05357em; }\n\n.fa-lg {\n  font-size: 1.25em;\n  line-height: 0.05em;\n  vertical-align: -0.075em; }\n\n.fa-xl {\n  font-size: 1.5em;\n  line-height: 0.04167em;\n  vertical-align: -0.125em; }\n\n.fa-2xl {\n  font-size: 2em;\n  line-height: 0.03125em;\n  vertical-align: -0.1875em; }\n\n.fa-fw {\n  text-align: center;\n  width: 1.25em; }\n\n.fa-ul {\n  list-style-type: none;\n  margin-left: var(--fa-li-margin, 2.5em);\n  padding-left: 0; }\n  .fa-ul > li {\n    position: relative; }\n\n.fa-li {\n  left: calc(-1 * var(--fa-li-width, 2em));\n  position: absolute;\n  text-align: center;\n  width: var(--fa-li-width, 2em);\n  line-height: inherit; }\n\n.fa-border {\n  border-color: var(--fa-border-color, #eee);\n  border-radius: var(--fa-border-radius, 0.1em);\n  border-style: var(--fa-border-style, solid);\n  border-width: var(--fa-border-width, 0.08em);\n  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em); }\n\n.fa-pull-left {\n  float: left;\n  margin-right: var(--fa-pull-margin, 0.3em); }\n\n.fa-pull-right {\n  float: right;\n  margin-left: var(--fa-pull-margin, 0.3em); }\n\n.fa-beat {\n  animation-name: fa-beat;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, ease-in-out); }\n\n.fa-bounce {\n  animation-name: fa-bounce;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1)); }\n\n.fa-fade {\n  animation-name: fa-fade;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1)); }\n\n.fa-beat-fade {\n  animation-name: fa-beat-fade;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1)); }\n\n.fa-flip {\n  animation-name: fa-flip;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, ease-in-out); }\n\n.fa-shake {\n  animation-name: fa-shake;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, linear); }\n\n.fa-spin {\n  animation-name: fa-spin;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 2s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, linear); }\n\n.fa-spin-reverse {\n  --fa-animation-direction: reverse; }\n\n.fa-pulse,\n.fa-spin-pulse {\n  animation-name: fa-spin;\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, steps(8)); }\n\n@media (prefers-reduced-motion: reduce) {\n  .fa-beat,\n  .fa-bounce,\n  .fa-fade,\n  .fa-beat-fade,\n  .fa-flip,\n  .fa-pulse,\n  .fa-shake,\n  .fa-spin,\n  .fa-spin-pulse {\n    animation-delay: -1ms;\n    animation-duration: 1ms;\n    animation-iteration-count: 1;\n    transition-delay: 0s;\n    transition-duration: 0s; } }\n\n@keyframes fa-beat {\n  0%, 90% {\n    transform: scale(1); }\n  45% {\n    transform: scale(var(--fa-beat-scale, 1.25)); } }\n\n@keyframes fa-bounce {\n  0% {\n    transform: scale(1, 1) translateY(0); }\n  10% {\n    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0); }\n  30% {\n    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em)); }\n  50% {\n    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0); }\n  57% {\n    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em)); }\n  64% {\n    transform: scale(1, 1) translateY(0); }\n  100% {\n    transform: scale(1, 1) translateY(0); } }\n\n@keyframes fa-fade {\n  50% {\n    opacity: var(--fa-fade-opacity, 0.4); } }\n\n@keyframes fa-beat-fade {\n  0%, 100% {\n    opacity: var(--fa-beat-fade-opacity, 0.4);\n    transform: scale(1); }\n  50% {\n    opacity: 1;\n    transform: scale(var(--fa-beat-fade-scale, 1.125)); } }\n\n@keyframes fa-flip {\n  50% {\n    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg)); } }\n\n@keyframes fa-shake {\n  0% {\n    transform: rotate(-15deg); }\n  4% {\n    transform: rotate(15deg); }\n  8%, 24% {\n    transform: rotate(-18deg); }\n  12%, 28% {\n    transform: rotate(18deg); }\n  16% {\n    transform: rotate(-22deg); }\n  20% {\n    transform: rotate(22deg); }\n  32% {\n    transform: rotate(-12deg); }\n  36% {\n    transform: rotate(12deg); }\n  40%, 100% {\n    transform: rotate(0deg); } }\n\n@keyframes fa-spin {\n  0% {\n    transform: rotate(0deg); }\n  100% {\n    transform: rotate(360deg); } }\n\n.fa-rotate-90 {\n  transform: rotate(90deg); }\n\n.fa-rotate-180 {\n  transform: rotate(180deg); }\n\n.fa-rotate-270 {\n  transform: rotate(270deg); }\n\n.fa-flip-horizontal {\n  transform: scale(-1, 1); }\n\n.fa-flip-vertical {\n  transform: scale(1, -1); }\n\n.fa-flip-both,\n.fa-flip-horizontal.fa-flip-vertical {\n  transform: scale(-1, -1); }\n\n.fa-rotate-by {\n  transform: rotate(var(--fa-rotate-angle, 0)); }\n\n.fa-stack {\n  display: inline-block;\n  vertical-align: middle;\n  height: 2em;\n  position: relative;\n  width: 2.5em; }\n\n.fa-stack-1x,\n.fa-stack-2x {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: var(--fa-stack-z-index, auto); }\n\n.svg-inline--fa.fa-stack-1x {\n  height: 1em;\n  width: 1.25em; }\n\n.svg-inline--fa.fa-stack-2x {\n  height: 2em;\n  width: 2.5em; }\n\n.fa-inverse {\n  color: var(--fa-inverse, #fff); }\n\n.sr-only,\n.fa-sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0; }\n\n.sr-only-focusable:not(:focus),\n.fa-sr-only-focusable:not(:focus) {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0; }\n\n.svg-inline--fa .fa-primary {\n  fill: var(--fa-primary-color, currentColor);\n  opacity: var(--fa-primary-opacity, 1); }\n\n.svg-inline--fa .fa-secondary {\n  fill: var(--fa-secondary-color, currentColor);\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.svg-inline--fa.fa-swap-opacity .fa-primary {\n  opacity: var(--fa-secondary-opacity, 0.4); }\n\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\n  opacity: var(--fa-primary-opacity, 1); }\n\n.svg-inline--fa mask .fa-primary,\n.svg-inline--fa mask .fa-secondary {\n  fill: black; }\n"]}