{"version": 3, "sources": ["sharp-regular.css"], "names": [], "mappings": "AAAA;;;;EAIE;AACF;EACE,+CAA+C;EAC/C,gEAAgE,EAAE;;AAEpE;EACE,mCAAmC;EACnC,kBAAkB;EAClB,gBAAgB;EAChB,mBAAmB;EACnB,kIAAkI,EAAE;;AAEtI;;EAEE,gBAAgB,EAAE", "file": "sharp-regular.css", "sourcesContent": ["/*!\n * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license (Commercial License)\n * Copyright 2024 Fonticons, Inc.\n */\n:root, :host {\n  --fa-style-family-sharp: 'Font Awesome 6 Sharp';\n  --fa-font-sharp-regular: normal 400 1em/1 'Font Awesome 6 Sharp'; }\n\n@font-face {\n  font-family: 'Font Awesome 6 Sharp';\n  font-style: normal;\n  font-weight: 400;\n  font-display: block;\n  src: url(\"../webfonts/fa-sharp-regular-400.woff2\") format(\"woff2\"), url(\"../webfonts/fa-sharp-regular-400.ttf\") format(\"truetype\"); }\n\n.fasr,\n.fa-regular {\n  font-weight: 400; }\n"]}