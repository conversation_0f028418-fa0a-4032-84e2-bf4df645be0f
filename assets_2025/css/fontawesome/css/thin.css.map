{"version": 3, "sources": ["thin.css"], "names": [], "mappings": "AAAA;;;;EAIE;AACF;EACE,+CAA+C;EAC/C,qDAAqD,EAAE;;AAEzD;EACE,iCAAiC;EACjC,kBAAkB;EAClB,gBAAgB;EAChB,mBAAmB;EACnB,gHAAgH,EAAE;;AAEpH;;EAEE,gBAAgB,EAAE", "file": "thin.css", "sourcesContent": ["/*!\n * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license (Commercial License)\n * Copyright 2024 Fonticons, Inc.\n */\n:root, :host {\n  --fa-style-family-classic: 'Font Awesome 6 Pro';\n  --fa-font-thin: normal 100 1em/1 'Font Awesome 6 Pro'; }\n\n@font-face {\n  font-family: 'Font Awesome 6 Pro';\n  font-style: normal;\n  font-weight: 100;\n  font-display: block;\n  src: url(\"../webfonts/fa-thin-100.woff2\") format(\"woff2\"), url(\"../webfonts/fa-thin-100.ttf\") format(\"truetype\"); }\n\n.fat,\n.fa-thin {\n  font-weight: 100; }\n"]}