html {
    background-color: #fff;
    background-image: none;
}
body#editorcontent {
    padding: 8px 15px;
    text-align: left;
    background-color: #fff;
}
div {
    margin-bottom: 1rem;
}

.embed-responsive {
    position: relative;
    height: 3rem;
    width: 100%;
    padding: 10px;
    text-align: center;
    background-color: #eee;
    border: solid 1px #ddd;
}
.embed-responsive:before {
    display: inline;
    content: 'Video Embedded - View Source Code to Edit';
    color: #aaa;
    font-style: italic;
    text-align: center;
}

@media (max-width: 991.999px) {
    .img-float {
        display: block;
        margin: 1.5rem auto;
    }
}
@media (min-width: 992px) {
    .img-float {
        width: 40%;
    }
    .img-float.float-lg-end {
        margin: .375rem 0 1.5rem 1.5rem;
    }
    .img-float.float-lg-start {
        margin: .375rem 1.5rem 1.5rem 0; 
    }
}

.block-set {
    float: none !important;
    width: 100%;
    margin: 1.5rem auto;
    font-size: .75rem;
    text-align: center;
    font-style: italic;
}
.block-set img {
    width: auto;
    max-width: 100%;
    height: auto;
    margin: 0 auto;
}
.block-set>:last-child {
    margin-bottom: 0;
}
@media (min-width: 992px) {
    .block-set {
        width: 40%;
    }
    .block-set.float-lg-end {
        float: right !important;
        margin: 0 0 1.5rem 1.5rem;
        padding-top: .5rem;
    }
    .block-set.float-lg-start {
        float: left !important;
        margin: 0 1.5rem 1.5rem 0;
        padding-top: .5rem;
    }
}
/*# sourceMappingURL=tinymce.css.map */