{"version": 3, "sources": ["tinymce.css"], "names": [], "mappings": "AAAA;IACI,sBAAsB;IACtB,sBAAsB;AAC1B;AACA;IACI,iBAAiB;IACjB,gBAAgB;IAChB,sBAAsB;AAC1B;AACA;IACI,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,YAAY;IACZ,WAAW;IACX,aAAa;IACb,kBAAkB;IAClB,sBAAsB;IACtB,sBAAsB;AAC1B;AACA;IACI,eAAe;IACf,oDAAoD;IACpD,WAAW;IACX,kBAAkB;IAClB,kBAAkB;AACtB;;AAEA;IACI;QACI,cAAc;QACd,mBAAmB;IACvB;AACJ;AACA;IACI;QACI,UAAU;IACd;IACA;QACI,+BAA+B;IACnC;IACA;QACI,+BAA+B;IACnC;AACJ;;AAEA;IACI,sBAAsB;IACtB,WAAW;IACX,mBAAmB;IACnB,iBAAiB;IACjB,kBAAkB;IAClB,kBAAkB;AACtB;AACA;IACI,WAAW;IACX,eAAe;IACf,YAAY;IACZ,cAAc;AAClB;AACA;IACI,gBAAgB;AACpB;AACA;IACI;QACI,UAAU;IACd;IACA;QACI,uBAAuB;QACvB,yBAAyB;QACzB,kBAAkB;IACtB;IACA;QACI,sBAAsB;QACtB,yBAAyB;QACzB,kBAAkB;IACtB;AACJ", "file": "tinymce.css", "sourcesContent": ["html {\n    background-color: #fff;\n    background-image: none;\n}\nbody#editorcontent {\n    padding: 8px 15px;\n    text-align: left;\n    background-color: #fff;\n}\ndiv {\n    margin-bottom: 1rem;\n}\n\n.embed-responsive {\n    position: relative;\n    height: 3rem;\n    width: 100%;\n    padding: 10px;\n    text-align: center;\n    background-color: #eee;\n    border: solid 1px #ddd;\n}\n.embed-responsive:before {\n    display: inline;\n    content: 'Video Embedded - View Source Code to Edit';\n    color: #aaa;\n    font-style: italic;\n    text-align: center;\n}\n\n@media (max-width: 991.999px) {\n    .img-float {\n        display: block;\n        margin: 1.5rem auto;\n    }\n}\n@media (min-width: 992px) {\n    .img-float {\n        width: 40%;\n    }\n    .img-float.float-lg-end {\n        margin: .375rem 0 1.5rem 1.5rem;\n    }\n    .img-float.float-lg-start {\n        margin: .375rem 1.5rem 1.5rem 0;\n    }\n}\n\n.block-set {\n    float: none !important;\n    width: 100%;\n    margin: 1.5rem auto;\n    font-size: .75rem;\n    text-align: center;\n    font-style: italic;\n}\n.block-set img {\n    width: auto;\n    max-width: 100%;\n    height: auto;\n    margin: 0 auto;\n}\n.block-set>:last-child {\n    margin-bottom: 0;\n}\n@media (min-width: 992px) {\n    .block-set {\n        width: 40%;\n    }\n    .block-set.float-lg-end {\n        float: right !important;\n        margin: 0 0 1.5rem 1.5rem;\n        padding-top: .5rem;\n    }\n    .block-set.float-lg-start {\n        float: left !important;\n        margin: 0 1.5rem 1.5rem 0;\n        padding-top: .5rem;\n    }\n}\n"]}