@mixin fa-icon-pseudo($style: solid, $icon: '\f005') {
    display: inline-block;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    content: $icon;

    @if $style == brands {
        font-family: 'Font Awesome 6 Pro';
        font-weight: 400;
    }
    @else if $style == solid {
        font-family: 'Font Awesome 6 Pro';
        font-weight: 900;
    }
    @else if $style == regular {
        font-family: 'Font Awesome 6 Pro';
        font-weight: 400;
    }
    @else if $style == light {
        font-family: 'Font Awesome 6 Pro';
        font-weight: 300;
    }
    @else if $style == thin {
        font-family: 'Font Awesome 6 Pro';
        font-weight: 100;
    }
    @else if $style == sharp-solid {
        font-family: 'Font Awesome 6 Sharp Solid';
        font-weight: 900;
    }
    @else if $style == sharp-regular {
        font-family: 'Font Awesome 6 Sharp Solid';
        font-weight: 400;
    }
    @else if $style == sharp-light {
        font: var(--fa-font-sharp-light);
    }
    @else if $style == sharp-thin {
        font-family: 'Font Awesome 6 Sharp Solid';
        font-weight: 100;
    }
}

@mixin icon-arrow-circle($color: white, $bg-color: $green) {

    &:after {
        display: block;
        content: '\f178';
        width: 36px;
        height: 36px;
        border-radius: 50%;
        color: $color;
        font-size: 20px;
        text-align: center;
        line-height: 36px;
        @include fa-icon-pseudo(regular);
        background-color: $bg-color;
    }
}

