// LOADING UP ALL THE FILES...
// We are attempting to build a bootstrap-5-based css file which can be included
// in pages in the Dialogs Framework backend, granting the use of BS5 utilities and flex-box
// components, without wrecking the existing overall layout of the Dialogs backend.

// We are not including ALL of bootstrap, only the grid, and utilities.

// Putting everything within a selector, which should restrict the scope of the styles.
// For the Portal pages where we want to be able to use BS5 styles, we have to add
// this class to a parent div around the content. Do this as far up the DOM as possible,
// rather than trying to do it multiple times within a single page.

// BOOTSTRAP
// First load up <PERSON>'s banner mixin, OUTSIDE of the selector
@import "../bootstrap/mixins/banner";
@include bsBanner("");

// Other stuff that needs to land outside the selector.

// BOOTSTRAP
// Load up BS's functions before custom, or some of our variables get grumpy.
@import "../bootstrap/functions";

// CUSTOM
// Must load up our custom overriding variables before BS's.
// @import "fonts";         // CUSTOM fonts
@import "variables";

// REMOVE FROM MAPS
$colors: map-remove($colors, "indigo", "pink", "yellow", "teal");
$theme-colors: map-remove($theme-colors, "indigo", "pink", "yellow", "teal");

// BOOTSTRAP
// Configuration
@import "../bootstrap/variables";
@import "../bootstrap/variables-dark";
@import "../bootstrap/maps";
//@import "../bootstrap/utilities";

@import "../bootstrap/mixins";
@import "mixins";

@import "../bootstrap/root";
@import "root";


.portal-branded {
    margin: 0; // 1
    font-family: var(--#{$prefix}body-font-family);
    @include font-size(var(--#{$prefix}body-font-size));
    font-weight: var(--#{$prefix}body-font-weight);
    line-height: var(--#{$prefix}body-line-height);
    color: var(--#{$prefix}body-color);
    text-align: var(--#{$prefix}body-text-align);
    -webkit-text-size-adjust: 100%; // 3
    -webkit-tap-highlight-color: rgba($black, 0); // 4

    // BOOTSTRAP
    // Configuration
    //@import "../bootstrap/variables";
    //@import "../bootstrap/variables-dark";
    //@import "../bootstrap/maps";
    @import "../bootstrap/utilities";

    // CUSTOM
    // Configuration
    // @import "variables-dark";
    // @import "maps";
    @import "utilities";


    // BOOTSTRAP
    // Mixins - only including those we need
    //@import "../bootstrap/mixins/breakpoints";
    //@import "../bootstrap/mixins/container";
    //@import "../bootstrap/mixins/grid";
    //@import "../bootstrap/mixins/utilities";

    // CUSTOM
    // Mixins

    // BOOTSTRAP
    // Layout & components - only what we need
    // Adding root to this, to get more utilities
    @import "../bootstrap/containers";
    @import "../bootstrap/type";
    @import "../bootstrap/grid";
    @import "../bootstrap/buttons";
    @import "../bootstrap/card";
    @import "../bootstrap/badge";

    // CUSTOM
    // Layout & components - only what we need
    @import "portal_branded/reboot";
    @import "type";

    // BOOTSTRAP
    // Helpers
    @import "../bootstrap/helpers";

    // CUSTOM
    // Helpers
    @import "helpers";

    // BOOTSTRAP
    // Utilities
    // Unlike the bootstrap-grid.scss file, we want to import the full utilities options.
    @import "../bootstrap/utilities/api";

    // CUSTOM LAYOUT
    //@import "portal/xxx";
}






