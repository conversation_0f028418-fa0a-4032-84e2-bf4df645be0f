//// All-caps `RGBA()` function used because of this Sass bug: https://github.com/sass/node-sass/issues/2251
//@each $color, $value in $theme-colors {
//  .text-bg-#{$color} {
//    color: color-contrast($value) if($enable-important-utilities, !important, null);
//    background-color: RGBA(var(--#{$prefix}#{$color}-rgb), var(--#{$prefix}bg-opacity, 1)) if($enable-important-utilities, !important, null);
//  }
//}


.text-bg-blue,
.text-bg-primary,
.text-bg-primary-500 {

  a:not(.btn):not([class*="link-"]) {
    @extend .link-primary-100;
  }
}

.text-bg-red,
.text-bg-secondary,
.text-bg-secondary-500 {
  color: $white !important;
  font-size: 1.5rem;

  a:not(.btn):not([class*="link-"]) {
    @extend .link-secondary-100;
  }
}

.text-bg-orange {
  color: $white !important;
  font-size: 1.5rem;

  a:not(.btn):not([class*="link-"]) {
    @extend .link-gray-100;
  }
}

.text-bg-purple {

  a:not(.btn):not([class*="link-"]) {
    @extend .link-gray-100;
  }
}

.text-bg-green {
  color: $white !important;
  font-size: 1.5rem;

  a:not(.btn):not([class*="link-"]) {
    @extend .link-gray-100;
  }
}

.text-bg-cyan {
  color: $white !important;
  font-size: 1.5rem;

  a:not(.btn):not([class*="link-"]) {
    @extend .link-gray-100;
  }
}
