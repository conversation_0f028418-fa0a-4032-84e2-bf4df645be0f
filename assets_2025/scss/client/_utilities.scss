// Utilities

// $utilities: () !default;
// stylelint-disable-next-line scss/dollar-variable-default
$utilities: map-merge(
        $utilities,
        (
            //
            // NEW utilities
            //
            "cursor": (
                property: cursor,
                class: cursor,
                responsive: true,
                values: auto pointer grab,
            ),

            //
            // MODIFIED utilities
            //
            "float": map-merge(
                    map-get($utilities, "float"),
                    (
                        values: map-merge(
                                map-get(map-get($utilities, "float"), "values"),
                                (
                                    left:   left,
                                    right:  right,
                                ),
                        ),
                    ),
            ),
            "border-color": map-merge(
                map-get($utilities, "border-color"),
                (
                    values: map-merge(
                        map-get(map-get($utilities, "border-color"), "values"),
                        (
                            transparent:   transparent,
                        ),
                    ),
                ),
            ),
            "width": map-merge(
                map-get($utilities, "width"),
                (
                    responsive: true,
                    values: map-merge(
                        map-get(map-get($utilities, "width"), "values"),
                        (
                            10: 10%,
                            20: 20%,
                            25: 25%,
                            30: 30%,
                            33: 33.333%,
                            40: 40%,
                            45: 45%,
                            50: 50%,
                            60: 60%,
                            66: 66.667%,
                            70: 70%,
                            75: 75%,
                            80: 80%,
                            90: 90%,
                            100: 100%,
                            auto: auto,
                        ),
                    ),
                ),
            ),
            "font-family": map-merge(
                    map-get($utilities, "font-family"),
                    (
                        values: map-merge(
                                map-get(map-get($utilities, "font-family"), "values"),
                                (
                                    // values: (monospace: var(--#{$prefix}font-monospace))
                                    sans-serif: $font-family-sans-serif,
                                    monospace:  $font-family-monospace,
                                    heading:    $headings-font-family,
                                    base:       $font-family-base,
                                ),
                        ),
                    ),
            ),
            "font-size": map-merge(
                    map-get($utilities, "font-size"),
                    (
                        values: map-merge(
                                map-get(map-get($utilities, "font-size"), "values"),
                                (
                                    10:     10px,
                                    12:     12px,
                                    14:     14px,
                                    16:     16px,
                                    18:     18px,
                                    20:     20px,
                                    22:     22px,
                                    24:     24px,
                                    26:     26px,
                                    28:     28px,
                                    30:     30px,
                                    32:     32px,
                                    36:     36px,
                                    40:     40px,
                                    44:     44px,
                                    48:     48px,
                                    52:     52px,
                                    56:     56px,
                                    60:     60px,
                                ),
                        ),
                    ),
            ),
            "font-weight": map-merge(
                    map-get($utilities, "font-weight"),
                    (
                        values: map-merge(
                                map-get(map-get($utilities, "font-weight"), "values"),
                                (
                                    thin: $font-weight-thin,
                                    extralight: $font-weight-extralight,
                                    light: $font-weight-light,
                                    normal: $font-weight-normal,
                                    medium: $font-weight-medium,
                                    semibold: $font-weight-semibold,
                                    bold: $font-weight-bold,
                                    extrabold: $font-weight-extrabold,
                                    black: $font-weight-black,
                                ),
                        ),
                    ),
            ),
        ),
);



