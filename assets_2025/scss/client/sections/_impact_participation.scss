#impact_participation {

    .participation-headline {

        .headline-image {
            width: 140px;
        }

        .headline-text {

            // MOBILE
            @include media-breakpoint-only(xs) {
                width: 100%;
                text-align: center;
            }

            // DESKTOP
            @include media-breakpoint-up(lg) {

            }
        }
    }

    .participation-stats {

        .stats-item {

            // MOBILE
            @include media-breakpoint-down(lg) {

            }

            // DESKTOP
            @include media-breakpoint-up(lg) {

            }
        }
    }

    .stats-chart-1,
    .stats-chart-2 {

        img.chart-img {
            max-width: min(280px, 80%);
        }
    }

    .stats-legend-1 {
        row-gap: .5rem;
        width: min(100%, 310px);
        margin: 0 auto;
    }

    .stats-legend-2 {
        width: min(100%, 310px);
        margin: 0 auto;
    }

    .progress-container {

        .progress-label-number {
            font-weight: $font-weight-semibold;
        }

        // MOBILE
        @include media-breakpoint-down(lg) {
            .progress-label-number {
                @include rfs(2rem);
            }
        }
        @include media-breakpoint-only(xs) {
            .progress-label-number {
                @include rfs(2rem);
            }
        }

        // DESKTOP
        @include media-breakpoint-up(lg) {
            .progress-label-number {
                @include rfs(2rem);
            }
        }
        @include media-breakpoint-up(xl) {
            .progress-label-number {
                @include rfs(2.5rem);
            }
        }
    }

    .official-img {
        width: min(180px, 50%);
        padding-bottom: 6%;
    }
}

// Impact Participation Section - Progress Bar Animation
// Target the specific section by ID and ensure proper initial state

// Initial state - progress bar starts at 0% width
#impact_participation .progress-bar {
    width: 0% !important;
    transition: width 1.8s cubic-bezier(0.4, 0, 0.2, 1);

    .progress-label-number {
        opacity: 0;
        transition: opacity 0.3s ease-in-out 1.2s;
    }
}

// Animation state - when section gets animate-progress class
#impact_participation.animate-progress {

    // Progress bar (35%) - animate to full width
    .progress-bar[aria-valuenow="35"] {
        width: 35% !important;

        .progress-label-number {
            opacity: 1;
        }
    }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
    #impact_participation .progress-bar {
        transition-duration: 0.3s;

        .progress-label-number {
            transition-delay: 0.1s;
        }
    }
}

// Impact Participation Section - Chart Image Animations
// Target the participant demographics section for chart fade-in animations

// Initial state - chart images start hidden
#impact_participation .participant-demographics {

    .stats-chart-1 img.chart-img,
    .stats-chart-2 img.chart-img {
        opacity: 0;
        clip-path: inset(100% 0 0 0);
        transition: opacity 1s ease-out, clip-path 1s ease-out;
    }
}

// Animation state - when participant-demographics gets animate-charts class
#impact_participation .participant-demographics.animate-charts {

    // First chart - animate immediately
    .stats-chart-1 img.chart-img {
        opacity: 1;
        clip-path: inset(0% 0 0 0);
    }

    // Second chart - animate with stagger delay
    .stats-chart-2 img.chart-img {
        opacity: 1;
        clip-path: inset(0% 0 0 0);
        transition-delay: 0.35s;
    }
}

// Reduced motion support for chart animations
@media (prefers-reduced-motion: reduce) {
    #impact_participation .participant-demographics {

        .stats-chart-1 img.chart-img,
        .stats-chart-2 img.chart-img {
            transition-duration: 0.2s;
        }
    }

    #impact_participation .participant-demographics.animate-charts {
        .stats-chart-2 img.chart-img {
            transition-delay: 0.1s;
        }
    }
}


