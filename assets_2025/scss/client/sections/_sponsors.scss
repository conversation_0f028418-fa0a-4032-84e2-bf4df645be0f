.sponsors-grid {
    gap: 1rem;

    &.sponsors-4-col {

        .sponsors-grid-item {
            display: block;
            width: 100%;
            height: 100%;
            padding: 1.5rem;
            box-shadow: none;
            transition: $transition-base;

            // MOBILE
            @include media-breakpoint-only(xs) {
                width: 100%;
                height: 160px;
            }
            @include media-breakpoint-only(sm) {
                width: calc((100% - 1rem) / 2);
                height: 160px;
            }
            @include media-breakpoint-only(md) {
                width: calc((100% - 1rem) / 2);
                height: 160px;
            }

            // DESKTOP
            @include media-breakpoint-up(lg) {
                width: calc((100% - 2rem) / 3);
                height: 180px;
            }
            @include media-breakpoint-up(xl) {
                width: calc((100% - 3rem) / 4);
                height: 180px;
            }
            @include media-breakpoint-up(xxl) {
                width: calc((100% - 4rem) / 5);
                height: 180px;
            }
            @include media-breakpoint-up(xxxl) {
                width: calc((100% - 5rem) / 6);
                height: 180px;
            }

            img {
                object-fit: contain;
                max-height: 100%;
                transition: $transition-base;
            }

            &:hover,
            &:focus {
                box-shadow: 0 0 .75rem rgba($black, .075);

                img {
                    transform: scale(1.05);
                }
            }
        }
    }
}


