.section-hero {

    // MOBILE
    @include media-breakpoint-down(md) {

        &:not(.hero-image) {
            background-image: none !important;
        }

        &.hero-image {

            &:before {
                display: block;
                padding-top: 39.58333%;
                content: "";
            }
        }
    }

    // DESKTOP
    @include media-breakpoint-up(md) {
        position: relative;
        width: 100%;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;

        &:before {
            display: block;
            padding-top: 39.58333%;
            content: "";
        }

        & > *:not(.btn-edit-section) {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .container-xl {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            height: 100%;
        }

        &.hero-left-text {

        }

        &.hero-right-text {

        }
    }

    @include media-breakpoint-only(lg) {

        h1, .h1 {
            @include rfs(3rem);
        }
    }

    @include media-breakpoint-only(md) {

        h1, .h1 {
            @include rfs(2.5rem);
        }
    }
}
