.staff-group {
    padding-top: 0;
    padding-bottom: min(5.625rem, 6vh);

    &:last-child {
        padding-bottom: 0;
    }
}

.staff-list {

    .staff-list-item {

    }

    .staff-list-item-link {
        color: $body-color;
        text-decoration: none;

        .staff-list-item-image {
            box-shadow: none;
        }

        &:hover,
        &:focus {
            color: $blue;
            background-color:rgba($gray-100, .5);

            .staff-list-item-image {
                box-shadow: $box-shadow;
            }
        }
    }
}

.staff-detail {
    
    // MOBILE
    @include media-breakpoint-down(lg) {
        width: min(100%, 420px);
        margin: 0 auto;
    }
    
    // DESKTOP
    @include media-breakpoint-up(lg) {
    }

    .item-quote {
        position: relative;
        padding: 0 4rem;
        font-style: italic;

        &::before {
            position: absolute;
            top: -1rem;
            left: 0;
            content: "\201C"; // left double quote
            font-size: 6rem;
            line-height: 1;
            color: $gray-200;
        }

        &::after {
            position: absolute;
            bottom: -3.5rem;
            right: 0;
            content: "\201D"; // left double quote
            font-size: 6rem;
            line-height: 1;
            color: $gray-200;
        }
    }
}