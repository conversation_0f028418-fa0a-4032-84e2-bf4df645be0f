.section-collapsing {

    .accordion {
        //--#{$prefix}accordion-color: #{$white};
        //--#{$prefix}accordion-bg: #{$primary-500};
        //--#{$prefix}accordion-transition: #{$accordion-transition};
        --#{$prefix}accordion-border-color: #{$primary-700};
        //--#{$prefix}accordion-border-width: #{$accordion-border-width};
        //--#{$prefix}accordion-border-radius: #{$accordion-border-radius};
        //--#{$prefix}accordion-inner-border-radius: #{$accordion-inner-border-radius};
        //--#{$prefix}accordion-btn-padding-x: #{$accordion-button-padding-x};
        //--#{$prefix}accordion-btn-padding-y: #{$accordion-button-padding-y};
        --#{$prefix}accordion-btn-color: #{$white};
        --#{$prefix}accordion-btn-bg: #{$primary-500};
        //--#{$prefix}accordion-btn-icon: #{escape-svg($accordion-button-icon)};
        //--#{$prefix}accordion-btn-icon-width: #{$accordion-icon-width};
        //--#{$prefix}accordion-btn-icon-transform: #{$accordion-icon-transform};
        //--#{$prefix}accordion-btn-icon-transition: #{$accordion-icon-transition};
        //--#{$prefix}accordion-btn-active-icon: #{escape-svg($accordion-button-active-icon)};
        //--#{$prefix}accordion-btn-focus-box-shadow: #{$accordion-button-focus-box-shadow};
        //--#{$prefix}accordion-body-padding-x: #{$accordion-body-padding-x};
        //--#{$prefix}accordion-body-padding-y: #{$accordion-body-padding-y};
        --#{$prefix}accordion-active-color: #{$white};
        --#{$prefix}accordion-active-bg: #{$primary-700};
    }

    .accordion-button {
        font-size: 1.5rem;
        font-weight: $font-weight-bold;
    }

    .accordion-button {
        //position: relative;
        //display: flex;
        //align-items: center;
        //width: 100%;
        //padding: var(--#{$prefix}accordion-btn-padding-y) var(--#{$prefix}accordion-btn-padding-x);
        //@include font-size($font-size-base);
        color: var(--#{$prefix}accordion-btn-color);
        //text-align: left; // Reset button style
        background-color: var(--#{$prefix}accordion-btn-bg);
        //border: 0;
        //@include border-radius(0);
        //overflow-anchor: none;
        //@include transition(var(--#{$prefix}accordion-transition));

        &:not(.collapsed) {
            color: var(--#{$prefix}accordion-active-color);
            background-color: var(--#{$prefix}accordion-active-bg);
            box-shadow: inset 0 calc(-1 * var(--#{$prefix}accordion-border-width)) 0 var(--#{$prefix}accordion-border-color); // stylelint-disable-line function-disallowed-list

            &::after {
                background-image: var(--#{$prefix}accordion-btn-active-icon);
                transform: var(--#{$prefix}accordion-btn-icon-transform);
            }
        }

        // Accordion icon
        &::after {
            flex-shrink: 0;
            width: var(--#{$prefix}accordion-btn-icon-width);
            height: var(--#{$prefix}accordion-btn-icon-width);
            margin-left: auto;
            content: "";
            background-image: var(--#{$prefix}accordion-btn-icon);
            background-repeat: no-repeat;
            background-size: var(--#{$prefix}accordion-btn-icon-width);
            @include transition(var(--#{$prefix}accordion-btn-icon-transition));
        }

        &:hover {
            z-index: 2;
        }

        &:focus {
            z-index: 3;
            outline: 0;
            box-shadow: var(--#{$prefix}accordion-btn-focus-box-shadow);
        }
    }
}