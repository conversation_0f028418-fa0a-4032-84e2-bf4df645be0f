.news-list {

    .news-list-item {
        margin-bottom: 3rem;
    }

    .news-list-item-link {
        color: $body-color;
        text-decoration: none;
        //box-shadow: none;

        .news-list-item-image {
            --bs-aspect-ratio: 50%; // 2x1

            img {
                object-fit: cover;
            }
        }

        &:hover,
        &:focus {
            color: $blue;
            background-color:rgba($gray-100, .5);
            //box-shadow: $box-shadow;

            .news-list-item-image img {
            }
        }
    }
}

.news-detail {

    img {
        border-radius: $border-radius;
    }
}