#impact_education {

    .progress-label-number {
        font-weight: $font-weight-semibold;
    }

    .progress-label-text {
        font-weight: $font-weight-normal;
        line-height: 1.2;
    }

    // MOBILE
    @include media-breakpoint-down(lg) {
        .progress-label-number {
            @include rfs(3.5rem);
        }
        .progress-label-text {
            @include rfs(1.25rem);
        }
    }
    @include media-breakpoint-only(xs) {
        .progress-label-number {
            @include rfs(2.5rem);
        }
        .progress-label-text {
            @include rfs(1rem);
        }
    }

    // DESKTOP
    @include media-breakpoint-up(lg) {
        .progress-label-number {
            @include rfs(2.5rem);
        }
        .progress-label-text {
            @include rfs(1rem);
        }
    }
    @include media-breakpoint-up(xl) {
        .progress-label-number {
            @include rfs(3.5rem);
        }
        .progress-label-text {
            @include rfs(1.25rem);
        }
    }
}


// Impact Education Section - Progress Bar Animations
// Target the specific section by ID and ensure proper initial state

// Initial state - progress bars start at 0% width
#impact_education .progress-bar {
    width: 0% !important;
    transition: width 1.8s cubic-bezier(0.4, 0, 0.2, 1);

    .d-flex {
        opacity: 0;
        transition: opacity 0.3s ease-in-out 1.2s;
    }
}

// Animation state - when section gets animate-progress class
#impact_education.animate-progress {

    // First progress bar (81%) - animate immediately
    .progress-bar[aria-valuenow="81"] {
        width: 81% !important;

        .d-flex {
            opacity: 1;
        }
    }

    // Second progress bar (58%) - animate with stagger delay
    .progress-bar[aria-valuenow="58"] {
        width: 58% !important;
        transition-delay: 0.4s;

        .d-flex {
            opacity: 1;
            transition-delay: 1.6s;
        }
    }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
    #impact_education .progress-bar {
        transition-duration: 0.3s;

        .d-flex {
            transition-delay: 0.1s;
        }
    }

    #impact_education.animate-progress .progress-bar[aria-valuenow="58"] {
        transition-delay: 0.1s;

        .d-flex {
            transition-delay: 0.2s;
        }
    }
}