.impact-numbers {

    .impact-list {
        position: relative;
        
        // MOBILE
        // XS size dividers are set on the .impact-item level
        @include media-breakpoint-only(sm) {
            &:before {
                position: absolute;
                top: 33.333%;
                left: 8rem;
                right: 8rem;
                content: '';
                display: block;
                width: auto;
                height: 1px;
                background-color: $gray-300;
            }
            &:after {
                position: absolute;
                bottom: 33.333%;
                left: 8rem;
                right: 8rem;
                content: '';
                display: block;
                width: auto;
                height: 1px;
                background-color: $gray-300;
            }
        }

        // DESKTOP
        @include media-breakpoint-up(md) {
            &:before {
                position: absolute;
                left: 33.333%;
                top: 7rem;
                bottom: 7rem;
                content: '';
                display: block;
                width: 1px;
                height: auto;
                background-color: $gray-300;
            }
            &:after {
                position: absolute;
                right: 33.333%;
                top: 7rem;
                bottom: 7rem;
                content: '';
                display: block;
                width: 1px;
                height: auto;
                background-color: $gray-300;
            }
        }
    }

    .impact-item {
        position: relative;

        .impact-number {
            line-height: 1.2;
        }

        &:nth-child(1) {
            .impact-number {
                color: $red;
            }
        }
        &:nth-child(2) {
            .impact-number {
                color: $orange;
            }
        }
        &:nth-child(3) {
            .impact-number {
                color: $cyan;
            }
        }
        &:nth-child(4) {
            .impact-number {
                color: $green;
            }
        }
        &:nth-child(5) {
            .impact-number {
                color: $blue;
            }
        }
        &:nth-child(6) {
            .impact-number {
                color: $purple;
            }
        }

        // MOBILE
        @include media-breakpoint-only(xs) {
            &:not(:last-child) {
                &:before {
                    position: absolute;
                    bottom: 0;
                    left: 7rem;
                    right: 7rem;
                    content: '';
                    display: block;
                    width: auto;
                    height: 1px;
                    background-color: $gray-300;
                }
            }
        }
    }
}

