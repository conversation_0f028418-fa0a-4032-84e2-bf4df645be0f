.section {
    position: relative;
    clear: both;
    margin-top: 0;
    margin-bottom: 0;
    padding-top: min(5.625rem, 6vh);
    padding-bottom: min(5.625rem, 6vh);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;

    :last-child {
        //margin-bottom: 0 !important;
    }

    .btn-edit-section {
        position: absolute;
        z-index: 100;
        top: 2rem;
        right: 2rem;
    }

    h1, .h1,
    h2, .h2,
    h3, .h3,
    h4, .h4,
    h5, .h5,
    h6, .h6 {
        color: inherit;
    }

    .section-row + .section-row {
        padding-top: 0;
    }

    &.bg-primary-900 {

        a:not(.btn) {
            color: $primary-300;
        }
    }

    &.section-img-full {

        .img-full-wrapper {

            img {

                // MOBILE
                @include media-breakpoint-down(md) {

                }

                // DESKTOP
                @include media-breakpoint-up(md) {
                    height: 100%;
                    object-fit: cover;
                }
            }
        }

        .section-content {
            padding-top: min(7rem, 7vh);
            padding-bottom: min(7rem, 7vh);
        }
    }
}


