.skills-container {

    // MOBILE
    @include media-breakpoint-down(lg) {
        gap: 3rem;
    }

    // DESKTOP
    @include media-breakpoint-up(lg) {
        gap: 2rem;
    }
    @include media-breakpoint-up(lg) {
        gap: 3rem;
    }
}

.skill-item {
    // MOBILE
    @include media-breakpoint-down(lg) {
        width: calc((100% - 6rem) / 3);
    }
    @include media-breakpoint-only(sm) {
        width: calc((100% - 3rem) / 2);
    }
    @include media-breakpoint-only(xs) {
        width: 100%;
    }

    // DESKTOP
    @include media-breakpoint-up(lg) {
        width: calc((100% - 6rem) / 3);
    }
    @include media-breakpoint-up(xxl) {
        width: calc((100% - 12rem) / 5);
    }
}

.skill-progress {
    width: 200px;
    height: 200px;
    line-height: 200px;
    background: none;
    margin: 0 auto;
    box-shadow: none;
    position: relative;

    /* CSS Custom Properties for dynamic percentages */
    --percentage: 50; /* Default fallback */
    --right-rotation: calc(min(180, var(--percentage) * 3.6) * 1deg);
    --left-rotation: calc(max(0, (var(--percentage) - 50) * 3.6) * 1deg);
    --left-animation-state: running;
    --animation-delay: 0s; /* Default delay */
    --left-animation-delay: calc(var(--animation-delay) + 1.62s); /* Right animation duration + base delay */
    --circle-size: clamp(100px, 100%, 250px);
    --border-radius: calc(var(--circle-size) / 2);
}
.skill-progress:after {
    content: "";
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 24px solid $gray-200;
    position: absolute;
    top: 0;
    left: 0;
}
.skill-progress > span {
    width: 50%;
    height: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    z-index: 1;
}
.skill-progress .skill-progress-left {
    left: 0;
}
.skill-progress .skill-progress-bar {
    width: 100%;
    height: 100%;
    background: none;
    border-width: 24px;
    border-style: solid;
    border-color: $primary;
    position: absolute;
    top: 0;
}
.skill-progress .skill-progress-left .skill-progress-bar {
    left: 100%;
    border-top-right-radius: 120px;
    border-bottom-right-radius: 120px;
    border-left: 0;
    -webkit-transform-origin: center left;
    transform-origin: center left;
}
.skill-progress .skill-progress-right {
    right: 0;
}
.skill-progress .skill-progress-right .skill-progress-bar {
    left: -100%;
    border-top-left-radius: 120px;
    border-bottom-left-radius: 120px;
    border-right: 0;
    -webkit-transform-origin: center right;
    transform-origin: center right;
    animation: skill-progress-right-dynamic 1.62s linear forwards;
    animation-delay: var(--animation-delay);
    animation-play-state: paused; /* Initially paused */
}

/* Start animations when section is in view */
.skill-progress.animate .skill-progress-right .skill-progress-bar {
    animation-play-state: running;
}
.skill-progress .skill-progress-value {
    width: 90%;
    height: 90%;
    border-radius: 50%;
    background: #ffffff;
    line-height: 180px;
    text-align: center;
    position: absolute;
    top: 5%;
    left: 5%;
}

.skill-progress .skill-progress-left .skill-progress-bar {
    animation: skill-progress-left-dynamic 1.35s linear forwards;
    animation-delay: var(--left-animation-delay);
    animation-play-state: paused; /* Initially paused */
}

/* Start left animations when section is in view and percentage > 50% */
.skill-progress.animate .skill-progress-left .skill-progress-bar {
    animation-play-state: var(--left-animation-state);
}






@keyframes skill-progress-right-dynamic {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(var(--right-rotation));
        transform: rotate(var(--right-rotation));
    }
}
@keyframes skill-progress-left-dynamic {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(var(--left-rotation));
        transform: rotate(var(--left-rotation));
    }
}
@keyframes loading-3 {
    0% { 
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
    }
}
@keyframes loading-4 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(36deg);
        transform: rotate(36deg);
    }
}
@keyframes loading-5 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(126deg);
        transform: rotate(126deg);
    }
}
@media only screen and (max-width: 990px) {
    .skill-progress { margin-bottom: 20px; }
}
