
.btn-sm {
    font-weight: $font-weight-normal;
    line-height: 1.25;
}

.btn-lg {
    font-weight: $font-weight-semibold;
    line-height: 1.08334;
}

.btn {
    font-weight: $font-weight-bold;
    text-transform: uppercase;
}


$btn-white-hover-bg: tint-color($blue, 93%);
$btn-white-active-bg: tint-color($blue, 88%);
.btn-white {
    --#{$prefix}btn-color: #{$primary};
    --#{$prefix}btn-hover-color: #{$primary};
    --#{$prefix}btn-hover-bg: #{$btn-white-hover-bg};
    --#{$prefix}btn-hover-border-color: #{$btn-white-hover-bg};
    --#{$prefix}btn-active-color: #{$primary};
    --#{$prefix}btn-active-bg: #{$btn-white-active-bg};
    --#{$prefix}btn-active-border-color: #{$btn-white-active-bg};
}

// red
.btn-danger,
.btn-red,
.btn-secondary,
.btn-secondary-500,
{
    --bs-btn-color: #{$white};
    //--bs-btn-bg: #EA7D1F;
    //--bs-btn-border-color: #EA7D1F;
    --bs-btn-hover-color: #{$white};
    //--bs-btn-hover-bg: rgb(237.15, 144.5, 64.6);
    //--bs-btn-hover-border-color: rgb(236.1, 138, 53.4);
    //--bs-btn-focus-shadow-rgb: 199, 106, 26;
    --bs-btn-active-color: #{$white};
    //--bs-btn-active-bg: rgb(238.2, 151, 75.8);
    //--bs-btn-active-border-color: rgb(236.1, 138, 53.4);
    //--bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #{$white};
    //--bs-btn-disabled-bg: #EA7D1F;
    //--bs-btn-disabled-border-color: #EA7D1F;
}

