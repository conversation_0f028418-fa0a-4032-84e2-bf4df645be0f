#site_main {
    flex-grow: 1;
    flex-shrink: 0;
    flex-basis: auto;
    display: flex;
    flex-direction: column;
    transition: padding-top .1s ease-in-out;
    // padding-top: var(--#{$variable-prefix}header-height);

    .content-generic {
        padding-top: min(5.625rem, 6vh);
        padding-bottom: min(5.625rem, 6vh);
    }

    // BEGIN STYLES SET UP FOR IMAGES COMING FROM WYSIWYG EDITOR

    // MOBILE
    @include media-breakpoint-down(lg) {
        .img-float {
            display: block;
            margin: 1.5rem auto;
        }
    }

    // DESKTOP
    @include media-breakpoint-up(lg) {
        .img-float {
            width: 40%;

            &.float-lg-end {
                margin: .375rem 0 1.5rem 1.5rem;
            }

            &.float-lg-start {
                margin: .375rem 1.5rem 1.5rem 0;
            }
        }
    }

    .block-set {
        float: none !important;
        width: 100%;
        margin: 1.5rem auto;
        font-size: .75rem;
        text-align: center;
        font-style: italic;

        img {
            width: auto;
            max-width: 100%;
            height: auto;
            margin: 0 auto;
        }

        > :last-child {
            margin-bottom: 0;
        }

        // MOBILE
        @include media-breakpoint-down(lg) {

        }

        // DESKTOP
        @include media-breakpoint-up(lg) {
            .block-set {
                width: 40%;

                &.float-lg-end {
                    float: right !important;
                    margin: 0 0 1.5rem 1.5rem;
                    padding-top: .5rem;
                }

                &.float-lg-start {
                    float: left !important;
                    margin: 0 1.5rem 1.5rem 0;
                    padding-top: .5rem;
                }
            }
        }
    }

    // END STYLES SET UP FOR IMAGES COMING FROM WYSIWYG EDITOR

}


