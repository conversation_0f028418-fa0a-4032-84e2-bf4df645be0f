#site_header {
    box-shadow: $box-shadow-sm;
    @include transition($transition-base);

    .skip {
        position: absolute;
        top: -1000px;
        left: -1000px;
        z-index: 100;
        height: auto;
        width: auto;
        padding: 10px 15px;
        text-align: left;
        overflow: hidden;

        &:active,
        &:focus,
        &:hover {
            left: 15px;
            top: 15px;
            overflow: visible;
            display: block;
        }
    }

    .navbar {

        // MOBILE
        @include media-breakpoint-down(xl) {
            padding-top: .75rem;
            padding-bottom: .75rem;
        }

        // DESKTOP
        @include media-breakpoint-up(xl) {
            padding-top: 1rem;
            padding-bottom: 1rem;

            .container-fluid {
                gap: 1rem;
            }
        }
    }

    .navbar-brand {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex-shrink: 0;
        width: auto;
        height: auto;
        padding: 0;

        // MOBILE
        @include media-breakpoint-down(xl) {
            width: 140px;
        }

        // DESKTOP
        @include media-breakpoint-up(xl) {
            width: 280px;
        }

        img {
            width: 100%;
            height: auto;
        }
    }

    .navbar-toggler {
        --#{$prefix}navbar-toggler-padding-y: 6px;
        --#{$prefix}navbar-toggler-padding-x: #{$btn-padding-x-sm};
        --#{$prefix}navbar-toggler-font-size: #{$navbar-toggler-font-size};
        // --#{$prefix}navbar-toggler-icon-bg: #{escape-svg($navbar-light-toggler-icon-bg)};
        --#{$prefix}navbar-toggler-border-color: transparent; // #{$navbar-light-toggler-border-color};
        --#{$prefix}navbar-toggler-border-radius: 25rem;
        --#{$prefix}navbar-toggler-focus-width: 1px;
        --#{$prefix}navbar-toggler-transition: #{$navbar-toggler-transition};

        color: $body-color;

        &:hover {
            color: $blue;
            text-decoration: none;
            background-color: rgba($blue, .1);
        }

        &:focus {
            color: $blue;
            text-decoration: none;
            outline: 0;
            background-color: rgba($blue, .1);
            box-shadow: 0 0 3px var(--#{$prefix}navbar-toggler-focus-width);
        }
    }

    #nav_primary {

        // MOBILE
        @include media-breakpoint-down(xl) {
            width: calc(100vw - 1rem);
            max-width: 420px;
            margin: .5rem;
            background-color: $white;
            border-radius: $border-radius;
            transform: translateX(calc(100% + 1rem));

            &.showing,
            &.show:not(.hiding) {
                transform: none;
            }
        }

        // DESKTOP
        @include media-breakpoint-up(xl) {
            width: 100%;
        }

        .offcanvas-header {

            // MOBILE
            @include media-breakpoint-down(xl) {
                padding: .5rem 1.5rem;

                .navbar-brand {
                    order: 1;
                }

                .btn-close {
                    order: 2;
                    margin: 0;
                    padding: 7px 16px;
                    color: $body-color;
                    font-size: 1.5rem;
                    line-height: 1;
                    border-radius: .25rem;
                    background-image: none;
                    opacity: 1;

                    &:hover {
                        color: $blue;
                        text-decoration: none;
                        background-color: rgba($blue, .2);
                        opacity: 1;
                    }

                    &:focus {
                        color: $blue;
                        text-decoration: none;
                        outline: 0;
                        background-color: rgba($blue, .2);
                        box-shadow: 0 0 3px 1px;
                        opacity: 1;
                    }
                }
            }

            // DESKTOP
            @include media-breakpoint-up(xl) {

            }
        }

        .offcanvas-body {
            display: flex;
            gap: 1rem;

            // MOBILE
            @include media-breakpoint-down(xl) {
                flex-direction: column;
                padding: 2rem 1.5rem;
            }

            // DESKTOP
            @include media-breakpoint-up(xl) {
                flex-direction: row;
                justify-content: flex-end;
                align-items: center;
                width: 100%;
            }

            .dropdown-menu {
                --#{$prefix}dropdown-min-width: 14rem;
                --#{$prefix}dropdown-padding-x: #{$dropdown-padding-x};
                --#{$prefix}dropdown-padding-y: 1rem;
                --#{$prefix}dropdown-spacer: 1.25rem;
                @include rfs($dropdown-font-size, --#{$prefix}dropdown-font-size);
                --#{$prefix}dropdown-color: #{$body-color};
                --#{$prefix}dropdown-bg: #{$secondary};
                --#{$prefix}dropdown-border-color: #{$white};
                --#{$prefix}dropdown-border-radius: #{$dropdown-border-radius};
                --#{$prefix}dropdown-border-width: 0;
                --#{$prefix}dropdown-inner-border-radius: #{$dropdown-inner-border-radius};
                --#{$prefix}dropdown-divider-bg: #{$dropdown-divider-bg};
                --#{$prefix}dropdown-divider-margin-y: #{$dropdown-divider-margin-y};
                --#{$prefix}dropdown-box-shadow: .25rem 0.25rem .75rem rgba(0, 0, 0, 0.3);
                --#{$prefix}dropdown-link-color: #{$body-color};
                --#{$prefix}dropdown-link-hover-color: #{$white};
                --#{$prefix}dropdown-link-hover-bg: #{$primary};
                --#{$prefix}dropdown-link-active-color: #{$white};
                --#{$prefix}dropdown-link-active-bg: #{$primary};
                --#{$prefix}dropdown-link-disabled-color: #{$dropdown-link-disabled-color};
                --#{$prefix}dropdown-item-padding-x: #{$dropdown-item-padding-x};
                --#{$prefix}dropdown-item-padding-y: #{$dropdown-item-padding-y};
                --#{$prefix}dropdown-header-color: #{$dropdown-header-color};
                --#{$prefix}dropdown-header-padding-x: #{$dropdown-header-padding-x};
                --#{$prefix}dropdown-header-padding-y: #{$dropdown-header-padding-y};
            }
        }

        .navbar-nav {

            .nav-link {
                color: $body-color;
                font-size: 1.125rem;
                font-weight: $font-weight-bold;
                text-transform: none;

                &.active,
                &.show {

                }

                &:hover {
                    color: $blue;
                }
                &:focus {
                    color: $blue;
                }
                &:focus-within {
                    color: $blue;
                }
            }

            .dropdown-toggle {

                &:after {
                    @include fa-icon-pseudo(regular, '\f107');
                    line-height: .8;
                    border: none;
                    transform: translateY(.35rem);
                }

                &:after,
                &:hover:after {
                    text-decoration: none;
                }
            }

            .dropdown-menu {

                .nav-item {

                }

                .nav-link {
                    font-size: 1.25rem;
                    font-weight: bold;
                    background-color: transparent;
                }
            }

            // MOBILE
            @include media-breakpoint-down(xl) {
                align-items: center;
                gap: 1rem;
                margin-bottom: 3rem;

                .nav-item {
                    overflow: hidden;
                    text-align: center;
                    background-color: transparent;
                    border-radius: 0;
                }

                .nav-link {
                    display: inline-block;
                    margin: 0 auto;
                    padding: .375rem 1.5rem;
                    text-decoration: none;

                    &:hover {
                        text-decoration: underline;
                    }
                }

                .dropdown-menu {
                    margin-top: 0;
                    padding-top: 0;
                    background-color: transparent;
                    box-shadow: none;

                    .nav-item {
                        border-radius: 0;
                    }

                    .nav-link {
                        font-weight: $font-weight-normal;
                    }
                }
            }
            
            // DESKTOP
            @include media-breakpoint-up(xl) {
                display: flex;
                flex-direction: row;
                flex-wrap: nowrap;
                justify-content: flex-end;
                align-items: center;
                gap: 0;
                width: 100%;
                //padding: 0 3rem;
                background-color: transparent;
                transform: translateY(1rem);

                .nav-item {
                    padding-right: .5rem;
                    padding-left: .5rem;
                }

                .nav-link {

                }

                .dropdown-menu {
                    width: 250px;
                    margin-top: .5rem !important;
                    background-color: $white !important;
                    border-radius: 0 !important;
                    box-shadow: var(--#{$prefix}dropdown-box-shadow);

                    .nav-item {
                        border-radius: 0;
                    }

                    .nav-link {
                        padding: .5rem 1rem;

                        &:hover {
                        }
                        &:focus {
                        }
                        &:focus-within {
                        }
                    }
                }
            }
            @include media-breakpoint-up(xxl) {
                transform: translateY(0);
            }
        }

        .user-access {

            // MOBILE
            @include media-breakpoint-down(xl) {

            }

            // DESKTOP
            @include media-breakpoint-up(xl) {
                position: absolute;
                top: -.5rem;
                right: calc((var(--#{$prefix}gutter-x)*.5) + .5rem);
            }
            @include media-breakpoint-up(xxl) {
                position: relative;
                top: 0;
                right: 0;
            }
        }
    }
}


