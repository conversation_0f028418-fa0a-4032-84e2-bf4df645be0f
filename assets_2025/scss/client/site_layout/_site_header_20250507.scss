#site_header {
    box-shadow: $box-shadow-sm;
    @include transition($transition-base);

    .skip {
        position: absolute;
        top: -1000px;
        left: -1000px;
        height: auto;
        width: auto;
        padding: 10px 15px;
        text-align: left;
        overflow: hidden;

        &:active,
        &:focus,
        &:hover {
            left: 15px;
            top: 15px;
            overflow: visible;
            display: block;
        }
    }

    .navbar {
        padding: 0;
    }

    .masthead {

        // MOBILE
        @include media-breakpoint-down(lg) {
            padding-top: .75rem;
            padding-bottom: .75rem;
        }

        // DESKTOP
        @include media-breakpoint-up(lg) {
            padding-top: 1rem;
            padding-bottom: 1rem;
        }
    }

    &.header-sticky .masthead {

        // MOBILE
        @include media-breakpoint-down(lg) {
            padding-top: .5rem;
            padding-bottom: .5rem;
        }

        // DESKTOP
        @include media-breakpoint-up(lg) {
            padding-top: 1rem;
            padding-bottom: 1rem;
        }
    }

    .navbar-brand {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: auto;
        height: auto;
        padding: 0;

        img {
            width: auto;
            @include transition($transition-base);

            // MOBILE
            @include media-breakpoint-down(lg) {
                max-height: 75px;
            }
            @include media-breakpoint-only(xs) {
                max-height: 45px;
            }

            // DESKTOP
            @include media-breakpoint-up(lg) {
                max-height: 115px;
            }
        }
    }

    &.header-sticky .navbar-brand {

        img {

            // MOBILE
            @include media-breakpoint-down(lg) {
                max-height: 50px;
            }
            @include media-breakpoint-only(xs) {
                max-height: 30px;
            }

            // DESKTOP
            @include media-breakpoint-up(lg) {
                max-height: 92px;
            }
        }
    }

    .navbar-toggler {
        --#{$prefix}navbar-toggler-padding-y: 6px;
        --#{$prefix}navbar-toggler-padding-x: #{$btn-padding-x-sm};
        --#{$prefix}navbar-toggler-font-size: #{$navbar-toggler-font-size};
        // --#{$prefix}navbar-toggler-icon-bg: #{escape-svg($navbar-light-toggler-icon-bg)};
        --#{$prefix}navbar-toggler-border-color: transparent; // #{$navbar-light-toggler-border-color};
        --#{$prefix}navbar-toggler-border-radius: 25rem;
        --#{$prefix}navbar-toggler-focus-width: 1px;
        --#{$prefix}navbar-toggler-transition: #{$navbar-toggler-transition};

        &:hover {
            text-decoration: none;
            background-color: rgba($white, .2);
        }

        &:focus {
            text-decoration: none;
            outline: 0;
            background-color: rgba($white, .2);
            box-shadow: 0 0 3px var(--#{$prefix}navbar-toggler-focus-width);
        }
    }

    .user-access-desktop-wrapper {
        position: absolute;
        top: 1rem;
        right: 1.5rem;

        .btn-consultation {
            margin-top: 56px;
            padding-top: 9px;
            padding-bottom: 9px;
        }
    }

    &.header-sticky .user-access-desktop-wrapper {

        .btn-consultation {
            margin-top: 52px;
        }
    }

    .user-access {
        color: $white;
        text-align: center;
        border-radius: 1.5rem;

        &.user-access-mobile {
            padding: .75rem 1.5rem;
        }

        &.user-access-desktop {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 100;
            padding: .5rem 1.5rem;
        }

        a:not(.form-close) {
            position: relative;
            color: $white;
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }

            &:focus {

            }

            &:focus-within {

            }
        }

        .access-links {

            // MOBILE
            @include media-breakpoint-down(lg) {

            }

            // DESKTOP
            @include media-breakpoint-up(lg) {
                flex-wrap: nowrap !important;

                a:not(:first-child):before {
                    position: absolute;
                    left: -.5rem;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    display: block;
                    content: '';
                    width: 1px;
                    height: 10px;
                    background-color: $white;
                }
            }

            a {
                white-space: nowrap;
            }
        }

        .form-login {
            width: 100%;
            padding: 1rem 0;

            .form-close {
                position: absolute;
                top: 1rem;
                right: 1rem;
                color: $white;

            }
        }
    }

    #nav_primary {

        // MOBILE
        @include media-breakpoint-down(lg) {
            width: calc(100vw - 1rem);
            max-width: 420px;
            margin: .5rem;
            background-color: white;
            border-radius: $border-radius;
            transform: translateX(calc(100% + 1rem));

            &.showing,
            &.show:not(.hiding) {
                transform: none;
            }
        }

        // DESKTOP
        @include media-breakpoint-up(lg) {
            width: 100%;
        }

        .offcanvas-header {

            // MOBILE
            @include media-breakpoint-down(lg) {
                padding: .5rem 1.5rem;

                .navbar-brand {
                    order: 1;

                    img {

                        // MOBILE
                        @include media-breakpoint-down(lg) {
                            max-height: 50px;
                        }
                        @include media-breakpoint-only(xs) {
                            max-height: 40px;
                        }

                        // DESKTOP
                        @include media-breakpoint-up(lg) {

                        }
                    }
                }

                .btn-close {
                    order: 2;
                    margin: 0;
                    padding: 7px 16px;
                    color: $body-color;
                    font-size: 1.5rem;
                    line-height: 1;
                    border-radius: .25rem;
                    background-image: none;
                    opacity: 1;

                    &:hover {
                        text-decoration: none;
                        background-color: rgba($primary, .2);
                        opacity: 1;
                    }

                    &:focus {
                        text-decoration: none;
                        outline: 0;
                        background-color: rgba($primary, .2);
                        box-shadow: 0 0 3px 1px;
                        opacity: 1;
                    }
                }
            }

            // DESKTOP
            @include media-breakpoint-up(lg) {

            }
        }

        .offcanvas-body {
            display: flex;

            // MOBILE
            @include media-breakpoint-down(lg) {
                flex-direction: column;
                padding: 2rem 1.5rem;
            }

            // DESKTOP
            @include media-breakpoint-up(lg) {
                flex-direction: row;
                justify-content: flex-end;
                align-items: center;
                width: 100%;
            }

            .dropdown-menu {
                --#{$prefix}dropdown-min-width: 14rem;
                --#{$prefix}dropdown-padding-x: #{$dropdown-padding-x};
                --#{$prefix}dropdown-padding-y: 1rem;
                --#{$prefix}dropdown-spacer: 1.25rem;
                @include rfs($dropdown-font-size, --#{$prefix}dropdown-font-size);
                --#{$prefix}dropdown-color: #{$white};
                --#{$prefix}dropdown-bg: #{$secondary};
                --#{$prefix}dropdown-border-color: #{$secondary};
                --#{$prefix}dropdown-border-radius: #{$dropdown-border-radius};
                --#{$prefix}dropdown-border-width: 0;
                --#{$prefix}dropdown-inner-border-radius: #{$dropdown-inner-border-radius};
                --#{$prefix}dropdown-divider-bg: #{$dropdown-divider-bg};
                --#{$prefix}dropdown-divider-margin-y: #{$dropdown-divider-margin-y};
                --#{$prefix}dropdown-box-shadow: .25rem 0.25rem .75rem rgba(0, 0, 0, 0.3);
                --#{$prefix}dropdown-link-color: #{$white};
                --#{$prefix}dropdown-link-hover-color: #{$secondary};
                --#{$prefix}dropdown-link-hover-bg: #{$white};
                --#{$prefix}dropdown-link-active-color: #{$primary};
                --#{$prefix}dropdown-link-active-bg: #{$white};
                --#{$prefix}dropdown-link-disabled-color: #{$dropdown-link-disabled-color};
                --#{$prefix}dropdown-item-padding-x: #{$dropdown-item-padding-x};
                --#{$prefix}dropdown-item-padding-y: #{$dropdown-item-padding-y};
                --#{$prefix}dropdown-header-color: #{$dropdown-header-color};
                --#{$prefix}dropdown-header-padding-x: #{$dropdown-header-padding-x};
                --#{$prefix}dropdown-header-padding-y: #{$dropdown-header-padding-y};

                box-shadow: var(--#{$prefix}dropdown-box-shadow);
            }
        }

        .navbar-nav {

            .nav-link {
                color: $white;
                font-size: 1.5rem;
                font-weight: $font-weight-bold;
                text-transform: uppercase;

                &.active,
                &.show {

                }

                &:hover {
                  color: $secondary-500;
                }
                &:focus {
                  color: $secondary-500;
                }
                &:focus-within {
                  color: $secondary-500;
                }
            }

            .dropdown-menu {
                background-color: $primary-900;

                .nav-item {

                }

                .nav-link {
                    font-size: 1.25rem;
                    background-color: transparent;
                }
            }

            // MOBILE
            @include media-breakpoint-down(lg) {
                gap: 1rem;
                background-color: $white;

                .nav-item {
                    overflow: hidden;
                    background-color: $primary-900;
                    border-radius: 1.5rem;
                }

                .nav-link {
                    padding: .375rem 1.5rem;
                }

                .dropdown-menu {
                    margin-top: 0;
                    padding-top: 0;
                    background-color: transparent;

                    .nav-item {
                        border-radius: 0;
                    }

                    .nav-link {
                        padding-left: 3rem;
                    }
                }
            }
            
            // DESKTOP
            @include media-breakpoint-up(lg) {
                display: flex;
                flex-direction: row;
                flex-wrap: nowrap;
                justify-content: flex-end;
                align-items: center;
                gap: 1rem;
                width: 100%;
                padding: 0 3rem;
                background-color: $primary-900;

                .nav-item {

                }

                .nav-link {
                    padding: .75rem;
                }

                .dropdown-menu {
                    width: 250px;
                    margin-top: .25rem;

                    .nav-item {

                    }

                    .nav-link {
                        padding: .5rem 1rem;
                    }
                }
            }
        }
    }
}


