
#site_footer {
    padding-top: 3rem;
    padding-bottom: 3rem;
    color: $white;
    font-size: .875rem;

    a {
        color: $white;
        text-decoration: none;

        &:hover,
        &:focus ,
        &:focus-within {
            color: $white;
            text-decoration: underline;
        }

    }

    .footer-row-1 {

        // MOBILE
        @include media-breakpoint-down(xl) {
            column-gap: 2rem;
            row-gap: 2rem;
            margin-bottom: 4rem;
        }

        // DESKTOP
        @include media-breakpoint-up(xl) {
            column-gap: 2rem;
            row-gap: 2rem;
        }
        @include media-breakpoint-up(xxl) {
            column-gap: 5rem;
            row-gap: 2rem;
        }
    }

    .footer-logo {
        width: 200px;
        flex-shrink: 0;

        a {

        }
    }

    .footer-nav {

        // MOBILE
        @include media-breakpoint-down(xl) {
            column-gap: 2rem;
            row-gap: 2rem;
        }

        // DESKTOP
        @include media-breakpoint-up(xl) {
            column-gap: 2rem;
            row-gap: 2rem;
        }
        @include media-breakpoint-up(xxl) {
            column-gap: 5rem;
            row-gap: 2rem;
        }
    }

    .footer-nav-col {

        // MOBILE
        @include media-breakpoint-down(lg) {
            width: 100%;
        }

        // DESKTOP
        @include media-breakpoint-up(lg) {
            width: 210px;
        }
        @include media-breakpoint-up(xl) {
            width: auto;
            max-width: 220px;
            flex-shrink: 1;
        }

        h2, a {
            white-space: nowrap;
        }
    }

    .footer-connect {
        gap: 2.5rem;

        // MOBILE
        @include media-breakpoint-down(xl) {
            width: 100%;
        }

        // DESKTOP
        @include media-breakpoint-up(xl) {
            width: 280px;
        }
        @include media-breakpoint-up(xxl) {
            width: 340px;
        }
    }

    .footer-newsletter {

    }

    .footer-legal {

        a {
            white-space: nowrap;
        }
    }

    .social {
        margin-bottom: 40px;

        a {
            margin-right: 5px;
            margin-left: 0;
        }

        // MOBILE
        @include media-breakpoint-down(lg) {

        }

        // DESKTOP
        @include media-breakpoint-up(lg) {
            text-align: right;

            a {
                margin-right: 0;
                margin-left: 5px;
            }

        }
    }
}
