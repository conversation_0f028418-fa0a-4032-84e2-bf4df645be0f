$(document).ready(function(){

    $(window).on('load', function(){
        setSiteMainTopPadding();
        setMastheadHeight();
    });

    $(window).resize(function(){
        setSiteMainTopPadding();
        setMastheadHeight();
    });



    // Set Classes on Scroll
    $(window).scroll(function() {
        if ($(this).scrollTop() > 0){
            $('body').addClass("has-header-sticky");
            $('#site_header').addClass("header-sticky");
            // setSiteMainTopPadding();
        }
        else{
            $('body').removeClass("has-header-sticky");
            $('#site_header').removeClass("header-sticky");
        }
        setSiteMainTopPadding();
        setMastheadHeight();
        setTimeout(setSiteMainTopPadding,201); // allow 200 ms for css transitions
        setTimeout(setMastheadHeight,201); // allow 200 ms for css transitions
    });



    // scroll to target ID for jump links
    var offsetSize = $('#site_header').height() + 30;

    if (window.location.hash && $(window.location.hash).length) {

        var anchor = window.location.hash;
        //console.log('hash: ' + anchor);
        $('html, body').animate({
            scrollTop:$(anchor).offset().top - offsetSize
        }, 400);

    }

    // scroll to target ID for jump links
    // https://codepen.io/chriscoyier/pen/dpBMVP
    $('a[href*="#"]')
        // Remove links that don't actually link to anything
        .not('[href="#"]')
        .not('[href="#0"]')
        .not('[data-bs-toggle=collapse]')
        .not('[data-bs-toggle=modal]')
        .not('[data-bs-slide-to]')
        .click(function(event) {
            // On-page links
            if (
                location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '')
                &&
                location.hostname == this.hostname
            ) {
                // Figure out element to scroll to
                var target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                // Does a scroll target exist?
                if (target.length) {
                    // Only prevent default if animation is actually gonna happen
//                    event.preventDefault();
                    $('html, body').animate({
                        scrollTop: target.offset().top - offsetSize
                    }, 1000, function() {
                        // Callback after animation
                        // Must change focus!
                        var $target = $(target);
                        $target.focus();
                        if ($target.is(":focus")) { // Checking if the target was focused
                            return false;
                        } else {
                            $target.attr('tabindex','-1'); // Adding tabindex for elements not focusable
                            $target.focus(); // Set focus again
                        };
                    });
                }
            }
        });

});







function setSiteMainTopPadding () {
    let headerHeight = $('#site_header').outerHeight();
    document.documentElement.style.setProperty('--page-header-height', headerHeight+'px');
}

function setMastheadHeight () {
    let mastheadHeight = $('.navbar-masthead').outerHeight();
    document.documentElement.style.setProperty('--page-masthead-height', mastheadHeight+'px');
}


