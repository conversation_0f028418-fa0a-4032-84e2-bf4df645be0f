tinymce.PluginManager.add("image",function(e){function t(e,t){function n(e,n){i.parentNode.removeChild(i),t({width:e,height:n})}var i=document.createElement("img");i.onload=function(){n(i.clientWidth,i.clientHeight)},i.onerror=function(){n()},i.src=e;var a=i.style;a.visibility="hidden",a.position="fixed",a.bottom=a.left=0,a.width=a.height="auto",document.body.appendChild(i)}function n(t){return function(){var n=e.settings.image_list;"string"==typeof n?tinymce.util.XHR.send({url:n,success:function(e){t(tinymce.util.JSON.parse(e))}}):t(n)}}function i(n){function i(){var t=[{text:"None",value:""}];return tinymce.each(n,function(n){t.push({text:n.text||n.title,value:e.convertURL(n.value||n.url,"src"),menu:n.menu})}),t}function a(e){var t,n,i,a;t=s.find("#width")[0],n=s.find("#height")[0],i=t.value(),a=n.value(),s.find("#constrain")[0].checked()&&d&&u&&i&&a&&(e.control==t?(a=Math.round(i/d*a),n.value(a)):(i=Math.round(a/u*i),t.value(i))),d=i,u=a}function o(){function t(t){function i(){t.onload=t.onerror=null,e.selection.select(t),e.nodeChanged()}t.onload=function(){n.width||n.height||m.setAttribs(t,{width:t.clientWidth,height:t.clientHeight}),i()},t.onerror=i}var n=s.toJSON();""===n.width&&(n.width=null),""===n.height&&(n.height=null),""===n.style&&(n.style=null),n={src:n.src,alt:n.alt,width:n.width,height:n.height,style:n.style},e.undoManager.transact(function(){return n.src?(p?m.setAttribs(p,n):(n.id="__mcenew",e.selection.setContent(m.createHTML("img",n)),p=m.get("__mcenew"),m.setAttrib(p,"id",null)),t(p),void 0):(p&&(m.remove(p),e.nodeChanged()),void 0)})}function l(e){return e&&(e=e.replace(/px$/,"")),e}function r(){h&&h.value(e.convertURL(this.value(),"src")),t(this.value(),function(e){e.width&&e.height&&(d=e.width,u=e.height,s.find("#width").value(d),s.find("#height").value(u))})}function c(){function e(e){return e.length>0&&/^[0-9]+$/.test(e)&&(e+="px"),e}var t=s.toJSON(),n=m.parseStyle(t.style);delete n.margin,n["margin-top"]=n["margin-bottom"]=e(t.vspace),n["margin-left"]=n["margin-right"]=e(t.hspace),n["border-width"]=e(t.border),s.find("#style").value(m.serializeStyle(m.parseStyle(m.serializeStyle(n))))}var s,d,u,h,g={},m=e.dom,p=e.selection.getNode();d=m.getAttrib(p,"width"),u=m.getAttrib(p,"height"),"IMG"!=p.nodeName||p.getAttribute("data-mce-object")||p.getAttribute("data-mce-placeholder")?p=null:g={src:m.getAttrib(p,"src"),alt:m.getAttrib(p,"alt"),width:d,height:u},n&&(h={type:"listbox",label:"Image list",values:i(),value:g.src&&e.convertURL(g.src,"src"),onselect:function(e){var t=s.find("#alt");(!t.value()||e.lastControl&&t.value()==e.lastControl.text())&&t.value(e.control.text()),s.find("#src").value(e.control.value())},onPostRender:function(){h=this}});var y=[{name:"src",type:"filepicker",filetype:"image",label:"Source",autofocus:!0,onchange:r},h,{name:"alt",type:"textbox",label:"Image description"},{type:"container",label:"Dimensions",layout:"flex",direction:"row",align:"center",spacing:5,items:[{name:"width",type:"textbox",maxLength:3,size:3,onchange:a},{type:"label",text:"x"},{name:"height",type:"textbox",maxLength:3,size:3,onchange:a},{name:"constrain",type:"checkbox",checked:!0,text:"Constrain proportions"}]}];e.settings.image_advtab?(p&&(g.hspace=l(p.style.marginLeft||p.style.marginRight),g.vspace=l(p.style.marginTop||p.style.marginBottom),g.border=l(p.style.borderWidth),g.style=e.dom.serializeStyle(e.dom.parseStyle(e.dom.getAttrib(p,"style")))),s=e.windowManager.open({title:"Insert/edit image",data:g,bodyType:"tabpanel",body:[{title:"General",type:"form",items:y},{title:"Advanced",type:"form",pack:"start",items:[{label:"Style",name:"style",type:"textbox"},{type:"form",layout:"grid",packV:"start",columns:2,padding:0,alignH:["left","right"],defaults:{type:"textbox",maxWidth:50,onchange:c},items:[{label:"Vertical space",name:"vspace"},{label:"Horizontal space",name:"hspace"},{label:"Border",name:"border"}]}]}],onSubmit:o})):s=e.windowManager.open({title:"Insert/edit image",data:g,body:y,onSubmit:o})}e.addButton("image",{icon:"image",tooltip:"Insert/edit image",onclick:n(i),stateSelector:"img:not([data-mce-object],[data-mce-placeholder])"}),e.addMenuItem("image",{icon:"image",text:"Insert image",onclick:n(i),context:"insert",prependToContext:!0})});