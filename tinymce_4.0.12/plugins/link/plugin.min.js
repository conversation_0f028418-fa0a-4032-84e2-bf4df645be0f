tinymce.PluginManager.add("link",function(e){function t(t){return function(){var n=e.settings.link_list;"string"==typeof n?tinymce.util.XHR.send({url:n,success:function(e){t(tinymce.util.JSON.parse(e))}}):t(n)}}function n(t){function n(e){var t=f.find("#text");(!t.value()||e.lastControl&&t.value()==e.lastControl.text())&&t.value(e.control.text()),f.find("#href").value(e.control.value())}function l(){var n=[{text:"None",value:""}];return tinymce.each(t,function(t){n.push({text:t.text||t.title,value:e.convertURL(t.value||t.url,"href"),menu:t.menu})}),n}function i(t){var n=[{text:"None",value:""}];return tinymce.each(e.settings.rel_list,function(e){n.push({text:e.text||e.title,value:e.value,selected:t===e.value})}),n}function r(t){var n=[{text:"None",value:""}];return e.settings.target_list||n.push({text:"New window",value:"_blank"}),tinymce.each(e.settings.target_list,function(e){n.push({text:e.text||e.title,value:e.value,selected:t===e.value})}),n}function a(t){var l=[];return tinymce.each(e.dom.select("a:not([href])"),function(e){var n=e.name||e.id;n&&l.push({text:n,value:"#"+n,selected:-1!=t.indexOf("#"+n)})}),l.length?(l.unshift({text:"None",value:""}),{name:"anchor",type:"listbox",label:"Anchors",values:l,onselect:n}):void 0}function o(){d&&d.value(e.convertURL(this.value(),"href")),c||0!==x.text.length||this.parent().parent().find("#text")[0].value(this.value())}var u,s,c,f,d,h,v,x={},g=e.selection,m=e.dom;u=g.getNode(),s=m.getParent(u,"a[href]"),x.text=c=s?s.innerText||s.textContent:g.getContent({format:"text"}),x.href=s?m.getAttrib(s,"href"):"",x.target=s?m.getAttrib(s,"target"):"",x.rel=s?m.getAttrib(s,"rel"):"","IMG"==u.nodeName&&(x.text=c=" "),t&&(d={type:"listbox",label:"Link list",values:l(),onselect:n,value:e.convertURL(x.href,"href"),onPostRender:function(){d=this}}),e.settings.target_list!==!1&&(v={name:"target",type:"listbox",label:"Target",values:r(x.target)}),e.settings.rel_list&&(h={name:"rel",type:"listbox",label:"Rel",values:i(x.rel)}),f=e.windowManager.open({title:"Insert link",data:x,body:[{name:"href",type:"filepicker",filetype:"file",size:40,autofocus:!0,label:"Url",onchange:o,onkeyup:o},{name:"text",type:"textbox",size:40,label:"Text to display",onchange:function(){x.text=this.value()}},a(x.href),d,h,v],onSubmit:function(t){function n(t,n){var l=e.selection.getRng();window.setTimeout(function(){e.windowManager.confirm(t,function(t){e.selection.setRng(l),n(t)})},0)}function l(){i.text!=c?s?(e.focus(),s.innerHTML=i.text,m.setAttribs(s,{href:r,target:i.target?i.target:null,rel:i.rel?i.rel:null}),g.select(s)):e.insertContent(m.createHTML("a",{href:r,target:i.target?i.target:null,rel:i.rel?i.rel:null},i.text)):e.execCommand("mceInsertLink",!1,{href:r,target:i.target,rel:i.rel?i.rel:null})}var i=t.data,r=i.href;return r?r.indexOf("@")>0&&-1==r.indexOf("//")&&-1==r.indexOf("mailto:")?(n("The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",function(e){e&&(r="mailto:"+r),l()}),void 0):/^\s*www\./i.test(r)?(n("The URL you entered seems to be an external link. Do you want to add the required http:// prefix?",function(e){e&&(r="http://"+r),l()}),void 0):(l(),void 0):(e.execCommand("unlink"),void 0)}})}e.addButton("link",{icon:"link",tooltip:"Insert/edit link",shortcut:"Ctrl+K",onclick:t(n),stateSelector:"a[href]"}),e.addButton("unlink",{icon:"unlink",tooltip:"Remove link",cmd:"unlink",stateSelector:"a[href]"}),e.addShortcut("Ctrl+K","",t(n)),this.showDialog=n,e.addMenuItem("link",{icon:"link",text:"Insert link",shortcut:"Ctrl+K",onclick:t(n),stateSelector:"a[href]",context:"insert",prependToContext:!0})});