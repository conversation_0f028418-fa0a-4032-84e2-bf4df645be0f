tinymce.PluginManager.add("autoresize",function(e){function t(i){var a,s,r=e.getDoc(),g=r.body,u=r.documentElement,m=tinymce.DOM,l=n.autoresize_min_height;"setcontent"==i.type&&i.initial||e.plugins.fullscreen&&e.plugins.fullscreen.isFullscreen()||(s=tinymce.Env.ie?g.scrollHeight:tinymce.Env.webkit&&0===g.clientHeight?0:g.offsetHeight,s>n.autoresize_min_height&&(l=s),n.autoresize_max_height&&s>n.autoresize_max_height?(l=n.autoresize_max_height,g.style.overflowY="auto",u.style.overflowY="auto"):(g.style.overflowY="hidden",u.style.overflowY="hidden",g.scrollTop=0),l!==o&&(a=l-o,m.setStyle(m.get(e.id+"_ifr"),"height",l+"px"),o=l,tinymce.isWebKit&&0>a&&t(i)))}function i(e,n,o){setTimeout(function(){t({}),e--?i(e,n,o):o&&o()},n)}var n=e.settings,o=0;e.settings.inline||(n.autoresize_min_height=parseInt(e.getParam("autoresize_min_height",e.getElement().offsetHeight),10),n.autoresize_max_height=parseInt(e.getParam("autoresize_max_height",0),10),e.on("init",function(){e.dom.setStyle(e.getBody(),"paddingBottom",e.getParam("autoresize_bottom_margin",50)+"px")}),e.on("change setcontent paste keyup",t),e.getParam("autoresize_on_init",!0)&&e.on("init",function(){i(20,100,function(){i(5,1e3)})}),e.addCommand("mceAutoResize",t))});