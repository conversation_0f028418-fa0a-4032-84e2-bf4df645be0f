tinymce.PluginManager.add("anchor",function(e){function t(){var t=e.selection.getNode();e.windowManager.open({title:"Anchor",body:{type:"textbox",name:"name",size:40,label:"Name",value:t.name||t.id},onsubmit:function(t){e.execCommand("mceInsertContent",!1,e.dom.createHTML("a",{id:t.data.name}))}})}e.add<PERSON>utton("anchor",{icon:"anchor",tooltip:"Anchor",onclick:t,stateSelector:"a:not([href])"}),e.addMenuItem("anchor",{icon:"anchor",text:"Anchor",context:"insert",onclick:t})});