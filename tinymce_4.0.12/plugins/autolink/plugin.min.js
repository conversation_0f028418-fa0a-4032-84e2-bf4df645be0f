tinymce.PluginManager.add("autolink",function(t){function e(t){o(t,-1,"(",!0)}function n(t){o(t,0,"",!0)}function i(t){o(t,-1,"",!1)}function o(t,e,n){var i,o,r,a,s,l,c,u,d;if(i=t.selection.getRng(!0).cloneRange(),i.startOffset<5){if(u=i.endContainer.previousSibling,!u){if(!i.endContainer.firstChild||!i.endContainer.firstChild.nextSibling)return;u=i.endContainer.firstChild.nextSibling}if(d=u.length,i.setStart(u,d),i.setEnd(u,d),i.endOffset<5)return;o=i.endOffset,a=u}else{if(a=i.endContainer,3!=a.nodeType&&a.firstChild){for(;3!=a.nodeType&&a.firstChild;)a=a.firstChild;3==a.nodeType&&(i.setStart(a,0),i.setEnd(a,a.nodeValue.length))}o=1==i.endOffset?2:i.endOffset-1-e}r=o;do i.setStart(a,o>=2?o-2:0),i.setEnd(a,o>=1?o-1:0),o-=1;while(" "!=i.toString()&&""!==i.toString()&&160!=i.toString().charCodeAt(0)&&o-2>=0&&i.toString()!=n);if(i.toString()==n||160==i.toString().charCodeAt(0)?(i.setStart(a,o),i.setEnd(a,r),o+=1):0===i.startOffset?(i.setStart(a,0),i.setEnd(a,r)):(i.setStart(a,o),i.setEnd(a,r)),l=i.toString(),"."==l.charAt(l.length-1)&&i.setEnd(a,r-1),l=i.toString(),c=l.match(/^(https?:\/\/|ssh:\/\/|ftp:\/\/|file:\/|www\.|(?:mailto:)?[A-Z0-9._%+\-]+@)(.+)$/i),c&&("www."==c[1]?c[1]="http://www.":/@$/.test(c[1])&&!/^mailto:/.test(c[1])&&(c[1]="mailto:"+c[1]),s=t.selection.getBookmark(),t.selection.setRng(i),t.execCommand("createlink",!1,c[1]+c[2]),t.selection.moveToBookmark(s),t.nodeChanged(),tinymce.Env.webkit)){t.selection.collapse(!1);var m=Math.min(a.length,r+1);i.setStart(a,m),i.setEnd(a,m),t.selection.setRng(i)}}t.on("keydown",function(e){return 13==e.keyCode?i(t):void 0}),tinymce.Env.ie||(t.on("keypress",function(n){return 41==n.which?e(t):void 0}),t.on("keyup",function(e){return 32==e.keyCode?n(t):void 0}))});