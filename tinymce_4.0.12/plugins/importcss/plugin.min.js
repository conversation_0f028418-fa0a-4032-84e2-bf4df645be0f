tinymce.PluginManager.add("importcss",function(t){function e(t){return"string"==typeof t?function(e){return-1!==e.indexOf(t)}:t instanceof RegExp?function(e){return t.test(e)}:t}function n(e,n){function i(t,e){var o,l=t.href;if((e||c[l])&&(!n||n(l))){s(t.imports,function(t){i(t,!0)});try{o=t.cssRules||t.rules}catch(a){}s(o,function(t){t.styleSheet?i(t.styleSheet,!0):t.selectorText&&s(t.selectorText.split(","),function(t){r.push(tinymce.trim(t))})})}}var r=[],c={};s(t.contentCSS,function(t){c[t]=!0});try{s(e.styleSheets,function(t){i(t)})}catch(o){}return r}function i(e){var n,i=/^(?:([a-z0-9\-_]+))?(\.[a-z0-9_\-\.]+)$/i.exec(e);if(i){var r=i[1],s=i[2].substr(1).split(".").join(" ");return i[1]?(n={title:e},t.schema.getTextBlockElements()[r]?n.block=r:t.schema.getBlockElements()[r]?n.selector=r:n.inline=r):i[2]&&(n={inline:"span",title:e.substr(1),classes:s}),t.settings.importcss_merge_classes!==!1?n.classes=s:n.attributes={"class":s},n}}var r=this,s=tinymce.each;t.on("renderFormatsMenu",function(c){var o=t.settings,l={},a=o.importcss_selector_converter||i,f=e(o.importcss_selector_filter);t.settings.importcss_append||t.settings.style_formats||c.control.items().remove();var m=[];tinymce.each(o.importcss_groups,function(t){t=tinymce.extend({},t),t.filter=e(t.filter),m.push(t)}),s(n(t.getDoc(),e(o.importcss_file_filter)),function(e){if(-1===e.indexOf(".mce-")&&!l[e]&&(!f||f(e))){var n,i=a.call(r,e);if(i){var s=i.name||tinymce.DOM.uniqueId();if(m)for(var o=0;o<m.length;o++)if(!m[o].filter||m[o].filter(e)){m[o].item||(m[o].item={text:m[o].title,menu:[]}),n=m[o].item.menu;break}t.formatter.register(s,i);var u=tinymce.extend({},c.control.settings.itemDefaults,{text:i.title,format:s});n?n.push(u):c.control.add(u)}l[e]=!0}}),s(m,function(t){c.control.add(t.item)}),c.control.renderNew()}),r.convertSelectorToFormat=i});