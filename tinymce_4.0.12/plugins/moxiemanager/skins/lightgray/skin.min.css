.moxman-container,
.moxman-container *,
.moxman-widget,
.moxman-widget *,
.moxman-reset {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  vertical-align: top;
  background: transparent;
  text-decoration: none;
  color: #333333;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  text-shadow: none;
  float: none;
  position: static;
  width: auto;
  height: auto;
  white-space: nowrap;
  cursor: inherit;
  -webkit-tap-highlight-color: transparent;
  line-height: normal;
  font-weight: normal;
  text-align: left;
  box-sizing: content-box;
  direction: ltr;
  max-width: none;
}
.moxman-widget button {
  box-sizing: border-box;
}
.moxman-container *[unselectable] {
  -moz-user-select: none;
  -webkit-user-select: none;
  -o-user-select: none;
  user-select: none;
}
.overflow-scrolling {
  -webkit-overflow-scrolling: touch;
}
.moxman-fade {
  opacity: 0;
  transition: opacity 0.15s linear;
}
.moxman-fade.moxman-in {
  opacity: 1;
}
.moxman-upload-row {
  border: 1px solid #cecece;
}
.moxman-upload-drop-zone {
  border: 1px dashed #9e9e9e;
  text-align: center;
}
.moxman-overflow-y {
  overflow-y: auto;
}
.moxman-abs-layout {
  position: relative;
}
body .moxman-abs-layout-item,
.moxman-abs-end {
  position: absolute;
  top: -1000px;
}
.moxman-abs-end {
  width: 1px;
  height: 1px;
}
.moxman-container-body.moxman-abs-layout {
  overflow: hidden;
}
.moxman-tooltip {
  position: absolute;
  padding: 5px;
  opacity: 0.8;
  filter: alpha(opacity=80);
  zoom: 1;
}
.moxman-tooltip-inner {
  font-size: 11px;
  background-color: #000000;
  color: white;
  max-width: 200px;
  padding: 5px 8px 4px 8px;
  text-align: center;
  white-space: normal;
}
.moxman-tooltip-inner {
  box-shadow: 0 0 5px #000000;
}
.moxman-tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  line-height: 0;
  border: 5px dashed #000000;
}
.moxman-tooltip-arrow-n {
  border-bottom-color: #000000;
}
.moxman-tooltip-arrow-s {
  border-top-color: #000000;
}
.moxman-tooltip-arrow-e {
  border-left-color: #000000;
}
.moxman-tooltip-arrow-w {
  border-right-color: #000000;
}
.moxman-tooltip-nw,
.moxman-tooltip-sw {
  margin-left: -14px;
}
.moxman-tooltip-n .moxman-tooltip-arrow {
  top: 0px;
  left: 50%;
  margin-left: -5px;
  border-bottom-style: solid;
  border-top: none;
  border-left-color: transparent;
  border-right-color: transparent;
}
.moxman-tooltip-nw .moxman-tooltip-arrow {
  top: 0;
  left: 10px;
  border-bottom-style: solid;
  border-top: none;
  border-left-color: transparent;
  border-right-color: transparent;
}
.moxman-tooltip-ne .moxman-tooltip-arrow {
  top: 0;
  right: 10px;
  border-bottom-style: solid;
  border-top: none;
  border-left-color: transparent;
  border-right-color: transparent;
}
.moxman-tooltip-s .moxman-tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  border-top-style: solid;
  border-bottom: none;
  border-left-color: transparent;
  border-right-color: transparent;
}
.moxman-tooltip-sw .moxman-tooltip-arrow {
  bottom: 0;
  left: 10px;
  border-top-style: solid;
  border-bottom: none;
  border-left-color: transparent;
  border-right-color: transparent;
}
.moxman-tooltip-se .moxman-tooltip-arrow {
  bottom: 0;
  right: 10px;
  border-top-style: solid;
  border-bottom: none;
  border-left-color: transparent;
  border-right-color: transparent;
}
.moxman-tooltip-e .moxman-tooltip-arrow {
  right: 0;
  top: 50%;
  margin-top: -5px;
  border-left-style: solid;
  border-right: none;
  border-top-color: transparent;
  border-bottom-color: transparent;
}
.moxman-tooltip-w .moxman-tooltip-arrow {
  left: 0;
  top: 50%;
  margin-top: -5px;
  border-right-style: solid;
  border-left: none;
  border-top-color: transparent;
  border-bottom-color: transparent;
}
.moxman-btn {
  border: 1px solid #b1b1b1;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25) rgba(0, 0, 0, 0.25);
  position: relative;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
  display: inline-block;
  *display: inline;
  *zoom: 1;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
  background-color: #f0f0f0;
  background-image: linear-gradient(to bottom, white, #d9d9d9);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#ffd9d9d9', GradientType=0);
  zoom: 1;
}
.moxman-btn:hover,
.moxman-btn:focus {
  color: #333333;
  background-color: #e3e3e3;
  background-image: linear-gradient(to bottom, #f2f2f2, #cccccc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff2f2f2', endColorstr='#ffcccccc', GradientType=0);
  zoom: 1;
}
.moxman-btn.moxman-disabled button,
.moxman-btn.moxman-disabled:hover button {
  cursor: default;
  box-shadow: none;
  opacity: 0.4;
  filter: alpha(opacity=40);
  zoom: 1;
}
.moxman-btn.moxman-active,
.moxman-btn.moxman-active:hover {
  background-color: #d6d6d6;
  background-image: linear-gradient(to bottom, #e6e6e6, #c0c0c0);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffe6e6e6', endColorstr='#ffc0c0c0', GradientType=0);
  zoom: 1;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
}
.moxman-btn:active {
  background-color: #d6d6d6;
  background-image: linear-gradient(to bottom, #e6e6e6, #c0c0c0);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffe6e6e6', endColorstr='#ffc0c0c0', GradientType=0);
  zoom: 1;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
}
@media (min-device-width: 800px) and (max-device-width: 1280px), (min-device-width: 768px) and (max-device-width: 1024px) {
  .moxman-btn:hover,
  .moxman-btn:focus,
  .moxman-btn:active {
    background-color: #f0f0f0;
    background-image: linear-gradient(to bottom, white, #d9d9d9);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#ffd9d9d9', GradientType=0);
    zoom: 1;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
  }
}
.moxman-btn button {
  padding: 4px 10px;
  font-size: 14px;
  line-height: 20px;
  *line-height: 16px;
  cursor: pointer;
  color: #333333;
  text-align: center;
  overflow: visible;
  -webkit-appearance: none;
}
.moxman-btn button::-moz-focus-inner {
  border: 0;
  padding: 0;
}
.moxman-btn i {
  text-shadow: 1px 1px white;
}
.moxman-primary {
  min-width: 50px;
  color: #ffffff;
  border: 1px solid #b1b1b1;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25) rgba(0, 0, 0, 0.25);
  background-color: #006dcc;
  background-image: linear-gradient(to bottom, #0088cc, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0044cc', GradientType=0);
  zoom: 1;
}
.moxman-primary:hover,
.moxman-primary:focus {
  background-color: #005fb3;
  background-image: linear-gradient(to bottom, #0077b3, #003cb3);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff0077b3', endColorstr='#ff003cb3', GradientType=0);
  zoom: 1;
}
.moxman-primary.moxman-disabled button,
.moxman-primary.moxman-disabled:hover button {
  cursor: default;
  box-shadow: none;
  opacity: 0.4;
  filter: alpha(opacity=40);
  zoom: 1;
}
.moxman-primary.moxman-active,
.moxman-primary.moxman-active:hover,
.moxman-primary:not(.moxman-disabled):active {
  background-color: #005299;
  background-image: linear-gradient(to bottom, #006699, #003399);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff006699', endColorstr='#ff003399', GradientType=0);
  zoom: 1;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
}
.moxman-primary button,
.moxman-primary button i {
  color: #ffffff;
  text-shadow: 1px 1px #333333;
}
.moxman-btn-large button {
  padding: 9px 14px;
  font-size: 16px;
  line-height: normal;
}
.moxman-btn-large i {
  margin-top: 2px;
}
.moxman-btn-small button {
  padding: 1px 5px;
  font-size: 12px;
  *padding-bottom: 2px;
}
.moxman-btn-small i {
  line-height: 20px;
  vertical-align: top;
  *line-height: 18px;
}
.moxman-btn .moxman-caret {
  margin-top: 8px;
  margin-left: 0;
}
.moxman-btn-small .moxman-caret {
  margin-top: 8px;
  margin-left: 0;
}
.moxman-caret {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  width: 0;
  height: 0;
  vertical-align: top;
  border-top: 4px solid #333333;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  content: "";
}
.moxman-disabled .moxman-caret {
  border-top-color: #aaaaaa;
}
.moxman-caret.moxman-up {
  border-bottom: 4px solid #333333;
  border-top: 0;
}
.moxman-btn-flat {
  border: 0;
  background: transparent;
  box-shadow: none;
  filter: none;
}
.moxman-btn-flat:hover,
.moxman-btn-flat.moxman-active,
.moxman-btn-flat:focus,
.moxman-btn-flat:active {
  border: 0;
  background: #e6e6e6;
  filter: none;
  box-shadow: none;
}
.moxman-rtl .moxman-btn button {
  direction: rtl;
}
.moxman-container,
.moxman-container-body {
  display: block;
}
.moxman-autoscroll {
  overflow: hidden;
}
.moxman-btn-group .moxman-btn {
  border-width: 1px 0 1px 0;
  margin: 0;
}
.moxman-btn-group .moxman-first {
  border-left: 1px solid #b1b1b1;
  border-left: 1px solid rgba(0, 0, 0, 0.25);
}
.moxman-btn-group .moxman-last {
  border-right: 1px solid #b1b1b1;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}
.moxman-btn-group .moxman-btn.moxman-flow-layout-item {
  margin: 0;
}
.moxman-carousel {
  background: black;
}
.moxman-carousel .moxman-dir {
  position: absolute;
  top: 50%;
  left: 25px;
  width: 40px;
  height: 40px;
  margin-top: -20px;
  font-size: 60px;
  *font-size: 30px;
  font-weight: 100;
  line-height: 30px;
  color: white;
  text-align: center;
  background: #222;
  border: 3px solid white;
  opacity: 50;
  filter: alpha(opacity=5000);
  zoom: 1;
  cursor: pointer;
  z-index: 1000;
}
.moxman-carousel .moxman-next {
  right: 25px;
  left: auto;
}
.moxman-carousel .moxman-dir:hover {
  opacity: 100;
  filter: alpha(opacity=10000);
  zoom: 1;
}
.moxman-carousel .moxman-view {
  position: absolute;
  width: 100%;
  height: 100%;
  background: black;
}
.moxman-object {
  position: absolute;
  visibility: hidden;
}
.moxman-checkbox {
  cursor: pointer;
}
i.moxman-i-checkbox {
  margin: 0 3px 0 0;
  border: 1px solid #c5c5c5;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
  background-color: #f0f0f0;
  background-image: linear-gradient(to bottom, white, #d9d9d9);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#ffd9d9d9', GradientType=0);
  zoom: 1;
  text-indent: -10em;
  *font-size: 0;
  *line-height: 0;
  *text-indent: 0;
  overflow: hidden;
}
.moxman-checked i.moxman-i-checkbox {
  color: #333333;
  font-size: 16px;
  line-height: 16px;
  text-indent: 0;
}
.moxman-checkbox:focus i.moxman-i-checkbox,
.moxman-checkbox.moxman-focus i.moxman-i-checkbox {
  border: 1px solid rgba(82, 168, 236, 0.8);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.65);
}
.moxman-checkbox.moxman-disabled .moxman-label,
.moxman-checkbox.moxman-disabled i.moxman-i-checkbox {
  color: #acacac;
}
.moxman-rtl .moxman-checkbox {
  direction: rtl;
  text-align: right;
}
.moxman-rtl i.moxman-i-checkbox {
  margin: 0 0 0 3px;
}
.moxman-combobox {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  *height: 32px;
}
.moxman-combobox input {
  border: 1px solid #c5c5c5;
  border-right-color: #c5c5c5;
  height: 28px;
}
.moxman-combobox.moxman-disabled input {
  color: #adadad;
}
.moxman-combobox .moxman-btn {
  border-left: 0;
}
.moxman-combobox button {
  padding-right: 8px;
  padding-left: 8px;
}
.moxman-combobox.moxman-disabled .moxman-btn button {
  cursor: default;
  box-shadow: none;
  opacity: 0.4;
  filter: alpha(opacity=40);
  zoom: 1;
}
.moxman-fieldset {
  border: 0 solid #9E9E9E;
}
.moxman-fieldset > .moxman-container-body {
  margin-top: -15px;
}
.moxman-fieldset-title {
  margin-left: 5px;
  padding: 0 5px 0 5px;
}
.moxman-fit-layout {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}
.moxman-fit-layout-item {
  position: absolute;
}
.moxman-scrollbar {
  position: absolute;
  width: 7px;
  height: 100%;
  top: 2px;
  right: 2px;
  opacity: 0.4;
  filter: alpha(opacity=40);
  zoom: 1;
}
.moxman-scrollbar-h {
  top: auto;
  right: auto;
  left: 2px;
  bottom: 2px;
  width: 100%;
  height: 7px;
}
.moxman-scrollbar-thumb {
  position: absolute;
  background-color: #000;
  border: 1px solid #888;
  border-color: rgba(85, 85, 85, 0.6);
  width: 5px;
  height: 100%;
}
.moxman-scrollbar-h .moxman-scrollbar-thumb {
  width: 100%;
  height: 5px;
}
.moxman-scrollbar:hover,
.moxman-scrollbar.moxman-active {
  background-color: #AAA;
  opacity: 0.6;
  filter: alpha(opacity=60);
  zoom: 1;
}
.moxman-scroll {
  position: relative;
}
.moxman-panel {
  border: 0 solid #9e9e9e;
  background-color: #f0f0f0;
  background-image: linear-gradient(to bottom, #fdfdfd, #dddddd);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fffdfdfd', endColorstr='#ffdddddd', GradientType=0);
  zoom: 1;
}
.moxman-floatpanel {
  position: absolute;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}
.moxman-floatpanel.moxman-fixed {
  position: fixed;
}
.moxman-floatpanel .moxman-arrow,
.moxman-floatpanel .moxman-arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.moxman-floatpanel .moxman-arrow {
  border-width: 11px;
}
.moxman-floatpanel .moxman-arrow:after {
  border-width: 10px;
  content: "";
}
.moxman-floatpanel.moxman-popover {
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background: transparent;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  top: 0;
  left: 0;
  background: #ffffff;
  border: 1px solid #9e9e9e;
  border: 1px solid rgba(0, 0, 0, 0.25);
}
.moxman-floatpanel.moxman-popover.moxman-bottom {
  margin-top: 10px;
  *margin-top: 0;
}
.moxman-floatpanel.moxman-popover.moxman-bottom > .moxman-arrow {
  left: 50%;
  margin-left: -11px;
  border-top-width: 0;
  border-bottom-color: #9e9e9e;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  top: -11px;
}
.moxman-floatpanel.moxman-popover.moxman-bottom > .moxman-arrow:after {
  top: 1px;
  margin-left: -10px;
  border-top-width: 0;
  border-bottom-color: #ffffff;
}
.moxman-floatpanel.moxman-popover.moxman-bottom.moxman-start {
  margin-left: -22px;
}
.moxman-floatpanel.moxman-popover.moxman-bottom.moxman-start > .moxman-arrow {
  left: 20px;
}
.moxman-floatpanel.moxman-popover.moxman-bottom.moxman-end {
  margin-left: 22px;
}
.moxman-floatpanel.moxman-popover.moxman-bottom.moxman-end > .moxman-arrow {
  right: 10px;
  left: auto;
}
.moxman-flow-layout-item {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}
.moxman-flow-layout-item {
  margin: 2px 0 2px 2px;
}
.moxman-flow-layout-item.moxman-last {
  margin-right: 2px;
}
.moxman-flow-layout {
  white-space: normal;
}
.moxman-tinymce-inline .moxman-flow-layout {
  white-space: nowrap;
}
.moxman-rtl .moxman-flow-layout {
  text-align: right;
  direction: rtl;
}
.moxman-rtl .moxman-flow-layout-item {
  margin: 2px 2px 2px 0;
}
.moxman-rtl .moxman-flow-layout-item.moxman-last {
  margin-left: 2px;
}
.moxman-iframe {
  border: 0 solid #9e9e9e;
  width: 100%;
  height: 100%;
}
.moxman-fullscreen {
  border: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
  background: #ffffff;
  height: 100%;
}
div.moxman-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
}
#moxman-modal-block {
  opacity: 0;
  filter: alpha(opacity=0);
  zoom: 1;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #000000;
}
#moxman-modal-block.moxman-in {
  opacity: 0.3;
  filter: alpha(opacity=30);
  zoom: 1;
}
.moxman-window-move {
  cursor: move;
}
.moxman-window {
  box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background: transparent;
  background: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  opacity: 0;
  transition: opacity 150ms ease-in;
}
.moxman-window.moxman-in {
  opacity: 1;
}
.moxman-window-head {
  padding: 9px 15px;
  border-bottom: 1px solid #c5c5c5;
  position: relative;
}
.moxman-window-head .moxman-close {
  position: absolute;
  right: 15px;
  top: 9px;
  font-size: 20px;
  font-weight: bold;
  line-height: 20px;
  color: #858585;
  cursor: pointer;
  height: 20px;
  overflow: hidden;
}
.moxman-close:hover {
  color: #adadad;
}
.moxman-window-head .moxman-title {
  line-height: 20px;
  font-size: 20px;
  font-weight: bold;
  text-rendering: optimizelegibility;
  padding-right: 10px;
}
.moxman-window .moxman-container-body {
  display: block;
}
.moxman-foot {
  display: block;
  background-color: #ffffff;
  border-top: 1px solid #c5c5c5;
}
.moxman-window-head .moxman-dragh {
  position: absolute;
  top: 0;
  left: 0;
  cursor: move;
  width: 90%;
  height: 100%;
}
.moxman-window iframe {
  width: 100%;
  height: 100%;
}
.moxman-rtl .moxman-window-head .moxman-close {
  position: absolute;
  right: auto;
  left: 15px;
}
.moxman-rtl .moxman-window-head .moxman-dragh {
  left: auto;
  right: 0;
}
.moxman-rtl .moxman-window-head .moxman-title {
  direction: rtl;
  text-align: right;
}
.moxman-imagecanvas {
  overflow: auto;
}
.moxman-imagecanvas canvas,
.moxman-imagecanvas img {
  box-shadow: 0px 2px 5px 3px #666666;
  display: block;
  max-width: none;
}
.moxman-imagecanvas-handle {
  position: absolute;
  border: 1px solid black;
  background: #FFF;
  width: 8px;
  height: 8px;
  margin: -4px 0 0 -4px;
  z-index: 10000;
  display: block;
}
@media (min-device-width: 800px) and (max-device-width: 1280px), (min-device-width: 768px) and (max-device-width: 1024px) {
  .moxman-imagecanvas-handle {
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
  }
}
.moxman-imagecanvas-handle:hover,
.moxman-imagecanvas-handle-selected {
  background: #000;
}
.moxman-imagecanvas-ants {
  opacity: 60;
  filter: alpha(opacity=6000);
  zoom: 1;
  display: block;
  position: absolute;
  background: url('img/ants.gif') #ffffff;
  width: 1px;
  height: 1px;
  z-index: 1001;
}
.moxman-imagecanvas-middle {
  display: block;
  position: absolute;
  overflow: hidden;
  z-index: 1000;
  background: transparent;
}
.moxman-imagecanvas-middle * {
  position: absolute;
  cursor: move;
}
.moxman-imagecanvas-overlay {
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
}
.moxman-imagecanvas-clip {
  position: absolute;
  display: block;
  background: #000;
}
.moxman-imagecanvas-crop .moxman-imagecanvas-overlay {
  display: block;
}
.moxman-imagecanvas-crop .moxman-imagecanvas-clip * {
  opacity: 60;
  filter: alpha(opacity=6000);
  zoom: 1;
}
.moxman-imagecanvas-circle {
  position: absolute;
  display: block;
  background: black;
  border: 1px solid white;
  opacity: 0.3;
}
.moxman-label {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
  overflow: hidden;
}
.moxman-label.moxman-autoscroll {
  overflow: auto;
}
.moxman-label.moxman-disabled {
  color: #aaaaaa;
}
.moxman-label.moxman-multiline {
  white-space: pre-wrap;
}
.moxman-label.moxman-error {
  color: #aa0000;
}
.moxman-rtl .moxman-label {
  text-align: right;
  direction: rtl;
}
/* MenuBar */
.moxman-menubar .moxman-menubtn {
  border-color: transparent;
  background: transparent;
  box-shadow: none;
  filter: none;
}
.moxman-menubar {
  border: 1px solid #c4c4c4;
}
.moxman-menubar .moxman-menubtn button span {
  color: #333333;
}
.moxman-menubar .moxman-caret {
  border-top-color: #333333;
}
.moxman-menubar .moxman-menubtn:hover,
.moxman-menubar .moxman-menubtn.moxman-active,
.moxman-menubar .moxman-menubtn:focus {
  border-color: transparent;
  background: #e6e6e6;
  filter: none;
  box-shadow: none;
}
/* MenuButton */
.moxman-menubtn span {
  color: #333333;
  margin-right: 2px;
  line-height: 20px;
  *line-height: 16px;
}
.moxman-menubtn.moxman-btn-small span {
  font-size: 12px;
}
.moxman-menubtn.moxman-fixed-width span {
  display: inline-block;
  overflow-x: hidden;
  text-overflow: ellipsis;
  width: 90px;
}
.moxman-menubtn.moxman-fixed-width.moxman-btn-small span {
  width: 70px;
}
.moxman-menubtn .moxman-caret {
  *margin-top: 6px;
}
.moxman-rtl .moxman-menubtn button {
  direction: rtl;
  text-align: right;
}
.moxman-menu-item {
  display: block;
  padding: 6px 15px 6px 12px;
  clear: both;
  font-weight: normal;
  line-height: 20px;
  color: #333333;
  white-space: nowrap;
  cursor: pointer;
  line-height: normal;
  border-left: 4px solid transparent;
  margin-bottom: 1px;
}
.moxman-menu-item .moxman-ico,
.moxman-menu-item .moxman-text {
  color: #333333;
}
.moxman-menu-item.moxman-disabled .moxman-text,
.moxman-menu-item.moxman-disabled .moxman-ico {
  color: #adadad;
}
.moxman-menu-item:hover .moxman-text,
.moxman-menu-item.moxman-selected .moxman-text,
.moxman-menu-item:focus .moxman-text {
  color: white;
}
.moxman-menu-item:hover .moxman-ico,
.moxman-menu-item.moxman-selected .moxman-ico,
.moxman-menu-item:focus .moxman-ico {
  color: white;
}
.moxman-menu-item.moxman-disabled:hover {
  background: #cccccc;
}
.moxman-menu-shortcut {
  display: inline-block;
  color: #adadad;
}
.moxman-menu-shortcut {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  padding: 0 15px 0 20px;
}
.moxman-menu-item:hover .moxman-menu-shortcut,
.moxman-menu-item.moxman-selected .moxman-menu-shortcut,
.moxman-menu-item:focus .moxman-menu-shortcut {
  color: white;
}
.moxman-menu-item .moxman-caret {
  margin-top: 4px;
  *margin-top: 3px;
  margin-right: 6px;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-left: 4px solid #333333;
}
.moxman-menu-item.moxman-selected .moxman-caret,
.moxman-menu-item:focus .moxman-caret,
.moxman-menu-item:hover .moxman-caret {
  border-left-color: white;
}
.moxman-menu-align .moxman-menu-shortcut {
  *margin-top: -2px;
}
.moxman-menu-align .moxman-menu-shortcut,
.moxman-menu-align .moxman-caret {
  position: absolute;
  right: 0;
}
.moxman-menu-item.moxman-active i {
  visibility: visible;
}
.moxman-menu-item-normal.moxman-active {
  background-color: #c8def4;
}
.moxman-menu-item-preview.moxman-active {
  border-left: 5px solid #aaaaaa;
}
.moxman-menu-item-normal.moxman-active .moxman-text {
  color: #333333;
}
.moxman-menu-item-normal.moxman-active:hover .moxman-text,
.moxman-menu-item-normal.moxman-active:hover .moxman-ico {
  color: white;
}
.moxman-menu-item-normal.moxman-active:focus .moxman-text,
.moxman-menu-item-normal.moxman-active:focus .moxman-ico {
  color: white;
}
.moxman-menu-item:hover,
.moxman-menu-item.moxman-selected,
.moxman-menu-item:focus {
  text-decoration: none;
  color: white;
  background-color: #0081c2;
  background-image: linear-gradient(to bottom, #0088cc, #0077b3);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0077b3', GradientType=0);
  zoom: 1;
}
div.moxman-menu .moxman-menu-item-sep,
.moxman-menu-item-sep:hover {
  border: 0;
  padding: 0;
  height: 1px;
  margin: 9px 1px;
  overflow: hidden;
  background: #cbcbcb;
  border-bottom: 1px solid #ffffff;
  cursor: default;
  filter: none;
}
.moxman-menu.moxman-rtl {
  direction: rtl;
}
.moxman-rtl .moxman-menu-item {
  text-align: right;
  direction: rtl;
  padding: 6px 12px 6px 15px;
}
.moxman-menu-align.moxman-rtl .moxman-menu-shortcut,
.moxman-menu-align.moxman-rtl .moxman-caret {
  right: auto;
  left: 0;
}
.moxman-rtl .moxman-menu-item .moxman-caret {
  margin-left: 6px;
  margin-right: 0;
  border-right: 4px solid #333333;
  border-left: 0;
}
.moxman-rtl .moxman-menu-item.moxman-selected .moxman-caret,
.moxman-rtl .moxman-menu-item:focus .moxman-caret,
.moxman-rtl .moxman-menu-item:hover .moxman-caret {
  border-left-color: transparent;
  border-right-color: white;
}
.moxman-menu {
  position: absolute;
  left: 0;
  top: 0;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background: transparent;
  z-index: 1000;
  padding: 5px 0 5px 0;
  margin: 2px 0 0;
  min-width: 160px;
  background: #ffffff;
  border: 1px solid #989898;
  border: 1px solid rgba(0, 0, 0, 0.2);
  z-index: 1002;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  max-height: 420px;
  overflow: auto;
  overflow-x: hidden;
}
.moxman-menu i {
  display: none;
}
.moxman-menu-has-icons i {
  display: inline-block;
  *display: inline;
}
.moxman-menu-sub-tr-tl {
  margin: -6px 0 0 -1px;
}
.moxman-menu-sub-br-bl {
  margin: 6px 0 0 -1px;
}
.moxman-menu-sub-tl-tr {
  margin: -6px 0 0 1px;
}
.moxman-menu-sub-bl-br {
  margin: 6px 0 0 1px;
}
.moxman-listbox button {
  text-align: left;
  padding-right: 20px;
  position: relative;
}
.moxman-listbox .moxman-caret {
  position: absolute;
  margin-top: -2px;
  right: 8px;
  top: 50%;
}
.moxman-rtl .moxman-listbox .moxman-caret {
  right: auto;
  left: 8px;
}
.moxman-rtl .moxman-listbox button {
  padding-right: 10px;
  padding-left: 20px;
}
.moxman-notification {
  position: absolute;
  bottom: 10px;
  right: 30px;
  background: #fdfdfd;
  padding: 10px;
  border: 1px solid #b1b1b1;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25) rgba(0, 0, 0, 0.25);
  min-width: 250px;
  transition: bottom 150ms ease-in, opacity 150ms ease-in;
}
.moxman-notification.moxman-fadeout {
  opacity: 0;
}
.moxman-notification .moxman-label {
  margin-right: 10px;
}
.moxman-notification .moxman-close {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 20px;
  font-weight: bold;
  line-height: 20px;
  color: #858585;
  cursor: pointer;
}
.moxman-notification .moxman-progress {
  display: block;
  margin-top: 5px;
  width: 100%;
}
.moxman-path {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  padding: 8px;
  white-space: normal;
}
.moxman-path .moxman-txt {
  display: inline-block;
  padding-right: 3px;
}
.moxman-path .moxman-path-body {
  display: inline-block;
}
.moxman-path-item {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  cursor: pointer;
  color: #333333;
}
.moxman-path-item:hover {
  text-decoration: underline;
}
.moxman-path-item:focus {
  background: #666666;
  color: #ffffff;
}
.moxman-path .moxman-divider {
  display: inline;
}
.moxman-disabled .moxman-path-item {
  color: #aaaaaa;
}
.moxman-rtl .moxman-path {
  direction: rtl;
}
.moxman-progress {
  display: inline-block;
  overflow: hidden;
  position: relative;
  width: 100px;
  height: 20px;
  border: 1px solid #cccccc;
}
.moxman-progress .moxman-text {
  position: absolute;
  top: 50%;
  left: 0;
  text-align: center;
  margin-top: -8px;
  font-size: 12px;
  width: 100%;
  color: #333333;
  text-shadow: 1px 1px white;
}
.moxman-bar {
  display: block;
  width: 0%;
  height: 100%;
  background-color: #d7d7d7;
  background-image: linear-gradient(to bottom, #dfdfdf, #cccccc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffdfdfdf', endColorstr='#ffcccccc', GradientType=0);
  zoom: 1;
  transition: width 0.2s ease;
}
.moxman-slider {
  border: 1px solid #aaaaaa;
  background: #eeeeee;
  width: 100px;
  height: 10px;
  position: relative;
  display: block;
}
.moxman-slider.moxman-vertical {
  width: 10px;
  height: 100px;
}
.moxman-slider-handle {
  border: 1px solid #bbbbbb;
  background: #dddddd;
  display: block;
  width: 13px;
  height: 13px;
  position: absolute;
  top: 0;
  left: 0;
  margin-left: -1px;
  margin-top: -2px;
}
.moxman-spacer {
  visibility: hidden;
}
.moxman-splitbtn .moxman-open {
  border-left: 1px solid transparent;
  border-right: 1px solid transparent;
}
.moxman-splitbtn:hover .moxman-open {
  border-left-color: #bdbdbd;
  border-right-color: #bdbdbd;
}
.moxman-splitbtn button {
  padding-right: 4px;
}
.moxman-splitbtn .moxman-open {
  padding-left: 4px;
}
.moxman-splitbtn .moxman-open.moxman-active {
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
}
.moxman-splitbtn.moxman-btn-small .moxman-open {
  padding: 0 3px 0 3px;
}
.moxman-rtl .moxman-splitbtn {
  direction: rtl;
  text-align: right;
}
.moxman-rtl .moxman-splitbtn button {
  padding-right: 10px;
  padding-left: 10px;
}
.moxman-rtl .moxman-splitbtn .moxman-open {
  padding-left: 4px;
  padding-right: 4px;
}
.moxman-stack-layout-item {
  display: block;
}
.moxman-tabs {
  display: block;
  border-bottom: 1px solid #c5c5c5;
}
.moxman-tab {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  border: 1px solid #c5c5c5;
  border-width: 0 1px 0 0;
  background: #e3e3e3;
  padding: 8px;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
  height: 13px;
  cursor: pointer;
}
.moxman-tab:hover {
  background: #fdfdfd;
}
.moxman-tab.moxman-active {
  background: #fdfdfd;
  border-bottom-color: transparent;
  margin-bottom: -1px;
  height: 14px;
}
.moxman-rtl .moxman-tabs {
  text-align: right;
  direction: rtl;
}
.moxman-rtl .moxman-tab {
  border-width: 0 0 0 1px;
}
.moxman-textbox {
  background: #ffffff;
  border: 1px solid #c5c5c5;
  border-radius: 0;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  display: inline-block;
  transition: border linear .2s, box-shadow linear .2s;
  height: 28px;
  resize: none;
  padding: 0 4px 0 4px;
  white-space: pre-wrap;
  *white-space: pre;
  color: #333333;
}
.moxman-textbox:focus,
.moxman-textbox.moxman-focus {
  border-color: rgba(82, 168, 236, 0.8);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.65);
}
.moxman-placeholder .moxman-textbox {
  color: #aaaaaa;
}
.moxman-textbox.moxman-multiline {
  padding: 4px;
}
.moxman-textbox.moxman-disabled {
  color: #adadad;
}
.moxman-rtl .moxman-textbox {
  text-align: right;
  direction: rtl;
}
.moxman-throbber {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.6;
  filter: alpha(opacity=60);
  zoom: 1;
  background: #ffffff url('img/loader.gif') no-repeat center center;
}
.moxman-throbber-inline {
  position: static;
  height: 50px;
}
.moxman-thumb {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  width: 100px;
  height: 100px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  border: 1px solid #aaaaaa;
  opacity: 70;
  filter: alpha(opacity=7000);
  zoom: 1;
  vertical-align: middle;
  line-height: 100px;
  text-align: center;
}
.moxman-thumb img {
  vertical-align: middle;
  max-height: 77px;
  max-width: 100px;
  margin-top: -24px;
  *margin-top: 0;
}
.moxman-thumb.moxman-loaded img {
  position: absolute;
  visibility: visible;
}
.moxman-thumb:hover,
.moxman-thumb.moxman-checked {
  opacity: 100;
  filter: alpha(opacity=10000);
  zoom: 1;
  outline: 2px solid #00709f;
}
.moxman-thumb:hover {
  outline: 2px solid #00709f;
}
.moxman-thumb.moxman-active {
  border: 1px solid #52a8ec;
  box-shadow: 0 0 5px #52a8ec;
}
.moxman-thumb.moxman-active .moxman-info,
.moxman-thumb.moxman-checked .moxman-info {
  background: #333333;
}
.moxman-info {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #333333;
  color: #ffffff;
  padding: 3px;
  opacity: 80;
  filter: alpha(opacity=8000);
  zoom: 1;
}
.moxman-thumb .moxman-i-checkbox,
.moxman-thumb.moxman-checked .moxman-i-checkbox {
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  position: absolute;
  top: 1px;
  right: 6px;
  margin: 1px;
  border: 1px solid #ffffff;
  background: transparent;
  background: #333333;
  color: #ffffff;
}
.moxman-thumb .moxman-i-checkbox {
  display: none;
}
.moxman-thumb:hover .moxman-i-checkbox,
.moxman-thumb.moxman-checked .moxman-i-checkbox {
  display: block;
}
.moxman-info .moxman-i-checkbox:before {
  color: #ffffff;
}
.moxman-thumb .moxman-ico {
  color: #333333;
}
.moxman-treeitem .moxman-treeitem-title {
  display: block;
  vertical-align: middle;
  padding: 0 4px 0 14px;
  cursor: pointer;
  line-height: 24px;
}
@media (min-device-width: 800px) and (max-device-width: 1280px), (min-device-width: 768px) and (max-device-width: 1024px) {
  .moxman-treeitem .moxman-treeitem-title {
    line-height: 31px;
  }
}
.moxman-treeitem .moxman-text {
  line-height: 24px;
}
@media (min-device-width: 800px) and (max-device-width: 1280px), (min-device-width: 768px) and (max-device-width: 1024px) {
  .moxman-treeitem .moxman-text {
    line-height: 31px;
  }
}
.moxman-treeitem-body {
  display: none;
}
.moxman-treeitem-title .moxman-ico {
  display: inline-block;
  color: #333333;
}
.moxman-treeitem-expanded {
  display: block;
}
.moxman-treeitem .moxman-container-body {
  height: auto;
}
.moxman-treeitem .moxman-text {
  padding-left: 4px;
}
.moxman-treeitem .moxman-expander {
  width: 9px;
  height: 9px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 3px;
}
.moxman-treeitem-title-expanded .moxman-expander {
  background-position: -9px 0;
}
.moxman-treeitem .moxman-hidden {
  background: none;
}
.moxman-treeitem-title-fixed .moxman-text {
  font-weight: bold;
  padding-left: 0;
}
.moxman-treeitem-title-fixed .moxman-expander {
  display: none;
}
.moxman-treeitem-title-selected {
  background-color: #0081c2;
  background-image: linear-gradient(to bottom, #0088cc, #0077b3);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0077b3', GradientType=0);
  zoom: 1;
}
.moxman-treeitem-title-selected .moxman-text {
  color: white;
}
@media all and (min-width: 0) {
  .moxman-treeitem-title-selected .moxman-ico {
    color: white;
  }
}
.moxman-viewport {
  border: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
  background: #FFF;
  height: 100%;
}
div.moxman-viewport {
  position: absolute;
  top: 0;
  left: 0;
}
.moxman-filelist {
  border: 1px solid #9e9e9e;
}
.moxman-filelist-cell .moxman-caret {
  display: none;
}
.moxman-filelist-cell .moxman-up,
.moxman-filelist-cell .moxman-down {
  display: block;
}
div.moxman-filelist-head {
  overflow: hidden;
  background-color: #f0f0f0;
  background-image: linear-gradient(to bottom, #fdfdfd, #dddddd);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fffdfdfd', endColorstr='#ffdddddd', GradientType=0);
  zoom: 1;
  border-bottom: 1px solid #9e9e9e;
}
.moxman-filelist-body {
  -webkit-overflow-scrolling: touch;
  display: block;
  overflow: scroll;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  background: #ffffff;
  height: 1px;
  overflow-y: scroll;
}
.moxman-filelist-body tbody .moxman-filelist-cell {
  display: inline-block;
}
.moxman-filelist td,
.moxman-filelist th {
  color: #333333;
  margin: 0;
}
.moxman-filelist .moxman-ico {
  color: #333333;
}
.moxman-filelist td,
.moxman-filelist th {
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
}
.moxman-filelist-cell {
  padding: 5px;
}
.moxman-filelist-head .moxman-filelist-cell {
  position: relative;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
}
.moxman-filelist-head-item .moxman-caret {
  position: absolute;
  right: 3px;
  top: 12px;
}
.moxman-filelist-body tbody td {
  border-bottom: 1px solid #cfcfcf;
  height: 28px;
  vertical-align: middle;
}
@media (min-device-width: 800px) and (max-device-width: 1280px), (min-device-width: 768px) and (max-device-width: 1024px) {
  .moxman-filelist-body tbody td {
    height: 32px;
  }
}
.moxman-filelist-row td:nth-child(2) div.moxman-txt:hover span {
  text-decoration: underline;
}
.moxman-filelist-row td:nth-child(2) div.moxman-txt {
  cursor: pointer;
}
.moxman-filelist table {
  border-spacing: 0;
  width: 100%;
  margin: 0;
  padding: 0;
  table-layout: fixed;
}
.moxman-filelist td {
  overflow: hidden;
}
.moxman-filelist thead td .moxman-filelist-cell {
  padding: 0 3px 0 5px;
  line-height: 0;
}
.moxman-filelist thead td {
  line-height: 0;
  overflow: hidden;
}
.moxman-filelist .moxman-filelist-odd {
  background-color: #f8f8f8;
}
.moxman-filelist-cell i {
  margin-right: 3px;
}
.moxman-checkbox-column i {
  margin-right: 0;
}
.moxman-filelist tr.moxman-checked,
.moxman-filelist tr.moxman-checked td {
  background-color: #54a4cc;
  background-image: linear-gradient(to bottom, #54a4cc, #54a4cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff54a4cc', endColorstr='#ff54a4cc', GradientType=0);
  zoom: 1;
  color: white;
}
.moxman-checkbox-column {
  text-align: center;
  width: 25px;
}
@media (min-device-width: 800px) and (max-device-width: 1280px), (min-device-width: 768px) and (max-device-width: 1024px) {
  .moxman-checkbox-column {
    width: 40px;
  }
}
.moxman-checkbox-column .moxman-filelist-cell {
  display: inline-block;
}
@media all and (min-width: 0) {
  .moxman-filelist tr.moxman-checked .moxman-ico {
    color: white;
  }
}
.moxman-filelist tr.moxman-checked div .moxman-txt {
  color: white;
}
.moxman-filelist tr.moxman-checked .moxman-i-checkbox {
  opacity: 1;
  filter: alpha(opacity=100);
  zoom: 1;
  color: #333333;
}
.moxman-filelist-cell .moxman-inter,
.moxman-filelist-cell .moxman-checked {
  font-size: 16px;
  line-height: 16px;
  text-indent: 0;
}
.moxman-thumbnailview {
  border: 0 solid #9E9E9E;
  overflow: auto;
  overflow-x: hidden;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}
.moxman-thumbnailview .moxman-thumb {
  margin: 4px;
  width: 100px;
  height: 100px;
}
.moxman-thumbnailview .moxman-checked .moxman-checkbox {
  font-size: 16px;
  line-height: 16px;
  text-indent: 0;
}
.moxman-thumbnailview i.moxman-thumb {
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -10px 0 0 -10px;
}
.moxman-thumbnailview i.moxman-thumb {
  margin: 0;
  position: static;
  width: 100%;
  height: 100%;
  font-size: 48px;
  text-align: center;
  margin-top: -24px;
  *margin-top: -10px;
  *margin-left: 4px;
  vertical-align: middle;
  line-height: 100px;
  border: 0;
}
/* Icons */
@font-face {
  font-family: 'moxman';
  src: url('fonts/icomoon.eot');
  src: url('fonts/icomoon.eot?#iefix') format('embedded-opentype'), url('fonts/icomoon.svg#icomoon') format('svg'), url('fonts/icomoon.woff') format('woff'), url('fonts/icomoon.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
.moxman-ico {
  font-family: 'moxman', Arial;
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 16px;
  vertical-align: text-top;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  background: transparent center center;
  width: 16px;
  height: 16px;
  color: #333333;
  zoom: 1;
}
.moxman-i-manage:before {
  content: "\e009";
}
.moxman-i-search:before {
  content: "\e00a";
}
.moxman-i-folder:before {
  content: "\e00b";
}
.moxman-i-thumbs:before {
  content: "\e00c";
}
.moxman-i-list:before {
  content: "\e00d";
}
.moxman-i-cloud-download:before {
  content: "\e00e";
}
.moxman-i-delete:before {
  content: "\e00f";
}
.moxman-i-paste:before {
  content: "\e010";
}
.moxman-i-copy:before {
  content: "\e011";
}
.moxman-i-cut:before {
  content: "\e012";
}
.moxman-i-create:before {
  content: "\e013";
}
.moxman-i-download:before {
  content: "\e014";
}
.moxman-i-upload:before {
  content: "\e015";
}
.moxman-i-refresh:before {
  content: "\e016";
}
.moxman-i-checkbox:before {
  content: "\e017";
}
.moxman-i-selected:before {
  content: "\e017";
}
.moxman-i-file:before {
  content: "\e018";
}
.moxman-i-save:before {
  content: "\e019";
}
.moxman-i-undo:before {
  content: "\e01a";
}
.moxman-i-fullscreen:before {
  content: "\e01b";
}
.moxman-i-redo:before {
  content: "\e01c";
}
.moxman-i-flip-v:before {
  content: "\e01d";
}
.moxman-i-flip-h:before {
  content: "\e01e";
}
.moxman-i-rotate-left:before {
  content: "\e01f";
}
.moxman-i-rotate-right:before {
  content: "\e020";
}
.moxman-i-history:before {
  content: "\e021";
}
.moxman-i-favorites:before {
  content: "\e022";
}
.moxman-i-parent:before {
  content: "\e01a";
}
.moxman-i-file-zip:before {
  content: "\e100";
}
.moxman-i-file-text:before {
  content: "\e101";
}
.moxman-i-file-code:before {
  content: "\e102";
}
.moxman-i-file-xml:before {
  content: "\e103";
}
.moxman-i-file-powerpoint:before {
  content: "\e104";
}
.moxman-i-file-excel:before {
  content: "\e105";
}
.moxman-i-file-word:before {
  content: "\e106";
}
.moxman-i-file-openoffice:before {
  content: "\e107";
}
.moxman-i-file-pdf:before {
  content: "\e108";
}
.moxman-i-file-image:before {
  content: "\e001";
}
.moxman-i-dropbox:before {
  content: "\e029";
}
.moxman-i-skydrive:before {
  content: "\e02a";
}
.moxman-i-logout:before {
  content: "\e02b";
}
.moxman-inter:before {
  content: "\e023";
}
.moxman-i-selected {
  visibility: hidden;
}

/*# sourceMappingURL=skin.min.css.map */