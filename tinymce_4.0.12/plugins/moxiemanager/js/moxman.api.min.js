// 2.0.5 (2015-06-30)
!function(e,t){"use strict";function n(e,t){for(var n,i=[],r=0;r<e.length;++r){if(n=s[e[r]]||o(e[r]),!n)throw"module definition dependecy not found: "+e[r];i.push(n)}t.apply(null,i)}function i(e,i,r){if("string"!=typeof e)throw"invalid module definition, module id must be defined and be a string";if(i===t)throw"invalid module definition, dependencies must be specified";if(r===t)throw"invalid module definition, definition function must be specified";n(i,function(){s[e]=r.apply(null,arguments)})}function r(e){return!!s[e]}function o(t){for(var n=e,i=t.split(/[.\/]/),r=0;r<i.length;++r){if(!n[i[r]])return;n=n[i[r]]}return n}function a(n){for(var i=0;i<n.length;i++){for(var r=e,o=n[i],a=o.split(/[.\/]/),l=0;l<a.length-1;++l)r[a[l]]===t&&(r[a[l]]={}),r=r[a[l]];r[a[a.length-1]]=s[o]}}var s={},l="moxman/polyfills/FunctionBind",u="moxman/polyfills/String",c="moxman/util/Tools",d="moxman/util/Class",f="moxman/ui/Layout",h="moxman/ui/AbsoluteLayout",p="moxman/ui/BorderLayout",m="moxman/ui/BoxUtils",g="moxman/util/EventDispatcher",v="moxman/data/Binding",y="moxman/util/Observable",x="moxman/data/ObservableObject",b="moxman/ui/Selector",w="moxman/ui/Collection",_="moxman/ui/DomUtils",E="moxman/dom/EventUtils",R="moxman/dom/Sizzle",C="moxman/Env",T="moxman/dom/DomQuery",S="moxman/ui/ClassList",A="moxman/ui/ReflowQueue",k="moxman/ui/Control",D="moxman/ui/Movable",N="moxman/ui/Tooltip",I="moxman/ui/Widget",M="moxman/ui/Button",F="moxman/ui/Factory",P="moxman/ui/KeyboardNavigation",O="moxman/ui/Container",H="moxman/ui/ButtonGroup",L="moxman/ui/Carousel",z="moxman/ui/Checkbox",B="moxman/ui/CheckboxGroup",W="moxman/ui/ComboBox",U="moxman/ui/DragHelper",j="moxman/ui/FormItem",V="moxman/ui/Form",q="moxman/ui/FieldSet",X="moxman/ui/FitLayout",Y="moxman/ui/FlexLayout",$="moxman/ui/Scrollable",G="moxman/ui/Panel",J="moxman/ui/Resizable",K="moxman/ui/FloatPanel",Z="moxman/ui/FlowLayout",Q="moxman/ui/GridLayout",ee="moxman/ui/Iframe",te="moxman/gfx/Shader",ne="moxman/gfx/Texture",ie="moxman/gfx/ImageEditor",re="moxman/util/Json",oe="moxman/util/Xhr",ae="moxman/util/Promise",se="moxman/util/Auth",le="moxman/util/JsonRpc",ue="moxman/util/Path",ce="moxie/core/utils/Basic",de="moxie/core/Exceptions",fe="moxie/core/EventTarget",he="moxie/core/utils/Encode",pe="moxie/core/utils/Url",me="moxie/core/utils/Env",ge="moxie/core/utils/Dom",ve="moxie/runtime/Runtime",ye="moxie/runtime/RuntimeClient",xe="moxie/runtime/RuntimeTarget",be="moxie/file/Blob",we="moxie/file/FileReaderSync",_e="moxie/xhr/FormData",Ee="moxie/core/I18n",Re="moxie/core/utils/Mime",Ce="moxie/xhr/XMLHttpRequest",Te="moxman/ui/Window",Se="moxman/ui/MessageBox",Ae="moxman/util/AsyncTools",ke="moxman/util/I18n",De="moxman/views/DialogHelper",Ne="moxman/data/Model",Ie="moxman/vfs/FileConfig",Me="moxman/vfs/File",Fe="moxman/data/ObservableArray",Pe="moxman/data/ModelCollection",Oe="moxman/vfs/FileCollection",He="moxman/components/ConfirmOverwriteHelper",Le="moxman/ui/ImageCanvas",ze="moxman/ui/Label",Be="moxman/ui/Toolbar",We="moxman/ui/MenuBar",Ue="moxman/ui/MenuButton",je="moxman/ui/MenuItem",Ve="moxman/ui/Menu",qe="moxman/ui/ListBox",Xe="moxman/ui/Notifier",Ye="moxman/ui/PanelButton",$e="moxman/ui/Path",Ge="moxman/ui/Progress",Je="moxman/ui/Radio",Ke="moxman/ui/RadioGroup",Ze="moxman/ui/Slider",Qe="moxman/ui/Spacer",et="moxman/ui/SplitButton",tt="moxman/ui/StackLayout",nt="moxman/ui/TabPanel",it="moxman/ui/TextBox",rt="moxman/ui/Throbber",ot="moxman/ui/Thumb",at="moxman/ui/TreeItem",st="moxman/ui/TreeView",lt="moxman/ui/ViewPort",ut="moxman/util/Actions",ct="moxman/util/DateFormatter",dt="moxman/util/Extensions",ft="moxman/util/FileSize",ht="moxman/util/Fullscreen",pt="moxman/util/ImageSize",mt="moxman/util/Loader",gt="moxman/util/Shortcuts",vt="moxman/util/Uri",yt="moxman/util/WidgetHelper",xt="moxman/vfs/ErrorCodes",bt="moxman/vfs/FileJson",wt="moxman/vfs/FileSystem",_t="moxman/vfs/FileSystemCollection",Et="moxman/vfs/Notification",Rt="moxman/vfs/NotificationCollection",Ct="moxie/file/File",Tt="moxie/file/FileInput",St="moxman/vfs/Uploader",At="moxman/components/CreateMenuButton",kt="moxman/components/ManageMenu",Dt="moxman/components/ManageMenuButton",Nt="moxman/components/UploadMenuButton",It="moxman/components/SortMenuButton",Mt="moxman/components/MainToolbar",Ft="moxman/components/FileSystemPanel",Pt="moxman/components/FileList",Ot="moxman/components/ThumbList",Ht="moxman/components/FileListPanel",Lt="moxman/views/EditImageView",zt="moxman/views/EditTextView",Bt="moxman/views/CreateDirView",Wt="moxman/views/CreateDocView",Ut="moxman/views/RenameView",jt="moxman/views/ZipView",Vt="moxman/views/ViewFileView",qt="moxman/views/ViewImageView",Xt="moxman/views/UploadView",Yt="moxman/interop/GooglePicker",$t="moxman/interop/DropBoxChooser",Gt="moxman/interop/OneDrivePicker",Jt="moxman/actions/BrowseActions",Kt="moxman/views/BrowseView",Zt="moxman/views/InstallView",Qt="moxman/views/LoginView",en="moxman/Loader",tn="moxman/Dialogs",nn="moxie/file/FileDrop",rn="moxie/runtime/html5/Runtime",on="moxie/runtime/html5/file/Blob",an="moxie/core/utils/Events",sn="moxie/runtime/html5/file/FileDrop",ln="moxie/runtime/html5/file/FileInput",un="moxie/runtime/html5/xhr/XMLHttpRequest",cn="moxie/runtime/flash/Runtime",dn="moxie/runtime/flash/file/Blob",fn="moxie/runtime/flash/file/FileInput",hn="moxie/runtime/Transporter",pn="moxie/runtime/flash/xhr/XMLHttpRequest",mn="moxie/runtime/flash/file/FileReaderSync";i(l,[],function(){Function.prototype.bind||(Function.prototype.bind=function(e){function t(){}function n(){var n;return n=this instanceof t&&e?this:e,i.apply(n,r.concat(Array.prototype.slice.call(arguments)))}var i=this,r=Array.prototype.slice.call(arguments,1);return t.prototype=this.prototype,n.prototype=new t,n})}),i(u,[],function(){if(!String.prototype.trim){var e=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;String.prototype.trim=function(){return this.replace(e,"")}}}),i(c,[],function(){function e(n,i){var r,o;if(n===i)return!0;if(null===n||null===i)return n===i;if("object"!=typeof n||"object"!=typeof i)return n===i;if(t.isArray(i)){if(n.length!==i.length)return!1;for(r=n.length;r--;)if(!e(n[r],i[r]))return!1}o={};for(r in i){if(!e(n[r],i[r]))return!1;o[r]=!0}for(r in n)if(!o[r]&&!e(n[r],i[r]))return!1;return!0}var t={makeMap:function(e,t,n){var i;for(e=e||[],t=t||",","string"==typeof e&&(e=e.split(t)),n=n||{},i=e.length;i--;)n[e[i]]={};return n},unique:function(e){for(var t=[],n=e.length,i;n--;)i=e[n],i.__checked||(t.push(i),i.__checked=1);for(n=t.length;n--;)delete t[n].__checked;return t},extend:function(e){var t=arguments,n,i,r;for(e=e||{},i=1;i<t.length;i++){n=t[i];for(r in n)e[r]=n[r]}return e},toArray:function(e){var t=[],n,i;for(n=0,i=e.length;i>n;n++)t[n]=e[n];return t},inArray:function(e,t){var n;if(t.indexOf)return t.indexOf(e);for(n=t.length;n--;)if(t[n]===e)return n;return-1},isArray:Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},each:function(e,t){var n,i,r,o;if(e)if(n=e.length,n===o){for(i in e)if(e.hasOwnProperty(i)&&t(e[i],i)===!1)return!1}else for(r=0;n>r;r++)if(t(e[r],r)===!1)return!1;return!0},filter:function(e,n){var i=[];return t.each(e,function(e){n(e)&&i.push(e)}),i},compose:function(){var e=arguments;return function(t){for(var n=e.length-1;n--;)t=e[n].call(this,t);return t}},curry:function n(e){var t=[].slice.call(arguments);return t.length-1>=e.length?e.apply(this,t.slice(1)):function(){return n.apply(this,t.concat([].slice.call(arguments)))}},not:function(e){return function(){return!e.apply(this,arguments)}},map:function i(e,t){var n,i=[];for(n=0;n<e.length;n++)i.push(t(e[n]));return i},isEqual:e,deepSplit:function(e,n){var i=[];return t.isArray(e)?t.each(e,function(e){i.concat(t.splitDeep(e,n))}):"string"==typeof e&&t.each(e.split(n||/[, ]/),function(e){e&&i.push(e)}),i}};return t}),i(d,[c],function(e){function t(){}var n,i;return t.extend=n=function(t){function r(){var e,t,n,r=this;if(!i&&(r.init&&r.init.apply(r,arguments),t=r.Mixins))for(e=t.length;e--;)n=t[e],n.init&&n.init.apply(r,arguments)}function o(){return this}function a(e,t){return function(){var n=this,i=n._super,r;return n._super=l[e],r=t.apply(n,arguments),n._super=i,r}}var s=this,l=s.prototype,u,c,d;i=!0,u=new s,i=!1,t.Mixins&&(e.each(t.Mixins,function(e){e=e;for(var n in e)"init"!==n&&(t[n]=e[n])}),l.Mixins&&(t.Mixins=l.Mixins.concat(t.Mixins))),t.Methods&&e.each(t.Methods.split(","),function(e){t[e]=o}),t.Properties&&e.each(t.Properties.split(","),function(e){var n="_"+e;t[e]=function(e){var t=this,i;return e!==i?(t[n]=e,t):t[n]}}),t.Statics&&e.each(t.Statics,function(e,t){r[t]=e}),t.Defaults&&l.Defaults&&(t.Defaults=e.extend({},l.Defaults,t.Defaults));for(c in t)d=t[c],"function"==typeof d&&l[c]?u[c]=a(c,d):u[c]=d;return r.prototype=u,r.constructor=r,r.extend=n,r},t}),i(f,[d,c],function(e,t){return e.extend({Defaults:{firstControlClass:"first",lastControlClass:"last"},init:function(e){this.settings=t.extend({},this.Defaults,e)},preRender:function(e){e.bodyClasses.add(this.settings.containerClass)},applyClasses:function(e){var t=this,n=t.settings,i,r,o,a;i=n.firstControlClass,r=n.lastControlClass,e.each(function(e){e.classes.remove(i).remove(r).add(n.controlClass),e.visible()&&(o||(o=e),a=e)}),o&&o.classes.add(i),a&&a.classes.add(r)},renderHtml:function(e){var t=this,n="";return t.applyClasses(e.items()),e.items().each(function(e){n+=e.renderHtml()}),n},recalc:function(){},postRender:function(){},isNative:function(){return!1}})}),i(h,[f],function(e){return e.extend({Defaults:{containerClass:"abs-layout",controlClass:"abs-layout-item"},recalc:function(e){e.items().filter(":visible").each(function(e){var t=e.settings;e.layoutRect({x:t.x,y:t.y,w:t.w,h:t.h}),e.recalc&&e.recalc()})},renderHtml:function(e){return'<div id="'+e._id+'-absend" class="'+e.classPrefix+'abs-end"></div>'+this._super(e)}})}),i(p,[h],function(e){return e.extend({recalc:function(e){function t(e,t,n,i,r){var o=e.marginBox;e.layoutRect({x:t+o.left,y:n+o.top,w:i+o.left+o.right,h:r+o.top+o.bottom,autoResize:!1}),e.recalc&&e.recalc()}var n,i,r,o,a,s,l,u,c,d,f,h,p,m,g,v,y,x,b,w,_;for(n=e.layoutRect(),m=e.paddingBox,i=n.innerW,r=n.innerH,s=m.left,l=m.top,u=i-(m.right+m.left),c=r-(m.bottom+m.top),d=e.items().filter(":visible"),h=0;h<d.length;h++)f=d[h],w=f.settings,_=f.layoutRect(),p=w.region,o=_.w,a=_.h,o>0&&1>o&&(o=i*o),a>0&&1>a&&(a=r*a),g=w.minWidth,v=w.minHeight,y=w.maxWidth,x=w.maxHeight,o=g&&g>o?g:o,a=v&&v>a?v:a,o=y&&o>y?y:o,a=x&&a>x?x:a,"north"===p?(t(f,s,l,u,a),l+=a,c-=a):"east"===p?(t(f,i-o-m.right,l,o,c),u-=o):"south"===p?(t(f,s,r-a-m.bottom,u,a),c-=a):"west"===p?(t(f,s,l,o,c),s+=o,u-=o):b=f;if(!b)throw new Error("You must define a center region for border layout.");t(b,s,l,u,c)}})}),i(m,[],function(){return{parseBox:function(e){var t,n=10;if(e)return"number"==typeof e?(e=e||0,{top:e,left:e,bottom:e,right:e}):(e=e.split(" "),t=e.length,1===t?e[1]=e[2]=e[3]=e[0]:2===t?(e[2]=e[0],e[3]=e[1]):3===t&&(e[3]=e[1]),{top:parseInt(e[0],n)||0,right:parseInt(e[1],n)||0,bottom:parseInt(e[2],n)||0,left:parseInt(e[3],n)||0})},measureBox:function(e,t){function n(t){var n=document.defaultView;return n?(t=t.replace(/[A-Z]/g,function(e){return"-"+e}),n.getComputedStyle(e,null).getPropertyValue(t)):e.currentStyle[t]}function i(e){var t=parseFloat(n(e),10);return isNaN(t)?0:t}return{top:i(t+"TopWidth"),right:i(t+"RightWidth"),bottom:i(t+"BottomWidth"),left:i(t+"LeftWidth")}}}}),i(g,[c],function(e){function t(e){function t(){return!1}function n(){return!0}function i(i,r){var a,s,l,d;if(i=i.toLowerCase(),r=r||{},r.type=i,r.target||(r.target=u),r.preventDefault||(r.preventDefault=function(){r.isDefaultPrevented=n},r.stopPropagation=function(){r.isPropagationStopped=n},r.stopImmediatePropagation=function(){r.isImmediatePropagationStopped=n},r.isDefaultPrevented=t,r.isPropagationStopped=t,r.isImmediatePropagationStopped=t),e.beforeFire&&e.beforeFire(r),a=c[i])for(s=0,l=a.length;l>s;s++){if(a[s]=d=a[s],d.once&&o(i,d),r.isImmediatePropagationStopped())return r.stopPropagation(),r;if(d.call(u,r)===!1)return r.preventDefault(),r}return r}function r(e,n,i){var r,o,a;if(n===!1&&(n=t),n)for(o=e.toLowerCase().split(" "),a=o.length;a--;)e=o[a],r=c[e],r||(r=c[e]=[],d(e,!0)),i?r.unshift(n):r.push(n);return l}function o(e,t){var n,i,r,o,a;if(e)for(o=e.toLowerCase().split(" "),n=o.length;n--;){if(e=o[n],i=c[e],!e){for(r in c)d(r,!1),delete c[r];return l}if(i){if(t)for(a=i.length;a--;)i[a]===t&&(i=i.slice(0,a).concat(i.slice(a+1)),c[e]=i);else i.length=0;i.length||(d(e,!1),delete c[e])}}else{for(e in c)d(e,!1);c={}}return l}function a(e,t,n){return t.once=!0,r(e,t,n)}function s(e){return e=e.toLowerCase(),!(!c[e]||0===c[e].length)}var l=this,u,c={},d;e=e||{},u=e.scope||l,d=e.toggleEvent||t,l.fire=i,l.on=r,l.off=o,l.once=a,l.has=s}var n=e.makeMap("focus blur focusin focusout click dblclick mousedown mouseup mousemove mouseover beforepaste paste cut copy selectionchange mouseout mouseenter mouseleave wheel keydown keypress keyup input contextmenu dragstart dragend dragover draggesture dragdrop drop drag submit compositionstart compositionend compositionupdate touchstart touchend"," ");return t.isNative=function(e){return!!n[e.toLowerCase()]},t}),i(v,[],function(){function e(e){this.create=e.create}return e.create=function(t,n){return new e({create:function(e,i){function r(t){e.set(i,t.value)}function o(e){t.set(n,e.value)}var a;return e.on("change:"+i,o),t.on("change:"+n,r),a=e._bindings,a||(a=e._bindings=[],e.on("destroy",function(){for(var e=a.length;e--;)a[e]()})),a.push(function(){t.off("change:"+n,r)}),t.get(n)}})},e}),i(y,[g],function(e){function t(t){return t._eventDispatcher||(t._eventDispatcher=new e({scope:t,toggleEvent:function(n,i){e.isNative(n)&&t.toggleNativeEvent&&t.toggleNativeEvent(n,i)}})),t._eventDispatcher}return{fire:function(e,n,i){var r=this;if(r.removed&&"remove"!==e)return n;if(n=t(r).fire(e,n,i),i!==!1&&r.parent)for(var o=r.parent();o&&!n.isPropagationStopped();)o.fire(e,n,!1),o=o.parent();return n},on:function(e,n,i){return t(this).on(e,n,i)},off:function(e,n){return t(this).off(e,n)},hasEventListeners:function(e){return t(this).has(e)}}}),i(x,[v,y,d,c],function(e,t,n,i){var r=i.isEqual;return n.extend({Mixins:[t],init:function(t){var n,i;t=t||{};for(n in t)i=t[n],i instanceof e&&(t[n]=i.create(this,n));this.data=t},set:function(t,n){var i,o,a=this.data[t];if(n instanceof e&&(n=n.create(this,t)),"object"==typeof t){for(i in t)this.set(i,t[i]);return this}return r(a,n)||(this.data[t]=n,o={target:this,name:t,value:n,oldValue:a},this.fire("change:"+t,o),this.fire("change",o)),this},get:function(e){return this.data[e]},has:function(e){return e in this.data},bind:function(t){return e.create(this,t)},destroy:function(){this.fire("destroy")}})}),i(b,[d],function(e){function t(e){for(var t=[],n=e.length,i;n--;)i=e[n],i.__checked||(t.push(i),i.__checked=1);for(n=t.length;n--;)delete t[n].__checked;return t}var n=/^([\w\\*]+)?(?:#([\w\\]+))?(?:\.([\w\\\.]+))?(?:\[\@?([\w\\]+)([\^\$\*!~]?=)([\w\\]+)\])?(?:\:(.+))?/i,i=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g,r=/^\s*|\s*$/g,o,a=e.extend({init:function(e){function t(e){return e?(e=e.toLowerCase(),function(t){return"*"===e||t.type===e}):void 0}function o(e){return e?function(t){return t._name===e}:void 0}function a(e){return e?(e=e.split("."),function(t){for(var n=e.length;n--;)if(!t.classes.contains(e[n]))return!1;return!0}):void 0}function s(e,t,n){return e?function(i){var r=i[e]?i[e]():"";return t?"="===t?r===n:"*="===t?r.indexOf(n)>=0:"~="===t?(" "+r+" ").indexOf(" "+n+" ")>=0:"!="===t?r!=n:"^="===t?0===r.indexOf(n):"$="===t?r.substr(r.length-n.length)===n:!1:!!n}:void 0}function l(e){var t;return e?(e=/(?:not\((.+)\))|(.+)/i.exec(e),e[1]?(t=c(e[1],[]),function(e){return!d(e,t)}):(e=e[2],function(t,n,i){return"first"===e?0===n:"last"===e?n===i-1:"even"===e?n%2===0:"odd"===e?n%2===1:t[e]?t[e]():!1})):void 0}function u(e,i,u){function c(e){e&&i.push(e)}var d;return d=n.exec(e.replace(r,"")),c(t(d[1])),c(o(d[2])),c(a(d[3])),c(s(d[4],d[5],d[6])),c(l(d[7])),i.psuedo=!!d[7],i.direct=u,i}function c(e,t){var n=[],r,o,a;do if(i.exec(""),o=i.exec(e),o&&(e=o[3],n.push(o[1]),o[2])){r=o[3];break}while(o);for(r&&c(r,t),e=[],a=0;a<n.length;a++)">"!=n[a]&&e.push(u(n[a],[],">"===n[a-1]));return t.push(e),t}var d=this.match;this._selectors=c(e,[])},match:function(e,t){var n,i,r,o,a,s,l,u,c,d,f,h,p;for(t=t||this._selectors,n=0,i=t.length;i>n;n++){for(a=t[n],o=a.length,p=e,h=0,r=o-1;r>=0;r--)for(u=a[r];p;){if(u.psuedo)for(f=p.parent().items(),c=d=f.length;c--&&f[c]!==p;);for(s=0,l=u.length;l>s;s++)if(!u[s](p,c,d)){s=l+1;break}if(s===l){h++;break}if(r===o-1)break;p=p.parent()}if(h===o)return!0}return!1},find:function(e){function n(e,t,r){var o,a,s,l,u,c=t[r];for(o=0,a=e.length;a>o;o++){for(u=e[o],s=0,l=c.length;l>s;s++)if(!c[s](u,o,a)){s=l+1;break}if(s===l)r==t.length-1?i.push(u):u.items&&n(u.items(),t,r+1);else if(c.direct)return;u.items&&n(u.items(),t,r)}}var i=[],r,s,l=this._selectors;if(e.items){for(r=0,s=l.length;s>r;r++)n(e.items(),l[r],0);s>1&&(i=t(i))}return o||(o=a.Collection),new o(i)}});return a}),i(w,[c,b,d],function(e,t,n){var i,r,o=Array.prototype.push,a=Array.prototype.slice;return r={length:0,init:function(e){e&&this.add(e)},add:function(t){var n=this;return e.isArray(t)?o.apply(n,t):t instanceof i?n.add(t.toArray()):o.call(n,t),n},set:function(e){var t=this,n=t.length,i;for(t.length=0,t.add(e),i=t.length;n>i;i++)delete t[i];return t},filter:function(e){var n=this,r,o,a=[],s,l;for("string"==typeof e?(e=new t(e),l=function(t){return e.match(t)}):l=e,r=0,o=n.length;o>r;r++)s=n[r],l(s)&&a.push(s);return new i(a)},slice:function(){return new i(a.apply(this,arguments))},eq:function(e){return-1===e?this.slice(e):this.slice(e,+e+1)},each:function(t){return e.each(this,t),this},toArray:function(){return e.toArray(this)},indexOf:function(e){for(var t=this,n=t.length;n--&&t[n]!==e;);return n},reverse:function(){return new i(e.toArray(this).reverse())},hasClass:function(e){return this[0]?this[0].classes.contains(e):!1},prop:function(e,t){var n=this,i,r;return t!==i?(n.each(function(n){n[e]&&n[e](t)}),n):(r=n[0],r&&r[e]?r[e]():void 0)},exec:function(t){var n=this,i=e.toArray(arguments).slice(1);return n.each(function(e){e[t]&&e[t].apply(e,i)}),n},remove:function(){for(var e=this.length;e--;)this[e].remove();return this},addClass:function(e){return this.each(function(t){t.classes.add(e)})},removeClass:function(e){return this.each(function(t){t.classes.remove(e)})}},e.each("fire on off show hide append prepend before after reflow".split(" "),function(t){r[t]=function(){var n=e.toArray(arguments);return this.each(function(e){t in e&&e[t].apply(e,n)}),this}}),e.each("text name disabled active selected checked visible parent value data".split(" "),function(e){r[e]=function(t){return this.prop(e,t)}}),i=n.extend(r),t.Collection=i,i}),i(_,[],function(){function e(e){return e.replace(/-(\D)/g,function(e,t){return t.toUpperCase()})}function t(e){return e.replace(/[A-Z]/g,function(e){return"-"+e})}function n(n,i){if(n.ownerDocument.defaultView)try{return n.ownerDocument.defaultView.getComputedStyle(n,null).getPropertyValue(t(i))}catch(r){return null}else if(n.currentStyle)return n.currentStyle[e(i)]}var i={getWindowSize:function(){var e,t,n=window,i=document,r=i.body,o=i.documentElement;return r.offsetWidth&&(e=r.offsetWidth,t=r.offsetHeight),"CSS1Compat"==i.compatMode&&o.offsetWidth&&(e=o.offsetWidth,t=o.offsetHeight),n.innerWidth&&n.innerHeight&&(e=n.innerWidth,t=n.innerHeight),{w:e,h:t}},getViewPort:function(e){var t,n;return e=e?e:window,t=e.document,n=t.documentMode>=8||"CSS1Compat"==t.compatMode?t.documentElement:t.body,{x:e.pageXOffset||n.scrollLeft,y:e.pageYOffset||n.scrollTop,w:e.innerWidth||n.clientWidth,h:e.innerHeight||n.clientHeight}},getDocumentSize:function(){var e=document,t,n,i,r,o,a,s,l,u=Math.max;return t=e.documentElement,n=e.body,i=u(t.scrollWidth,n.scrollWidth),r=u(t.clientWidth,n.clientWidth),o=u(t.offsetWidth,n.offsetWidth),a=u(t.scrollHeight,n.scrollHeight),s=u(t.clientHeight,n.clientHeight),l=u(t.offsetHeight,n.offsetHeight),{width:o>i?r:i,height:l>a?s:a}},getSize:function(e){var t,n,i;return e.getBoundingClientRect?(i=e.getBoundingClientRect(),t=i.right-i.left,n=i.bottom-i.top):(t=e.offsetWidth,n=e.offsetHeight),{width:t,height:n}},getPos:function(e,t){var i,r,o,a,s,l;if(i=r=0,e)if(o=document,a=o.body,t=t||a,t===a&&e.getBoundingClientRect&&"static"===n(a,"position"))l=e.getBoundingClientRect(),e=o.documentElement,i=l.left+(e.scrollLeft||a.scrollLeft)-e.clientTop,r=l.top+(e.scrollTop||a.scrollTop)-e.clientLeft;else{for(t=o.documentElement,s=e;s&&s!=t&&s.nodeType;)i+=s.offsetLeft||0,r+=s.offsetTop||0,s=s.offsetParent;for(s=e.parentNode;s&&s!=t&&s.nodeType;)i-=s.scrollLeft||0,r-=s.scrollTop||0,s=s.parentNode}return{x:i,y:r}},getRuntimeStyle:n};return i}),i(E,[],function(){function e(e,t,n,i){e.addEventListener?e.addEventListener(t,n,i||!1):e.attachEvent&&e.attachEvent("on"+t,n)}function t(e,t,n,i){e.removeEventListener?e.removeEventListener(t,n,i||!1):e.detachEvent&&e.detachEvent("on"+t,n)}function n(e,t){function n(){return!1}function i(){return!0}var r,o=t||{},l;for(r in e)s[r]||(o[r]=e[r]);if(o.target||(o.target=o.srcElement||document),e&&a.test(e.type)&&e.pageX===l&&e.clientX!==l){var u=o.target.ownerDocument||document,c=u.documentElement,d=u.body;o.pageX=e.clientX+(c&&c.scrollLeft||d&&d.scrollLeft||0)-(c&&c.clientLeft||d&&d.clientLeft||0),o.pageY=e.clientY+(c&&c.scrollTop||d&&d.scrollTop||0)-(c&&c.clientTop||d&&d.clientTop||0)}return o.preventDefault=function(){o.isDefaultPrevented=i,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},o.stopPropagation=function(){o.isPropagationStopped=i,e&&(e.stopPropagation?e.stopPropagation():e.cancelBubble=!0)},o.stopImmediatePropagation=function(){o.isImmediatePropagationStopped=i,o.stopPropagation()},o.isDefaultPrevented||(o.isDefaultPrevented=n,o.isPropagationStopped=n,o.isImmediatePropagationStopped=n),o}function i(n,i,r){function o(){r.domLoaded||(r.domLoaded=!0,i(u))}function a(){("complete"===l.readyState||"interactive"===l.readyState&&l.body)&&(t(l,"readystatechange",a),o())}function s(){try{l.documentElement.doScroll("left")}catch(e){return void setTimeout(s,0)}o()}var l=n.document,u={type:"ready"};return r.domLoaded?void i(u):(l.addEventListener?"complete"===l.readyState?o():e(n,"DOMContentLoaded",o):(e(l,"readystatechange",a),l.documentElement.doScroll&&n.self===n.top&&s()),void e(n,"load",o))}function r(){function r(e,t){var n,i,r,o,a=s[t];if(n=a&&a[e.type])for(i=0,r=n.length;r>i;i++)if(o=n[i],o&&o.func.call(o.scope,e)===!1&&e.preventDefault(),e.isImmediatePropagationStopped())return}var a=this,s={},l,u,c,d,f;u=o+(+new Date).toString(32),d="onmouseenter"in document.documentElement,c="onfocusin"in document.documentElement,f={mouseenter:"mouseover",mouseleave:"mouseout"},l=1,a.domLoaded=!1,a.events=s,a.bind=function(t,o,h,p){function m(e){r(n(e||E.event),g)}var g,v,y,x,b,w,_,E=window;if(t&&3!==t.nodeType&&8!==t.nodeType){for(t[u]?g=t[u]:(g=l++,t[u]=g,s[g]={}),p=p||t,o=o.split(" "),y=o.length;y--;)x=o[y],w=m,b=_=!1,"DOMContentLoaded"===x&&(x="ready"),a.domLoaded&&"ready"===x&&"complete"==t.readyState?h.call(p,n({type:x})):(d||(b=f[x],b&&(w=function(e){var t,i;if(t=e.currentTarget,i=e.relatedTarget,i&&t.contains)i=t.contains(i);else for(;i&&i!==t;)i=i.parentNode;i||(e=n(e||E.event),e.type="mouseout"===e.type?"mouseleave":"mouseenter",e.target=t,r(e,g))})),c||"focusin"!==x&&"focusout"!==x||(_=!0,b="focusin"===x?"focus":"blur",w=function(e){e=n(e||E.event),e.type="focus"===e.type?"focusin":"focusout",r(e,g)}),v=s[g][x],v?"ready"===x&&a.domLoaded?h({type:x}):v.push({func:h,scope:p}):(s[g][x]=v=[{func:h,scope:p}],v.fakeName=b,v.capture=_,v.nativeHandler=w,"ready"===x?i(t,w,a):e(t,b||x,w,_)));return t=v=0,h}},a.unbind=function(e,n,i){var r,o,l,c,d,f;if(!e||3===e.nodeType||8===e.nodeType)return a;if(r=e[u]){if(f=s[r],n){for(n=n.split(" "),l=n.length;l--;)if(d=n[l],o=f[d]){if(i)for(c=o.length;c--;)if(o[c].func===i){var h=o.nativeHandler,p=o.fakeName,m=o.capture;o=o.slice(0,c).concat(o.slice(c+1)),o.nativeHandler=h,o.fakeName=p,o.capture=m,f[d]=o}i&&0!==o.length||(delete f[d],t(e,o.fakeName||d,o.nativeHandler,o.capture))}}else{for(d in f)o=f[d],t(e,o.fakeName||d,o.nativeHandler,o.capture);f={}}for(d in f)return a;delete s[r];try{delete e[u]}catch(g){e[u]=null}}return a},a.fire=function(e,t,i){var o;if(!e||3===e.nodeType||8===e.nodeType)return a;i=n(null,i),i.type=t,i.target=e;do o=e[u],o&&r(i,o),e=e.parentNode||e.ownerDocument||e.defaultView||e.parentWindow;while(e&&!i.isPropagationStopped());return a},a.clean=function(e){var t,n,i=a.unbind;if(!e||3===e.nodeType||8===e.nodeType)return a;if(e[u]&&i(e),e.getElementsByTagName||(e=e.document),e&&e.getElementsByTagName)for(i(e),n=e.getElementsByTagName("*"),t=n.length;t--;)e=n[t],e[u]&&i(e);return a},a.destroy=function(){s={}},a.cancel=function(e){return e&&(e.preventDefault(),e.stopImmediatePropagation()),!1}}var o="moxman-data-",a=/^(?:mouse|contextmenu)|click/,s={keyLocation:1,layerX:1,layerY:1,returnValue:1};return r.Event=new r,r.Event.bind(window,"ready",function(){}),r}),i(R,[],function(){function e(e,t,n,i){var r,o,a,s,l,u,d,h,p,m;if((t?t.ownerDocument||t:B)!==I&&N(t),t=t||I,n=n||[],!e||"string"!=typeof e)return n;if(1!==(s=t.nodeType)&&9!==s)return[];if(F&&!i){if(r=ve.exec(e))if(a=r[1]){if(9===s){if(o=t.getElementById(a),!o||!o.parentNode)return n;if(o.id===a)return n.push(o),n}else if(t.ownerDocument&&(o=t.ownerDocument.getElementById(a))&&L(t,o)&&o.id===a)return n.push(o),n}else{if(r[2])return Q.apply(n,t.getElementsByTagName(e)),n;if((a=r[3])&&w.getElementsByClassName)return Q.apply(n,t.getElementsByClassName(a)),n}if(w.qsa&&(!P||!P.test(e))){if(h=d=z,p=t,m=9===s&&e,1===s&&"object"!==t.nodeName.toLowerCase()){for(u=C(e),(d=t.getAttribute("id"))?h=d.replace(xe,"\\$&"):t.setAttribute("id",h),h="[id='"+h+"'] ",l=u.length;l--;)u[l]=h+f(u[l]);p=ye.test(e)&&c(t.parentNode)||t,m=u.join(",")}if(m)try{return Q.apply(n,p.querySelectorAll(m)),n}catch(g){}finally{d||t.removeAttribute("id")}}}return S(e.replace(se,"$1"),t,n,i)}function n(){function e(n,i){return t.push(n+" ")>_.cacheLength&&delete e[t.shift()],e[n+" "]=i}var t=[];return e}function i(e){return e[z]=!0,e}function r(e){var t=I.createElement("div");try{return!!e(t)}catch(n){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function o(e,t){for(var n=e.split("|"),i=e.length;i--;)_.attrHandle[n[i]]=t}function a(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||$)-(~e.sourceIndex||$);if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function s(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function l(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function u(e){return i(function(t){return t=+t,i(function(n,i){for(var r,o=e([],n.length,t),a=o.length;a--;)n[r=o[a]]&&(n[r]=!(i[r]=n[r]))})})}function c(e){return e&&typeof e.getElementsByTagName!==Y&&e}function d(){}function f(e){for(var t=0,n=e.length,i="";n>t;t++)i+=e[t].value;return i}function h(e,t,n){var i=t.dir,r=n&&"parentNode"===i,o=U++;return t.first?function(t,n,o){for(;t=t[i];)if(1===t.nodeType||r)return e(t,n,o)}:function(t,n,a){var s,l,u=[W,o];if(a){for(;t=t[i];)if((1===t.nodeType||r)&&e(t,n,a))return!0}else for(;t=t[i];)if(1===t.nodeType||r){if(l=t[z]||(t[z]={}),(s=l[i])&&s[0]===W&&s[1]===o)return u[2]=s[2];if(l[i]=u,u[2]=e(t,n,a))return!0}}}function p(e){return e.length>1?function(t,n,i){for(var r=e.length;r--;)if(!e[r](t,n,i))return!1;return!0}:e[0]}function m(t,n,i){for(var r=0,o=n.length;o>r;r++)e(t,n[r],i);return i}function g(e,t,n,i,r){for(var o,a=[],s=0,l=e.length,u=null!=t;l>s;s++)(o=e[s])&&(!n||n(o,i,r))&&(a.push(o),u&&t.push(s));return a}function v(e,t,n,r,o,a){return r&&!r[z]&&(r=v(r)),o&&!o[z]&&(o=v(o,a)),i(function(i,a,s,l){var u,c,d,f=[],h=[],p=a.length,v=i||m(t||"*",s.nodeType?[s]:s,[]),y=!e||!i&&t?v:g(v,f,e,s,l),x=n?o||(i?e:p||r)?[]:a:y;if(n&&n(y,x,s,l),r)for(u=g(x,h),r(u,[],s,l),c=u.length;c--;)(d=u[c])&&(x[h[c]]=!(y[h[c]]=d));if(i){if(o||e){if(o){for(u=[],c=x.length;c--;)(d=x[c])&&u.push(y[c]=d);o(null,x=[],u,l)}for(c=x.length;c--;)(d=x[c])&&(u=o?te.call(i,d):f[c])>-1&&(i[u]=!(a[u]=d))}}else x=g(x===a?x.splice(p,x.length):x),o?o(null,a,x,l):Q.apply(a,x)})}function y(e){for(var t,n,i,r=e.length,o=_.relative[e[0].type],a=o||_.relative[" "],s=o?1:0,l=h(function(e){return e===t},a,!0),u=h(function(e){return te.call(t,e)>-1},a,!0),c=[function(e,n,i){return!o&&(i||n!==A)||((t=n).nodeType?l(e,n,i):u(e,n,i))}];r>s;s++)if(n=_.relative[e[s].type])c=[h(p(c),n)];else{if(n=_.filter[e[s].type].apply(null,e[s].matches),n[z]){for(i=++s;r>i&&!_.relative[e[i].type];i++);return v(s>1&&p(c),s>1&&f(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(se,"$1"),n,i>s&&y(e.slice(s,i)),r>i&&y(e=e.slice(i)),r>i&&f(e))}c.push(n)}return p(c)}function x(t,n){var r=n.length>0,o=t.length>0,a=function(i,a,s,l,u){var c,d,f,h=0,p="0",m=i&&[],v=[],y=A,x=i||o&&_.find.TAG("*",u),b=W+=null==y?1:Math.random()||.1,w=x.length;for(u&&(A=a!==I&&a);p!==w&&null!=(c=x[p]);p++){if(o&&c){for(d=0;f=t[d++];)if(f(c,a,s)){l.push(c);break}u&&(W=b)}r&&((c=!f&&c)&&h--,i&&m.push(c))}if(h+=p,r&&p!==h){for(d=0;f=n[d++];)f(m,v,a,s);if(i){if(h>0)for(;p--;)m[p]||v[p]||(v[p]=K.call(l));v=g(v)}Q.apply(l,v),u&&!i&&v.length>0&&h+n.length>1&&e.uniqueSort(l)}return u&&(W=b,A=y),m};return r?i(a):a}var b,w,_,E,R,C,T,S,A,k,D,N,I,M,F,P,O,H,L,z="sizzle"+-new Date,B=window.document,W=0,U=0,j=n(),V=n(),q=n(),X=function(e,t){return e===t&&(D=!0),0},Y=typeof t,$=1<<31,G={}.hasOwnProperty,J=[],K=J.pop,Z=J.push,Q=J.push,ee=J.slice,te=J.indexOf||function(e){for(var t=0,n=this.length;n>t;t++)if(this[t]===e)return t;return-1},ne="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",ie="[\\x20\\t\\r\\n\\f]",re="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",oe="\\["+ie+"*("+re+")(?:"+ie+"*([*^$|!~]?=)"+ie+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+re+"))|)"+ie+"*\\]",ae=":("+re+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+oe+")*)|.*)\\)|)",se=new RegExp("^"+ie+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ie+"+$","g"),le=new RegExp("^"+ie+"*,"+ie+"*"),ue=new RegExp("^"+ie+"*([>+~]|"+ie+")"+ie+"*"),ce=new RegExp("="+ie+"*([^\\]'\"]*?)"+ie+"*\\]","g"),de=new RegExp(ae),fe=new RegExp("^"+re+"$"),he={ID:new RegExp("^#("+re+")"),CLASS:new RegExp("^\\.("+re+")"),TAG:new RegExp("^("+re+"|[*])"),ATTR:new RegExp("^"+oe),PSEUDO:new RegExp("^"+ae),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ie+"*(even|odd|(([+-]|)(\\d*)n|)"+ie+"*(?:([+-]|)"+ie+"*(\\d+)|))"+ie+"*\\)|)","i"),bool:new RegExp("^(?:"+ne+")$","i"),needsContext:new RegExp("^"+ie+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ie+"*((?:-\\d)?\\d*)"+ie+"*\\)|)(?=[^-]|$)","i")},pe=/^(?:input|select|textarea|button)$/i,me=/^h\d$/i,ge=/^[^{]+\{\s*\[native \w/,ve=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ye=/[+~]/,xe=/'|\\/g,be=new RegExp("\\\\([\\da-f]{1,6}"+ie+"?|("+ie+")|.)","ig"),we=function(e,t,n){var i="0x"+t-65536;return i!==i||n?t:0>i?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320)};try{Q.apply(J=ee.call(B.childNodes),B.childNodes),J[B.childNodes.length].nodeType}catch(_e){Q={apply:J.length?function(e,t){Z.apply(e,ee.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}w=e.support={},R=e.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return t?"HTML"!==t.nodeName:!1},N=e.setDocument=function(e){var t,n=e?e.ownerDocument||e:B,i=n.defaultView;return n!==I&&9===n.nodeType&&n.documentElement?(I=n,M=n.documentElement,F=!R(n),i&&i!==i.top&&(i.addEventListener?i.addEventListener("unload",function(){N()},!1):i.attachEvent&&i.attachEvent("onunload",function(){N()})),w.attributes=r(function(e){return e.className="i",
!e.getAttribute("className")}),w.getElementsByTagName=r(function(e){return e.appendChild(n.createComment("")),!e.getElementsByTagName("*").length}),w.getElementsByClassName=ge.test(n.getElementsByClassName),w.getById=r(function(e){return M.appendChild(e).id=z,!n.getElementsByName||!n.getElementsByName(z).length}),w.getById?(_.find.ID=function(e,t){if(typeof t.getElementById!==Y&&F){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},_.filter.ID=function(e){var t=e.replace(be,we);return function(e){return e.getAttribute("id")===t}}):(delete _.find.ID,_.filter.ID=function(e){var t=e.replace(be,we);return function(e){var n=typeof e.getAttributeNode!==Y&&e.getAttributeNode("id");return n&&n.value===t}}),_.find.TAG=w.getElementsByTagName?function(e,t){return typeof t.getElementsByTagName!==Y?t.getElementsByTagName(e):void 0}:function(e,t){var n,i=[],r=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[r++];)1===n.nodeType&&i.push(n);return i}return o},_.find.CLASS=w.getElementsByClassName&&function(e,t){return F?t.getElementsByClassName(e):void 0},O=[],P=[],(w.qsa=ge.test(n.querySelectorAll))&&(r(function(e){e.innerHTML="<select msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&P.push("[*^$]="+ie+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||P.push("\\["+ie+"*(?:value|"+ne+")"),e.querySelectorAll(":checked").length||P.push(":checked")}),r(function(e){var t=n.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&P.push("name"+ie+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||P.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),P.push(",.*:")})),(w.matchesSelector=ge.test(H=M.matches||M.webkitMatchesSelector||M.mozMatchesSelector||M.oMatchesSelector||M.msMatchesSelector))&&r(function(e){w.disconnectedMatch=H.call(e,"div"),H.call(e,"[s!='']:x"),O.push("!=",ae)}),P=P.length&&new RegExp(P.join("|")),O=O.length&&new RegExp(O.join("|")),t=ge.test(M.compareDocumentPosition),L=t||ge.test(M.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,i=t&&t.parentNode;return e===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},X=t?function(e,t){if(e===t)return D=!0,0;var i=!e.compareDocumentPosition-!t.compareDocumentPosition;return i?i:(i=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&i||!w.sortDetached&&t.compareDocumentPosition(e)===i?e===n||e.ownerDocument===B&&L(B,e)?-1:t===n||t.ownerDocument===B&&L(B,t)?1:k?te.call(k,e)-te.call(k,t):0:4&i?-1:1)}:function(e,t){if(e===t)return D=!0,0;var i,r=0,o=e.parentNode,s=t.parentNode,l=[e],u=[t];if(!o||!s)return e===n?-1:t===n?1:o?-1:s?1:k?te.call(k,e)-te.call(k,t):0;if(o===s)return a(e,t);for(i=e;i=i.parentNode;)l.unshift(i);for(i=t;i=i.parentNode;)u.unshift(i);for(;l[r]===u[r];)r++;return r?a(l[r],u[r]):l[r]===B?-1:u[r]===B?1:0},n):I},e.matches=function(t,n){return e(t,null,null,n)},e.matchesSelector=function(t,n){if((t.ownerDocument||t)!==I&&N(t),n=n.replace(ce,"='$1']"),!(!w.matchesSelector||!F||O&&O.test(n)||P&&P.test(n)))try{var i=H.call(t,n);if(i||w.disconnectedMatch||t.document&&11!==t.document.nodeType)return i}catch(r){}return e(n,I,null,[t]).length>0},e.contains=function(e,t){return(e.ownerDocument||e)!==I&&N(e),L(e,t)},e.attr=function(e,n){(e.ownerDocument||e)!==I&&N(e);var i=_.attrHandle[n.toLowerCase()],r=i&&G.call(_.attrHandle,n.toLowerCase())?i(e,n,!F):t;return r!==t?r:w.attributes||!F?e.getAttribute(n):(r=e.getAttributeNode(n))&&r.specified?r.value:null},e.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},e.uniqueSort=function(e){var t,n=[],i=0,r=0;if(D=!w.detectDuplicates,k=!w.sortStable&&e.slice(0),e.sort(X),D){for(;t=e[r++];)t===e[r]&&(i=n.push(r));for(;i--;)e.splice(n[i],1)}return k=null,e},E=e.getText=function(e){var t,n="",i=0,r=e.nodeType;if(r){if(1===r||9===r||11===r){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=E(e)}else if(3===r||4===r)return e.nodeValue}else for(;t=e[i++];)n+=E(t);return n},_=e.selectors={cacheLength:50,createPseudo:i,match:he,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(be,we),e[3]=(e[3]||e[4]||e[5]||"").replace(be,we),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||e.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&e.error(t[0]),t},PSEUDO:function(e){var t,n=!e[6]&&e[2];return he.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&de.test(n)&&(t=C(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(be,we).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=j[e+" "];return t||(t=new RegExp("(^|"+ie+")"+e+"("+ie+"|$)"))&&j(e,function(e){return t.test("string"==typeof e.className&&e.className||typeof e.getAttribute!==Y&&e.getAttribute("class")||"")})},ATTR:function(t,n,i){return function(r){var o=e.attr(r,t);return null==o?"!="===n:n?(o+="","="===n?o===i:"!="===n?o!==i:"^="===n?i&&0===o.indexOf(i):"*="===n?i&&o.indexOf(i)>-1:"$="===n?i&&o.slice(-i.length)===i:"~="===n?(" "+o+" ").indexOf(i)>-1:"|="===n?o===i||o.slice(0,i.length+1)===i+"-":!1):!0}},CHILD:function(e,t,n,i,r){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===i&&0===r?function(e){return!!e.parentNode}:function(t,n,l){var u,c,d,f,h,p,m=o!==a?"nextSibling":"previousSibling",g=t.parentNode,v=s&&t.nodeName.toLowerCase(),y=!l&&!s;if(g){if(o){for(;m;){for(d=t;d=d[m];)if(s?d.nodeName.toLowerCase()===v:1===d.nodeType)return!1;p=m="only"===e&&!p&&"nextSibling"}return!0}if(p=[a?g.firstChild:g.lastChild],a&&y){for(c=g[z]||(g[z]={}),u=c[e]||[],h=u[0]===W&&u[1],f=u[0]===W&&u[2],d=h&&g.childNodes[h];d=++h&&d&&d[m]||(f=h=0)||p.pop();)if(1===d.nodeType&&++f&&d===t){c[e]=[W,h,f];break}}else if(y&&(u=(t[z]||(t[z]={}))[e])&&u[0]===W)f=u[1];else for(;(d=++h&&d&&d[m]||(f=h=0)||p.pop())&&((s?d.nodeName.toLowerCase()!==v:1!==d.nodeType)||!++f||(y&&((d[z]||(d[z]={}))[e]=[W,f]),d!==t)););return f-=r,f===i||f%i===0&&f/i>=0}}},PSEUDO:function(t,n){var r,o=_.pseudos[t]||_.setFilters[t.toLowerCase()]||e.error("unsupported pseudo: "+t);return o[z]?o(n):o.length>1?(r=[t,t,"",n],_.setFilters.hasOwnProperty(t.toLowerCase())?i(function(e,t){for(var i,r=o(e,n),a=r.length;a--;)i=te.call(e,r[a]),e[i]=!(t[i]=r[a])}):function(e){return o(e,0,r)}):o}},pseudos:{not:i(function(e){var t=[],n=[],r=T(e.replace(se,"$1"));return r[z]?i(function(e,t,n,i){for(var o,a=r(e,null,i,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))}):function(e,i,o){return t[0]=e,r(t,null,o,n),!n.pop()}}),has:i(function(t){return function(n){return e(t,n).length>0}}),contains:i(function(e){return e=e.replace(be,we),function(t){return(t.textContent||t.innerText||E(t)).indexOf(e)>-1}}),lang:i(function(t){return fe.test(t||"")||e.error("unsupported lang: "+t),t=t.replace(be,we).toLowerCase(),function(e){var n;do if(n=F?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return n=n.toLowerCase(),n===t||0===n.indexOf(t+"-");while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=window.location&&window.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===M},focus:function(e){return e===I.activeElement&&(!I.hasFocus||I.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return e.disabled===!1},disabled:function(e){return e.disabled===!0},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!_.pseudos.empty(e)},header:function(e){return me.test(e.nodeName)},input:function(e){return pe.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:u(function(){return[0]}),last:u(function(e,t){return[t-1]}),eq:u(function(e,t,n){return[0>n?n+t:n]}),even:u(function(e,t){for(var n=0;t>n;n+=2)e.push(n);return e}),odd:u(function(e,t){for(var n=1;t>n;n+=2)e.push(n);return e}),lt:u(function(e,t,n){for(var i=0>n?n+t:n;--i>=0;)e.push(i);return e}),gt:u(function(e,t,n){for(var i=0>n?n+t:n;++i<t;)e.push(i);return e})}},_.pseudos.nth=_.pseudos.eq;for(b in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})_.pseudos[b]=s(b);for(b in{submit:!0,reset:!0})_.pseudos[b]=l(b);return d.prototype=_.filters=_.pseudos,_.setFilters=new d,C=e.tokenize=function(t,n){var i,r,o,a,s,l,u,c=V[t+" "];if(c)return n?0:c.slice(0);for(s=t,l=[],u=_.preFilter;s;){(!i||(r=le.exec(s)))&&(r&&(s=s.slice(r[0].length)||s),l.push(o=[])),i=!1,(r=ue.exec(s))&&(i=r.shift(),o.push({value:i,type:r[0].replace(se," ")}),s=s.slice(i.length));for(a in _.filter)!(r=he[a].exec(s))||u[a]&&!(r=u[a](r))||(i=r.shift(),o.push({value:i,type:a,matches:r}),s=s.slice(i.length));if(!i)break}return n?s.length:s?e.error(t):V(t,l).slice(0)},T=e.compile=function(e,t){var n,i=[],r=[],o=q[e+" "];if(!o){for(t||(t=C(e)),n=t.length;n--;)o=y(t[n]),o[z]?i.push(o):r.push(o);o=q(e,x(r,i)),o.selector=e}return o},S=e.select=function(e,t,n,i){var r,o,a,s,l,u="function"==typeof e&&e,d=!i&&C(e=u.selector||e);if(n=n||[],1===d.length){if(o=d[0]=d[0].slice(0),o.length>2&&"ID"===(a=o[0]).type&&w.getById&&9===t.nodeType&&F&&_.relative[o[1].type]){if(t=(_.find.ID(a.matches[0].replace(be,we),t)||[])[0],!t)return n;u&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(r=he.needsContext.test(e)?0:o.length;r--&&(a=o[r],!_.relative[s=a.type]);)if((l=_.find[s])&&(i=l(a.matches[0].replace(be,we),ye.test(o[0].type)&&c(t.parentNode)||t))){if(o.splice(r,1),e=i.length&&f(o),!e)return Q.apply(n,i),n;break}}return(u||T(e,d))(i,t,!F,n,ye.test(e)&&c(t.parentNode)||t),n},w.sortStable=z.split("").sort(X).join("")===z,w.detectDuplicates=!!D,N(),w.sortDetached=r(function(e){return 1&e.compareDocumentPosition(I.createElement("div"))}),r(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||o("type|href|height|width",function(e,t,n){return n?void 0:e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),w.attributes&&r(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||o("value",function(e,t,n){return n||"input"!==e.nodeName.toLowerCase()?void 0:e.defaultValue}),r(function(e){return null==e.getAttribute("disabled")})||o(ne,function(e,t,n){var i;return n?void 0:e[t]===!0?t.toLowerCase():(i=e.getAttributeNode(t))&&i.specified?i.value:null}),e}),i(C,[],function(){function e(e){return"matchMedia"in window?matchMedia(e).matches:!1}var t=navigator,n=t.userAgent,i,r,o,a,s,l,u;return i=/WebKit/.test(n),r=!i&&/MSIE/gi.test(n)&&/Explorer/gi.test(t.appName),r=r&&/MSIE (\w+)\./.exec(n)[1],o=-1!=n.indexOf("Mac"),a=/Android/.test(n),s=/(iPad|iPhone)/.test(n),l=e("only screen and (max-device-width: 480px)")&&(a||s),u=e("only screen and (min-width: 800px)")&&(a||s),{apiPageName:"api.php",ie:r,ie7:!("createRange"in document)&&"all"in document&&!window.opera&&!document.documentMode,mac:o,phone:l,tablet:u,desktop:!l&&!u}}),i(T,[E,R,c,C],function(e,n,i,r){function o(e){return"undefined"!=typeof e}function a(e){return"string"==typeof e}function s(e){return e&&e==e.window}function l(e,t){var n,i,r;for(t=t||_,r=t.createElement("div"),n=t.createDocumentFragment(),r.innerHTML=e;i=r.firstChild;)n.appendChild(i);return n}function u(e,t,n,i){var r;if("undefined"==typeof t)return e;if(a(t))t=l(t,v(e[0]));else if(t.length&&!t.nodeType){if(t=f.makeArray(t),i)for(r=t.length-1;r>=0;r--)u(e,t[r],n,i);else for(r=0;r<t.length;r++)u(e,t[r],n,i);return e}if(t.nodeType)for(r=e.length;r--;)n.call(e[r],t);return e}function c(e,t){return e&&t&&-1!==(" "+e.className+" ").indexOf(" "+t+" ")}function d(e,t,n){var i,r;return t=f(t)[0],e.each(function(){var e=this;n&&i==e.parentNode?r.appendChild(e):(i=e.parentNode,r=t.cloneNode(!1),e.parentNode.insertBefore(r,e),r.appendChild(e))}),e}function f(e,t){return new f.fn.init(e,t)}function h(e,t){var n;if(t.indexOf)return t.indexOf(e);for(n=t.length;n--;)if(t[n]===e)return n;return-1}function p(e){return null===e||e===S?"":(""+e).replace(F,"")}function m(e,t){var n,i,r,o,a;if(e)if(n=e.length,n===o){for(i in e)if(e.hasOwnProperty(i)&&(a=e[i],t.call(a,i,a)===!1))break}else for(r=0;n>r&&(a=e[r],t.call(a,r,a)!==!1);r++);return e}function g(e,t){var n=[];return m(e,function(e,i){t(i,e)&&n.push(i)}),n}function v(e){return e?9==e.nodeType?e:e.ownerDocument:_}function y(e,n,i){var r=[],o=e[n];for("string"!=typeof i&&i instanceof f&&(i=i[0]);o&&9!==o.nodeType;){if(i!==t){if(o===i)break;if("string"==typeof i&&f(o).is(i))break}1===o.nodeType&&r.push(o),o=o[n]}return r}function x(e,n,i,r){var o=[];for(r instanceof f&&(r=r[0]);e;e=e[n])if(!i||e.nodeType===i){if(r!==t){if(e===r)break;if("string"==typeof r&&f(e).is(r))break}o.push(e)}return o}function b(e,t,n){for(e=e[t];e;e=e[t])if(e.nodeType==n)return e;return null}function w(e,t,n){m(n,function(n,i){e[n]=e[n]||{},e[n][t]=i})}var _=document,E=Array.prototype.push,R=Array.prototype.slice,C=/^(?:[^#<]*(<[\w\W]+>)[^>]*$|#([\w\-]*)$)/,T=e.Event,S,A=i.makeMap("fillOpacity fontWeight lineHeight opacity orphans widows zIndex zoom"," "),k=i.makeMap("checked compact declare defer disabled ismap multiple nohref noshade nowrap readonly selected"," "),D={"for":"htmlFor","class":"className",readonly:"readOnly"},N={"float":"cssFloat"},I={},M={},F=/^\s*|\s*$/g;return f.fn=f.prototype={constructor:f,selector:"",context:null,length:0,init:function(e,t){var n=this,i,r;if(!e)return n;if(e.nodeType)return n.context=n[0]=e,n.length=1,n;if(t&&t.nodeType)n.context=t;else{if(t)return f(e).attr(t);n.context=t=document}if(a(e)){if(n.selector=e,i="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:C.exec(e),!i)return f(t).find(e);if(i[1])for(r=l(e,v(t)).firstChild;r;)E.call(n,r),r=r.nextSibling;else{if(r=v(t).getElementById(i[2]),!r)return n;if(r.id!==i[2])return n.find(e);n.length=1,n[0]=r}}else this.add(e,!1);return n},toArray:function(){return i.toArray(this)},add:function(e,t){var n=this,i,r;if(a(e))return n.add(f(e));if(t!==!1)for(i=f.unique(n.toArray().concat(f.makeArray(e))),n.length=i.length,r=0;r<i.length;r++)n[r]=i[r];else E.apply(n,f.makeArray(e));return n},attr:function(e,t){var n=this,i;if("object"==typeof e)m(e,function(e,t){n.attr(e,t)});else{if(!o(t)){if(n[0]&&1===n[0].nodeType){if(i=I[e],i&&i.get)return i.get(n[0],e);if(k[e])return n.prop(e)?e:S;t=n[0].getAttribute(e,2),null===t&&(t=S)}return t}this.each(function(){var n;if(1===this.nodeType){if(n=I[e],n&&n.set)return void n.set(this,t);null===t?this.removeAttribute(e,2):this.setAttribute(e,t,2)}})}return n},removeAttr:function(e){return this.attr(e,null)},prop:function(e,t){var n=this;if(e=D[e]||e,"object"==typeof e)m(e,function(e,t){n.prop(e,t)});else{if(!o(t))return n[0]&&n[0].nodeType&&e in n[0]?n[0][e]:t;this.each(function(){1==this.nodeType&&(this[e]=t)})}return n},css:function(e,t){function n(e){return e.replace(/-(\D)/g,function(e,t){return t.toUpperCase()})}function i(e){return e.replace(/[A-Z]/g,function(e){return"-"+e})}var r=this,a,s;if("object"==typeof e)m(e,function(e,t){r.css(e,t)});else if(o(t))e=n(e),"number"!=typeof t||A[e]||(t+="px"),r.each(function(){var n=this.style;if(s=M[e],s&&s.set)return void s.set(this,t);try{this.style[N[e]||e]=t}catch(r){}(null===t||""===t)&&(n.removeProperty?n.removeProperty(i(e)):n.removeAttribute(e))});else{if(a=r[0],s=M[e],s&&s.get)return s.get(a);if(a.ownerDocument.defaultView)try{return a.ownerDocument.defaultView.getComputedStyle(a,null).getPropertyValue(i(e))}catch(l){return S}else if(a.currentStyle)return a.currentStyle[n(e)]}return r},remove:function(){for(var e=this,t,n=this.length;n--;)t=e[n],T.clean(t),t.parentNode&&t.parentNode.removeChild(t);return this},empty:function(){for(var e=this,t,n=this.length;n--;)for(t=e[n];t.firstChild;)t.removeChild(t.firstChild);return this},html:function(e){var t=this,n;if(o(e)){n=t.length;try{for(;n--;)t[n].innerHTML=e}catch(i){f(t[n]).empty().append(e)}return t}return t[0]?t[0].innerHTML:""},text:function(e){var t=this,n;if(o(e)){for(n=t.length;n--;)"innerText"in t[n]?t[n].innerText=e:t[0].textContent=e;return t}return t[0]?t[0].innerText||t[0].textContent:""},append:function(){return u(this,arguments,function(e){1===this.nodeType&&this.appendChild(e)})},prepend:function(){return u(this,arguments,function(e){1===this.nodeType&&this.insertBefore(e,this.firstChild)},!0)},before:function(){var e=this;return e[0]&&e[0].parentNode?u(e,arguments,function(e){this.parentNode.insertBefore(e,this)}):e},after:function(){var e=this;return e[0]&&e[0].parentNode?u(e,arguments,function(e){this.parentNode.insertBefore(e,this.nextSibling)},!0):e},appendTo:function(e){return f(e).append(this),this},prependTo:function(e){return f(e).prepend(this),this},replaceWith:function(e){return this.before(e).remove()},wrap:function(e){return d(this,e)},wrapAll:function(e){return d(this,e,!0)},wrapInner:function(e){return this.each(function(){f(this).contents().wrapAll(e)}),this},unwrap:function(){return this.parent().each(function(){f(this).replaceWith(this.childNodes)})},clone:function(){var e=[];return this.each(function(){e.push(this.cloneNode(!0))}),f(e)},addClass:function(e){return this.toggleClass(e,!0)},removeClass:function(e){return this.toggleClass(e,!1)},toggleClass:function(e,t){var n=this;return"string"!=typeof e?n:(-1!==e.indexOf(" ")?m(e.split(" "),function(){n.toggleClass(this,t)}):n.each(function(n,i){var r,o;o=c(i,e),o!==t&&(r=i.className,o?i.className=p((" "+r+" ").replace(" "+e+" "," ")):i.className+=r?" "+e:e)}),n)},hasClass:function(e){return c(this[0],e)},each:function(e){return m(this,e)},on:function(e,t){return this.each(function(){T.bind(this,e,t)})},off:function(e,t){return this.each(function(){T.unbind(this,e,t)})},trigger:function(e){return this.each(function(){"object"==typeof e?T.fire(this,e.type,e):T.fire(this,e)})},show:function(){return this.css("display","")},hide:function(){return this.css("display","none")},slice:function(){return new f(R.apply(this,arguments))},eq:function(e){return-1===e?this.slice(e):this.slice(e,+e+1)},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},find:function(e){var t,n,i=[];for(t=0,n=this.length;n>t;t++)f.find(e,this[t],i);return f(i)},filter:function(e){return f("function"==typeof e?g(this.toArray(),function(t,n){return e(n,t)}):f.filter(e,this.toArray()))},closest:function(e){var t=[];return e instanceof f&&(e=e[0]),this.each(function(n,i){for(;i;){if("string"==typeof e&&f(i).is(e)){t.push(i);break}if(i==e){t.push(i);break}i=i.parentNode}}),f(t)},offset:function(e){var t,n,i,r=0,o=0,a;return e?this.css(e):(t=this[0],t&&(n=t.ownerDocument,i=n.documentElement,t.getBoundingClientRect&&(a=t.getBoundingClientRect(),r=a.left+(i.scrollLeft||n.body.scrollLeft)-i.clientLeft,o=a.top+(i.scrollTop||n.body.scrollTop)-i.clientTop)),{left:r,top:o})},push:E,sort:[].sort,splice:[].splice},i.extend(f,{extend:i.extend,makeArray:function(e){return s(e)||e.nodeType?[e]:i.toArray(e)},inArray:h,isArray:i.isArray,each:m,trim:p,grep:g,find:n,expr:n.selectors,unique:n.uniqueSort,text:n.getText,contains:n.contains,filter:function(e,t,n){var i=t.length;for(n&&(e=":not("+e+")");i--;)1!=t[i].nodeType&&t.splice(i,1);return t=1===t.length?f.find.matchesSelector(t[0],e)?[t[0]]:[]:f.find.matches(e,t)}}),m({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return y(e,"parentNode")},next:function(e){return b(e,"nextSibling",1)},prev:function(e){return b(e,"previousSibling",1)},children:function(e){return x(e.firstChild,"nextSibling",1)},contents:function(e){return i.toArray(("iframe"===e.nodeName?e.contentDocument||e.contentWindow.document:e).childNodes)}},function(e,t){f.fn[e]=function(n){var i=this,r=[];return i.each(function(){var e=t.call(r,this,n,r);e&&(f.isArray(e)?r.push.apply(r,e):r.push(e))}),this.length>1&&(r=f.unique(r),0===e.indexOf("parents")&&(r=r.reverse())),r=f(r),n?r.filter(n):r}}),m({parentsUntil:function(e,t){return y(e,"parentNode",t)},nextUntil:function(e,t){return x(e,"nextSibling",1,t).slice(1)},prevUntil:function(e,t){return x(e,"previousSibling",1,t).slice(1)}},function(e,t){f.fn[e]=function(n,i){var r=this,o=[];return r.each(function(){var e=t.call(o,this,n,o);e&&(f.isArray(e)?o.push.apply(o,e):o.push(e))}),this.length>1&&(o=f.unique(o),(0===e.indexOf("parents")||"prevUntil"===e)&&(o=o.reverse())),o=f(o),i?o.filter(i):o}}),f.fn.is=function(e){return!!e&&this.filter(e).length>0},f.fn.init.prototype=f.fn,f.overrideDefaults=function(e){function t(i,r){return n=n||e(),0===arguments.length&&(i=n.element),r||(r=n.context),new t.fn.init(i,r)}var n;return f.extend(t,this),t},r.ie&&r.ie<8&&(w(I,"get",{maxlength:function(e){var t=e.maxLength;return 2147483647===t?S:t},size:function(e){var t=e.size;return 20===t?S:t},"class":function(e){return e.className},style:function(e){var t=e.style.cssText;return 0===t.length?S:t}}),w(I,"set",{"class":function(e,t){e.className=t},style:function(e,t){e.style.cssText=t}})),r.ie&&r.ie<9&&(N["float"]="styleFloat",w(M,"set",{opacity:function(e,t){var n=e.style;null===t||""===t?n.removeAttribute("filter"):(n.zoom=1,n.filter="alpha(opacity="+100*t+")")}})),f.attrHooks=I,f.cssHooks=M,f}),i(S,[c],function(e){function t(){}function n(e){this.cls=[],this.cls._map={},this.onchange=e||t,this.prefix=""}return e.extend(n.prototype,{add:function(e){return e&&!this.contains(e)&&(this.cls._map[e]=!0,this.cls.push(e),this._change()),this},remove:function(e){if(this.contains(e)){for(var t=0;t<this.cls.length&&this.cls[t]!==e;t++);this.cls.splice(t,1),delete this.cls._map[e],this._change()}return this},toggle:function(e,t){var n=this.contains(e);return n!==t&&(n?this.remove(e):this.add(e),this._change()),this},contains:function(e){return!!this.cls._map[e]},_change:function(){delete this.clsValue,this.onchange.call(this)}}),n.prototype.toString=function(){var e;if(this.clsValue)return this.clsValue;e="";for(var t=0;t<this.cls.length;t++)t>0&&(e+=" "),e+=this.prefix+this.cls[t];return e},n}),i(A,[],function(){function e(e,t){function n(e){window.setTimeout(e,0)}var i,r=window.requestAnimationFrame,o=["ms","moz","webkit"];for(i=0;i<o.length&&!r;i++)r=window[o[i]+"RequestAnimationFrame"];r||(r=n),r(e,t)}var t={},n;return{add:function(i){var r=i.parent();if(r){if(!r._layout||r._layout.isNative())return;t[r._id]||(t[r._id]=r),n||(n=!0,e(function(){var e,i;n=!1;for(e in t)i=t[e],i.state.get("rendered")&&i.reflow();t={}},document.body))}},remove:function(e){t[e._id]&&delete t[e._id]}}}),i(k,[d,c,g,x,w,_,T,m,S,A],function(e,t,n,i,r,o,a,s,l,u){function c(e){return e._eventDispatcher||(e._eventDispatcher=new n({scope:e,toggleEvent:function(t,i){i&&n.isNative(t)&&(e._nativeEvents||(e._nativeEvents={}),e._nativeEvents[t]=!0,e.state.get("rendered")&&d(e))}})),e._eventDispatcher}function d(e){function t(t){var n=e.getParentCtrl(t.target);n&&n.fire(t.type,t)}function n(){var e=u._lastHoverCtrl;e&&(e.fire("mouseleave",{target:e.getEl()}),e.parents().each(function(e){e.fire("mouseleave",{target:e.getEl()})}),u._lastHoverCtrl=null)}function i(t){var n=e.getParentCtrl(t.target),i=u._lastHoverCtrl,r=0,o,a,s;if(n!==i){if(u._lastHoverCtrl=n,a=n.parents().toArray().reverse(),a.push(n),i){for(s=i.parents().toArray().reverse(),s.push(i),r=0;r<s.length&&a[r]===s[r];r++);for(o=s.length-1;o>=r;o--)i=s[o],i.fire("mouseleave",{target:i.getEl()})}for(o=r;o<a.length;o++)n=a[o],n.fire("mouseenter",{target:n.getEl()})}}function r(t){t.preventDefault(),"mousewheel"==t.type?(t.deltaY=-1/40*t.wheelDelta,t.wheelDeltaX&&(t.deltaX=-1/40*t.wheelDeltaX)):(t.deltaX=0,t.deltaY=t.detail),t=e.fire("wheel",t)}var o,s,l,u,c,d;if(c=e._nativeEvents){for(l=e.parents().toArray(),l.unshift(e),o=0,s=l.length;!u&&s>o;o++)u=l[o]._eventsRoot;for(u||(u=l[l.length-1]||e),e._eventsRoot=u,s=o,o=0;s>o;o++)l[o]._eventsRoot=u;var p=u._delegates;p||(p=u._delegates={});for(d in c){if(!c)return!1;"wheel"!==d||h?("mouseenter"===d||"mouseleave"===d?u._hasMouseEnter||(a(u.getEl()).on("mouseleave",n).on("mouseover",i),u._hasMouseEnter=1):p[d]||(a(u.getEl()).on(d,t),p[d]=!0),c[d]=!1):f?a(e.getEl()).on("mousewheel",r):a(e.getEl()).on("DOMMouseScroll",r)}}}var f="onmousewheel"in document,h=!1,p="moxman-",m,g=0,v={Statics:{classPrefix:p},isRtl:function(){return m.rtl},classPrefix:p,init:function(e){var n=this,r,o;if(n.settings=e=t.extend({},n.Defaults,e),n._id=e.id||"moxman-"+g++,n._aria={role:e.role},n._elmCache={},n.$=a,n.state=new i({visible:!0,active:!1,disabled:!1,value:""}),n.data=new i(e.data),n.classes=new l(function(){n.state.get("rendered")&&(n.getEl().className=this.toString())}),n.classes.prefix=n.classPrefix,r=e.classes)for(r=r.split(" "),o=0;o<r.length;o++)n.classes.add(r[o]);t.each("title text name visible disabled active value".split(" "),function(t){t in e&&n[t](e[t])}),n.on("click",function(){return n.disabled()?!1:void 0}),n.settings=e,n.borderBox=s.parseBox(e.border),n.paddingBox=s.parseBox(e.padding),n.marginBox=s.parseBox(e.margin),e.hidden&&n.hide()},Properties:"parent,name",getContainerElm:function(){return document.body},getParentCtrl:function(e){for(var t,n=this.getRoot().controlIdLookup;e&&n&&!(t=n[e.id]);)e=e.parentNode;return t},initLayoutRect:function(){var e=this,t=e.settings,n,i,r=e.getEl(),a,l,u,c,d,f,h,p;n=e.borderBox=e.borderBox||s.measureBox(r,"border"),e.paddingBox=e.paddingBox||s.measureBox(r,"padding"),e.marginBox=e.marginBox||s.measureBox(r,"margin"),p=o.getSize(r),f=t.minWidth,h=t.minHeight,u=f||p.width,c=h||p.height,a=t.width,l=t.height,d=t.autoResize,d="undefined"!=typeof d?d:!a&&!l,a=a||u,l=l||c;var m=n.left+n.right,g=n.top+n.bottom,v=t.maxWidth||65535,y=t.maxHeight||65535;return e._layoutRect=i={x:t.x||0,y:t.y||0,w:a,h:l,deltaW:m,deltaH:g,contentW:a-m,contentH:l-g,innerW:a-m,innerH:l-g,startMinWidth:f||0,startMinHeight:h||0,minW:Math.min(u,v),minH:Math.min(c,y),maxW:v,maxH:y,autoResize:d,scrollW:0},e._lastLayoutRect={},i},layoutRect:function(e){var t=this,n=t._layoutRect,i,r,o,a,s,l;return n||(n=t.initLayoutRect()),e?(o=n.deltaW,a=n.deltaH,e.x!==s&&(n.x=e.x),e.y!==s&&(n.y=e.y),e.minW!==s&&(n.minW=e.minW),e.minH!==s&&(n.minH=e.minH),r=e.w,r!==s&&(r=r<n.minW?n.minW:r,r=r>n.maxW?n.maxW:r,n.w=r,n.innerW=r-o),r=e.h,r!==s&&(r=r<n.minH?n.minH:r,r=r>n.maxH?n.maxH:r,n.h=r,n.innerH=r-a),r=e.innerW,r!==s&&(r=r<n.minW-o?n.minW-o:r,r=r>n.maxW-o?n.maxW-o:r,n.innerW=r,n.w=r+o),r=e.innerH,r!==s&&(r=r<n.minH-a?n.minH-a:r,r=r>n.maxH-a?n.maxH-a:r,n.innerH=r,n.h=r+a),e.contentW!==s&&(n.contentW=e.contentW),e.contentH!==s&&(n.contentH=e.contentH),i=t._lastLayoutRect,(i.x!==n.x||i.y!==n.y||i.w!==n.w||i.h!==n.h)&&(l=m.repaintControls,l&&l.map&&!l.map[t._id]&&(l.push(t),l.map[t._id]=!0),i.x=n.x,i.y=n.y,i.w=n.w,i.h=n.h),t):n},repaint:function(){var e=this,t,n,i,r,o,a=0,s=0,l,u,c;u=document.createRange?function(e){return e}:Math.round,t=e.getEl().style,r=e._layoutRect,l=e._lastRepaintRect||{},o=e.borderBox,a=o.left+o.right,s=o.top+o.bottom,r.x!==l.x&&(t.left=u(r.x)+"px",l.x=r.x),r.y!==l.y&&(t.top=u(r.y)+"px",l.y=r.y),r.w!==l.w&&(c=u(r.w-a),t.width=(c>=0?c:0)+"px",l.w=r.w),r.h!==l.h&&(c=u(r.h-s),t.height=(c>=0?c:0)+"px",l.h=r.h),e._hasBody&&r.innerW!==l.innerW&&(c=u(r.innerW),i=e.getEl("body"),i&&(n=i.style,n.width=(c>=0?c:0)+"px"),l.innerW=r.innerW),e._hasBody&&r.innerH!==l.innerH&&(c=u(r.innerH),i=i||e.getEl("body"),i&&(n=n||i.style,n.height=(c>=0?c:0)+"px"),l.innerH=r.innerH),e._lastRepaintRect=l,e.fire("repaint",{},!1)},on:function(e,t){function n(e){var t,n;return"string"!=typeof e?e:function(r){return t||i.parentsAndSelf().each(function(i){var r=i.settings.callbacks;return r&&(t=r[e])?(n=i,!1):void 0}),t?t.call(n,r):(r.action=e,void this.fire("execute",r))}}var i=this;return c(i).on(e,n(t)),i},off:function(e,t){return c(this).off(e,t),this},fire:function(e,t,n){var i=this;if(t=t||{},t.control||(t.control=i),t=c(i).fire(e,t),n!==!1&&i.parent)for(var r=i.parent();r&&!t.isPropagationStopped();)r.fire(e,t,!1),r=r.parent();return t},hasEventListeners:function(e){return c(this).has(e)},parents:function(e){var t=this,n,i=new r;for(n=t.parent();n;n=n.parent())i.add(n);return e&&(i=i.filter(e)),i},parentsAndSelf:function(e){return new r(this).add(this.parents(e))},next:function(){var e=this.parent().items();return e[e.indexOf(this)+1]},prev:function(){var e=this.parent().items();return e[e.indexOf(this)-1]},innerHtml:function(e){return this.$el.html(e),this},getEl:function(e){var t=e?this._id+"-"+e:this._id;return this._elmCache[t]||(this._elmCache[t]=a("#"+t)[0]),this._elmCache[t]},show:function(){return this.visible(!0)},hide:function(){return this.visible(!1)},focus:function(){try{this.getEl().focus()}catch(e){}return this},blur:function(){return this.getEl().blur(),this},aria:function(e,t){var n=this,i=n.getEl(n.ariaTarget);return"undefined"==typeof t?n._aria[e]:(n._aria[e]=t,n.state.get("rendered")&&i.setAttribute("role"==e?e:"aria-"+e,t),n)},encode:function(e,t){return t!==!1&&(e=this.translate(e)),(e||"").replace(/[&<>"]/g,function(e){return"&#"+e.charCodeAt(0)+";"})},translate:function(e){return m.translate?m.translate(e):e},before:function(e){var t=this,n=t.parent();return n&&n.insert(e,n.items().indexOf(t),!0),t},after:function(e){var t=this,n=t.parent();return n&&n.insert(e,n.items().indexOf(t)),t},remove:function(){var e=this,t=e.getEl(),n=e.parent(),i,r;if(e.items){var o=e.items().toArray();for(r=o.length;r--;)o[r].remove()}n&&n.items&&(i=[],n.items().each(function(t){t!==e&&i.push(t)}),n.items().set(i),n._lastRect=null),e._eventsRoot&&e._eventsRoot==e&&a(t).off();var s=e.getRoot().controlIdLookup;return s&&delete s[e._id],t&&t.parentNode&&t.parentNode.removeChild(t),e.state.set("rendered",!1),e.state.destroy(),e.fire("remove"),e},renderBefore:function(e){return a(e).before(this.renderHtml()),this.postRender(),this},renderTo:function(e){return a(e||this.getContainerElm()).append(this.renderHtml()),this.postRender(),this},preRender:function(){},render:function(){},renderHtml:function(){return""},postRender:function(){var e=this,t=e.settings,n,i,r,o,s;e.$el=a(e.getEl()),e.state.set("rendered",!0);for(o in t)0===o.indexOf("on")&&e.on(o.substr(2),t[o]);if(e._eventsRoot){for(r=e.parent();!s&&r;r=r.parent())s=r._eventsRoot;if(s)for(o in s._nativeEvents)e._nativeEvents[o]=!0}d(e),t.style&&(n=e.getEl(),n&&(n.setAttribute("style",t.style),n.style.cssText=t.style)),e.settings.border&&(i=e.borderBox,e.$el.css({"border-top-width":i.top,"border-right-width":i.right,"border-bottom-width":i.bottom,"border-left-width":i.left}));var l=e.getRoot();l.controlIdLookup||(l.controlIdLookup={}),l.controlIdLookup[e._id]=e;for(var c in e._aria)e.aria(c,e._aria[c]);e.state.get("visible")===!1&&(e.getEl().style.display="none"),e.bindStates(),e.state.on("change:visible",function(t){var n=t.value,i;e.state.get("rendered")&&(e.getEl().style.display=n===!1?"none":"",e.getEl().getBoundingClientRect()),i=e.parent(),i&&(i._lastRect=null),e.fire(n?"show":"hide"),u.add(e)}),e.fire("postrender",{},!1)},bindStates:function(){},scrollIntoView:function(e){function t(e,t){var n,i,r=e;for(n=i=0;r&&r!=t&&r.nodeType;)n+=r.offsetLeft||0,i+=r.offsetTop||0,r=r.offsetParent;return{x:n,y:i}}var n=this.getEl(),i=n.parentNode,r,o,a,s,l,u,c=t(n,i);return r=c.x,o=c.y,a=n.offsetWidth,s=n.offsetHeight,l=i.clientWidth,u=i.clientHeight,
"end"==e?(r-=l-a,o-=u-s):"center"==e&&(r-=l/2-a/2,o-=u/2-s/2),i.scrollLeft=r,i.scrollTop=o,this},getRoot:function(){for(var e=this,t,n=[];e;){if(e.rootControl){t=e.rootControl;break}n.push(e),t=e,e=e.parent()}t||(t=this);for(var i=n.length;i--;)n[i].rootControl=t;return t},reflow:function(){u.remove(this);var e=this.parent();return e._layout&&!e._layout.isNative()&&e.reflow(),this}};return t.each("text title visible disabled active value".split(" "),function(e){v[e]=function(t){return 0===arguments.length?this.state.get(e):("undefined"!=typeof t&&this.state.set(e,t),this)}}),m=e.extend(v)}),i(D,[_],function(e){function t(t,n,i){var r,o,a,s,l,u,c,d,f,h;return f=e.getViewPort(),o=e.getPos(n),a=o.x,s=o.y,t.state.get("fixed")&&"static"==e.getRuntimeStyle(document.body,"position")&&(a-=f.x,s-=f.y),r=t.getEl(),h=e.getSize(r),l=h.width,u=h.height,h=e.getSize(n),c=h.width,d=h.height,i=(i||"").split(""),"b"===i[0]&&(s+=d),"r"===i[1]&&(a+=c),"c"===i[0]&&(s+=Math.round(d/2)),"c"===i[1]&&(a+=Math.round(c/2)),"b"===i[3]&&(s-=u),"r"===i[4]&&(a-=l),"c"===i[3]&&(s-=Math.round(u/2)),"c"===i[4]&&(a-=Math.round(l/2)),{x:a,y:s,w:l,h:u}}return{testMoveRel:function(n,i){for(var r=e.getViewPort(),o=0;o<i.length;o++){var a=t(this,n,i[o]);if(this.state.get("fixed")){if(a.x>0&&a.x+a.w<r.w&&a.y>0&&a.y+a.h<r.h)return i[o]}else if(a.x>r.x&&a.x+a.w<r.w+r.x&&a.y>r.y&&a.y+a.h<r.h+r.y)return i[o]}return i[0]},moveRel:function(e,n){"string"!=typeof n&&(n=this.testMoveRel(e,n));var i=t(this,e,n);return this.moveTo(i.x,i.y)},moveBy:function(e,t){var n=this,i=n.layoutRect();return n.moveTo(i.x+e,i.y+t),n},moveTo:function(t,n){function i(e,t,n){return 0>e?0:e+n>t?(e=t-n,0>e?0:e):e}var r=this;if(r.settings.constrainToViewport){var o=e.getViewPort(window),a=r.layoutRect();t=i(t,o.w+o.x,a.w),n=i(n,o.h+o.y,a.h)}return r.state.get("rendered")?r.layoutRect({x:t,y:n}).repaint():(r.settings.x=t,r.settings.y=n),r.fire("move",{x:t,y:n}),r}}}),i(N,[k,D],function(e,t){return e.extend({Mixins:[t],Defaults:{classes:"widget tooltip tooltip-n"},renderHtml:function(){var e=this,t=e.classPrefix;return'<div id="'+e._id+'" class="'+e.classes+'" role="presentation"><div class="'+t+'tooltip-arrow"></div><div class="'+t+'tooltip-inner">'+e.encode(e.state.get("text"))+"</div></div>"},bindStates:function(){var e=this;return e.state.on("change:text",function(t){e.getEl().lastChild.innerHTML=e.encode(t.value)}),e._super()},repaint:function(){var e=this,t,n;t=e.getEl().style,n=e._layoutRect,t.left=n.x+"px",t.top=n.y+"px",t.zIndex=131070}})}),i(I,[k,N],function(e,t){var n,i=e.extend({init:function(e){var t=this;t._super(e),e=t.settings,t.canFocus=!0,e.tooltip&&i.tooltips!==!1&&(t.on("mouseenter",function(n){var i=t.tooltip().moveTo(-65535);if(n.control==t){var r=i.text(e.tooltip).show().testMoveRel(t.getEl(),["bc-tc","bc-tl","bc-tr"]);i.classes.toggle("tooltip-n","bc-tc"==r),i.classes.toggle("tooltip-nw","bc-tl"==r),i.classes.toggle("tooltip-ne","bc-tr"==r),i.moveRel(t.getEl(),r)}else i.hide()}),t.on("mouseleave mousedown click",function(){t.tooltip().hide()})),t.aria("label",e.ariaLabel||e.tooltip)},tooltip:function(){return n||(n=new t({type:"tooltip"}),n.renderTo()),n},postRender:function(){var e=this,t=e.settings;e._super(),e.parent()||!t.width&&!t.height||(e.initLayoutRect(),e.repaint()),t.autofocus&&e.focus()},bindStates:function(){function e(e){n.aria("disabled",e),n.classes.toggle("disabled",e)}function t(e){n.aria("pressed",e),n.classes.toggle("active",e)}var n=this;return n.state.on("change:disabled",function(t){e(t.value)}),n.state.on("change:active",function(e){t(e.value)}),n.state.get("disabled")&&e(!0),n.state.get("active")&&t(!0),n._super()},remove:function(){this._super(),n&&(n.remove(),n=null)}});return i}),i(M,[I],function(e){return e.extend({Defaults:{classes:"widget btn",role:"button"},init:function(e){var t=this,n;t._super(e),e=t.settings,n=t.settings.size,t.on("click mousedown",function(e){e.preventDefault()}),t.on("touchstart",function(e){t.fire("click",e),e.preventDefault()}),e.subtype&&t.classes.add(e.subtype),n&&t.classes.add("btn-"+n),e.icon&&t.icon(e.icon)},icon:function(e){return arguments.length?(this.state.set("icon",e),this):this.state.get("icon")},repaint:function(){var e=this.getEl().firstChild.style;e.width=e.height="100%",this._super()},renderHtml:function(){var e=this,t=e._id,n=e.classPrefix,i=e.state.get("icon"),r,o=e.state.get("text");return r=e.settings.image,r?(i="none","string"!=typeof r&&(r=window.getSelection?r[0]:r[1]),r=" style=\"background-image: url('"+r+"')\""):r="",i=e.settings.icon?n+"ico "+n+"i-"+i:"",'<div id="'+t+'" class="'+e.classes+'" tabindex="-1" aria-labelledby="'+t+'"><button role="presentation" type="button" tabindex="-1">'+(i?'<i class="'+i+'"'+r+"></i>":"")+(o?(i?"\xa0":"")+e.encode(o):"")+"</button></div>"},bindStates:function(){function e(e){for(var n=t.getEl().firstChild.firstChild;n;n=n.nextSibling)3==n.nodeType&&(n.previousSibling&&(e="\xa0"+e),n.nextSibling&&(e+="\xa0"),n.data=t.translate(e))}var t=this;return t.state.on("change:text",function(t){e(t.value)}),t.state.on("change:icon",function(n){var i=n.value,r=t.classPrefix;t.settings.icon=i,i=i?r+"ico "+r+"i-"+t.settings.icon:"";var o=t.getEl().firstChild,a=o.getElementsByTagName("i")[0];i?(a&&a==o.firstChild||(a=document.createElement("i"),o.insertBefore(a,o.firstChild)),a.className=i):a&&o.removeChild(a),e(t.state.get("text"))}),t._super()}})}),i(F,[],function(){var e={},t;return{add:function(t,n){e[t.toLowerCase()]=n},has:function(t){return!!e[t.toLowerCase()]},create:function(n,i){var r,o,a;if(!t){a=moxman.ui;for(o in a)e[o.toLowerCase()]=a[o];t=!0}if("string"==typeof n?(i=i||{},i.type=n):(i=n,n=i.type),n=n.toLowerCase(),r=e[n],!r)throw new Error("Could not find control by type: "+n);return r=new r(i),r.type=n,r}}}),i(P,[],function(){return function(e){function t(e){return e&&1===e.nodeType}function n(e){return e=e||b,t(e)?e.getAttribute("role"):null}function i(e){for(var t,i=e||b;i=i.parentNode;)if(t=n(i))return t}function r(e){var n=b;return t(n)?n.getAttribute("aria-"+e):void 0}function o(e){var t=e.tagName.toUpperCase();return"INPUT"==t||"TEXTAREA"==t}function a(e){return o(e)&&!e.hidden?!0:/^(button|menuitem|checkbox|tab|menuitemcheckbox|option|gridcell)$/.test(n(e))?!0:!1}function s(e){function t(e){if(1==e.nodeType&&"none"!=e.style.display){a(e)&&n.push(e);for(var i=0;i<e.childNodes.length;i++)t(e.childNodes[i])}}var n=[];return t(e||x.getEl()),n}function l(e){var t,n;e=e||w,n=e.parents().toArray(),n.unshift(e);for(var i=0;i<n.length&&(t=n[i],!t.settings.ariaRoot);i++);return t}function u(e){var t=l(e),n=s(t.getEl());t.settings.ariaRemember&&"lastAriaIndex"in t?c(t.lastAriaIndex,n):c(0,n)}function c(e,t){return 0>e?e=t.length-1:e>=t.length&&(e=0),t[e]&&t[e].focus(),e}function d(e,t){var n=-1,i=l();t=t||s(i.getEl());for(var r=0;r<t.length;r++)t[r]===b&&(n=r);n+=e,i.lastAriaIndex=c(n,t)}function f(){var e=i();"tablist"==e?d(-1,s(b.parentNode)):w.parent().submenu?v():d(-1)}function h(){var e=n(),t=i();"tablist"==t?d(1,s(b.parentNode)):"menuitem"==e&&"menu"==t&&r("haspopup")?y():d(1)}function p(){d(-1)}function m(){var e=n(),t=i();"menuitem"==e&&"menubar"==t?y():"button"==e&&r("haspopup")?y({key:"down"}):d(1)}function g(e){var t=i();if("tablist"==t){var n=s(w.getEl("body"))[0];n&&n.focus()}else d(e.shiftKey?-1:1)}function v(){w.fire("cancel")}function y(e){e=e||{},w.fire("click",{target:b,aria:e})}var x=e.root,b,w;try{b=document.activeElement}catch(_){b=document.body}return w=x.getParentCtrl(b),x.on("keydown",function(e){function t(e,t){o(b)||t(e)!==!1&&e.preventDefault()}if(!e.isDefaultPrevented())switch(e.keyCode){case 37:t(e,f);break;case 39:t(e,h);break;case 38:t(e,p);break;case 40:t(e,m);break;case 27:v();break;case 14:case 13:case 32:t(e,y);break;case 9:g(e)!==!1&&e.preventDefault()}}),x.on("focusin",function(e){b=e.target,w=e.control}),{focusFirst:u}}}),i(O,[k,w,b,F,P,c,T,S,A],function(e,t,n,i,r,o,a,s,l){var u={};return e.extend({init:function(e){var n=this;n._super(e),e=n.settings,e.fixed&&n.state.set("fixed",!0),n._items=new t,n.isRtl()&&n.classes.add("rtl"),n.bodyClasses=new s(function(){n.state.get("rendered")&&(n.getEl("body").className=this.toString())}),n.bodyClasses.prefix=n.classPrefix,n.classes.add("container"),n.bodyClasses.add("container-body"),e.containerCls&&n.classes.add(e.containerCls),n._layout=i.create((e.layout||"")+"layout"),n.add(n.settings.items?n.settings.items:n.render()),n._hasBody=!0},items:function(){return this._items},find:function(e){return e=u[e]=u[e]||new n(e),e.find(this)},add:function(e){var t=this;return t.items().add(t.create(e)).parent(t),t},focus:function(e){var t=this,n,i,r;return e&&(i=t.keyboardNav||t.parents().eq(-1)[0].keyboardNav)?void i.focusFirst(t):(r=t.find("*"),t.statusbar&&r.add(t.statusbar.items()),r.each(function(e){return e.settings.autofocus?(n=null,!1):void(e.canFocus&&(n=n||e))}),n&&n.focus(),t)},replace:function(e,t){for(var n,i=this.items(),r=i.length;r--;)if(i[r]===e){i[r]=t;break}r>=0&&(n=t.getEl(),n&&n.parentNode.removeChild(n),n=e.getEl(),n&&n.parentNode.removeChild(n)),t.parent(this)},create:function(t){var n=this,r,a=[];return o.isArray(t)||(t=[t]),o.each(t,function(t){t&&(t instanceof e||("string"==typeof t&&(t={type:t}),r=o.extend({},n.settings.defaults,t),t.type=r.type=r.type||t.type||n.settings.defaultType||(r.defaults?r.defaults.type:null),t=i.create(r)),a.push(t))}),a},renderNew:function(){var e=this;return e.items().each(function(t,n){var i;t.parent(e),t.state.get("rendered")||(i=e.getEl("body"),i.hasChildNodes()&&n<=i.childNodes.length-1?a(i.childNodes[n]).before(t.renderHtml()):a(i).append(t.renderHtml()),t.postRender(),l.add(t))}),e._layout.applyClasses(e.items().filter(":visible")),e._lastRect=null,e},append:function(e){return this.add(e).renderNew()},prepend:function(e){var t=this;return t.items().set(t.create(e).concat(t.items().toArray())),t.renderNew()},insert:function(e,t,n){var i=this,r,o,a;return e=i.create(e),r=i.items(),!n&&t<r.length-1&&(t+=1),t>=0&&t<r.length&&(o=r.slice(0,t).toArray(),a=r.slice(t).toArray(),r.set(o.concat(e,a))),i.renderNew()},fromJSON:function(e){var t=this;for(var n in e)t.find("#"+n).value(e[n]);return t},toJSON:function(){var e=this,t={};return e.find("*").each(function(e){var n=e.name(),i=e.value();n&&"undefined"!=typeof i&&(t[n]=i)}),t},renderHtml:function(){var e=this,t=e._layout,n=this.settings.role;return e.preRender(),t.preRender(e),'<div id="'+e._id+'" class="'+e.classes+'"'+(n?' role="'+this.settings.role+'"':"")+'><div id="'+e._id+'-body" class="'+e.bodyClasses+'">'+(e.settings.html||"")+t.renderHtml(e)+"</div></div>"},postRender:function(){var e=this,t;return e.items().exec("postRender"),e._super(),e._layout.postRender(e),e.state.set("rendered",!0),e.settings.style&&e.$el.css(e.settings.style),e.settings.border&&(t=e.borderBox,e.$el.css({"border-top-width":t.top,"border-right-width":t.right,"border-bottom-width":t.bottom,"border-left-width":t.left})),e.parent()||(e.keyboardNav=new r({root:e})),e},initLayoutRect:function(){var e=this,t=e._super();return e._layout.recalc(e),t},recalc:function(){var e=this,t=e._layoutRect,n=e._lastRect;return n&&n.w==t.w&&n.h==t.h?void 0:(e._layout.recalc(e),t=e.layoutRect(),e._lastRect={x:t.x,y:t.y,w:t.w,h:t.h},!0)},reflow:function(){var t;if(l.remove(this),this.visible()){for(e.repaintControls=[],e.repaintControls.map={},this.recalc(),t=e.repaintControls.length;t--;)e.repaintControls[t].repaint();"flow"!==this.settings.layout&&"stack"!==this.settings.layout&&this.repaint(),e.repaintControls=[]}return this}})}),i(H,[O],function(e){return e.extend({Defaults:{defaultType:"button",role:"group"},renderHtml:function(){var e=this,t=e._layout;return e.classes.add("btn-group"),e.preRender(),t.preRender(e),'<div id="'+e._id+'" class="'+e.classes+'"><div id="'+e._id+'-body">'+(e.settings.html||"")+t.renderHtml(e)+"</div></div>"}})}),i(L,[I],function(e){return e.extend({Defaults:{classes:"carousel",checked:!1},init:function(e){var t=this;t._super(e),t._data=e.data,t._index=0,t._first=!0,t.on("click",function(e){var n=e.target.className;-1!=n.indexOf("prev")&&t.prev(),-1!=n.indexOf("next")&&t.next()})},data:function(e){this._data=e},index:function(e,t){function n(e){var n,r=i.getEl("view");r.firstChild&&r.removeChild(r.firstChild),n=new Image,n.id=i._id+"-img",n.className=i.classPrefix+"object",n.onload=function(){n.style.visibility="visible",window.setTimeout(function(){var e=50,o=r.clientWidth-e,a=r.clientHeight-e,s=n.clientWidth,l=n.clientHeight,u=Math.min(o/s,a/l);1>u&&(s*=u,l*=u,n.style.width=s+"px",n.style.height=l+"px"),n.style.left=e/2+(o/2-s/2)+"px",n.style.top=e/2+(a/2-l/2)+"px",i._first=!i._first,t&&t()},0)},n.style.width=n.style.height="",n.src=e,r.appendChild(n)}var i=this;return"undefined"!=typeof e?(e>=0&&e<=this._data.length-1&&(i.getEl().firstChild.style.display=e>0?"block":"none",i.getEl().childNodes[1].style.display=e<i._data.length-1?"block":"none",n(this._data[e].url),i.fire("change",{index:e,lastIndex:i._index}),i._index=e),i):i._index},prev:function(){this.index(this.index()-1)},next:function(){this.index(this.index()+1)},postRender:function(){var e=this;e._super(),e.index(e.settings.selectedIndex||0)},renderHtml:function(){var e=this,t=e._id,n=e.classPrefix;return'<div id="'+t+'" class="'+e.classes+'"><div class="'+n+"dir "+n+'prev" unselectable="true">&lsaquo;</div><div class="'+n+"dir "+n+'next" unselectable="true">&rsaquo;</div><div id="'+t+'-view" class="'+n+'view"></div></div>'}})}),i(z,[I],function(e){return e.extend({Defaults:{classes:"checkbox",role:"checkbox",checked:!1},init:function(e){var t=this;t._super(e),t.on("click mousedown",function(e){e.preventDefault()}),t.on("click",function(e){e.preventDefault(),t.disabled()||t.checked(!t.checked())}),t.checked(t.settings.checked)},checked:function(e){return arguments.length?(this.state.set("checked",e),this):this.state.get("checked")},value:function(e){return arguments.length?this.checked(e):this.checked()},renderHtml:function(){var e=this,t=e._id,n=e.classPrefix;return'<div id="'+t+'" class="'+e.classes+'" unselectable="on" aria-labelledby="'+t+'-al" tabindex="-1"><i class="'+n+"ico "+n+'i-checkbox"></i><span id="'+t+'-al" class="'+n+'label">'+e.encode(e.state.get("text"))+"</span></div>"},bindStates:function(){function e(e){t.classes.toggle("checked",e),t.aria("checked",e)}var t=this;return t.state.on("change:text",function(e){t.getEl("al").firstChild.data=t.translate(e.value)}),t.state.on("change:checked change:value",function(t){e(t.value)}),t.state.on("change:icon",function(e){var n=e.value,i=t.classPrefix;if("undefined"==typeof n)return t.settings.icon;t.settings.icon=n,n=n?i+"ico "+i+"i-"+t.settings.icon:"";var r=t.getEl().firstChild,o=r.getElementsByTagName("i")[0];n?(o&&o==r.firstChild||(o=document.createElement("i"),r.insertBefore(o,r.firstChild)),o.className=n):o&&r.removeChild(o)}),t.state.get("checked")&&e(!0),t._super()}})}),i(B,[O],function(e){return e.extend({})}),i(W,[I,F,_,T],function(e,t,n,i){return e.extend({init:function(e){var t=this;t._super(e),e=t.settings,t.classes.add("combobox"),t.subinput=!0,t.ariaTarget="inp",e.menu=e.menu||e.values,e.menu&&(e.icon="caret"),t.on("click",function(n){for(var i=n.target,r=t.getEl();i&&i!=r;)i.id&&-1!=i.id.indexOf("-open")&&(t.fire("action"),e.menu&&(t.showMenu(),n.aria&&t.menu.items()[0].focus())),i=i.parentNode}),t.on("keydown",function(e){"INPUT"==e.target.nodeName&&13==e.keyCode&&t.parents().reverse().each(function(n){var i=t.state.get("value"),r=t.getEl("inp").value;return e.preventDefault(),t.state.set("value",r),i!=r&&t.fire("change"),n.hasEventListeners("submit")&&n.toJSON?(n.fire("submit",{data:n.toJSON()}),!1):void 0})}),t.on("keyup",function(e){"INPUT"==e.target.nodeName&&t.state.set("value",e.target.value)})},showMenu:function(){var e=this,n=e.settings,i;e.menu||(i=n.menu||[],i.length?i={type:"menu",items:i}:i.type=i.type||"menu",e.menu=t.create(i).parent(e).renderTo(e.getContainerElm()),e.fire("createmenu"),e.menu.reflow(),e.menu.on("cancel",function(t){t.control===e.menu&&e.focus()}),e.menu.on("show hide",function(t){t.control.items().each(function(t){t.active(t.value()==e.value())})}).fire("show"),e.menu.on("select",function(t){e.value(t.control.value())}),e.on("focusin",function(t){"INPUT"==t.target.tagName.toUpperCase()&&e.menu.hide()}),e.aria("expanded",!0)),e.menu.show(),e.menu.layoutRect({w:e.layoutRect().w}),e.menu.moveRel(e.getEl(),e.isRtl()?["br-tr","tr-br"]:["bl-tl","tl-bl"])},focus:function(){this.getEl("inp").focus()},repaint:function(){var e=this,t=e.getEl(),r=e.getEl("open"),o=e.layoutRect(),a,s;a=r?o.w-n.getSize(r).width-10:o.w-10;var l=document;return l.all&&(!l.documentMode||l.documentMode<=8)&&(s=e.layoutRect().h-2+"px"),i(t.firstChild).css({width:a,lineHeight:s}),e._super(),e},postRender:function(){var e=this;return i(this.getEl("inp")).on("change",function(t){e.state.set("value",t.target.value),e.fire("change",t)}),e._super()},renderHtml:function(){var e=this,t=e._id,n=e.settings,i=e.classPrefix,r=e.state.get("value")||"",o,a,s="",l="";return"spellcheck"in n&&(l+=' spellcheck="'+n.spellcheck+'"'),n.maxLength&&(l+=' maxlength="'+n.maxLength+'"'),n.size&&(l+=' size="'+n.size+'"'),n.subtype&&(l+=' type="'+n.subtype+'"'),e.disabled()&&(l+=' disabled="disabled"'),o=n.icon,o&&"caret"!=o&&(o=i+"ico "+i+"i-"+n.icon),a=e.state.get("text"),(o||a)&&(s='<div id="'+t+'-open" class="'+i+"btn "+i+'open" tabIndex="-1" role="button"><button id="'+t+'-action" type="button" hidefocus="1" tabindex="-1">'+("caret"!=o?'<i class="'+o+'"></i>':'<i class="'+i+'caret"></i>')+(a?(o?" ":"")+a:"")+"</button></div>",e.classes.add("has-open")),'<div id="'+t+'" class="'+e.classes+'"><input id="'+t+'-inp" class="'+i+'textbox" value="'+e.encode(r,!1)+'" hidefocus="1"'+l+' placeholder="'+e.encode(n.placeholder)+'" />'+s+"</div>"},bindStates:function(){var e=this;return e.state.on("change:value",function(t){e.getEl("inp").value=t.value}),e.state.on("change:disabled",function(t){e.getEl("inp").disabled=t.value}),e._super()},remove:function(){i(this.getEl("inp")).off(),this._super()}})}),i(U,[T],function(e){function t(){var e=document,t,n,i,r,o,a,s,l,u=Math.max;return t=e.documentElement,n=e.body,i=u(t.scrollWidth,n.scrollWidth),r=u(t.clientWidth,n.clientWidth),o=u(t.offsetWidth,n.offsetWidth),a=u(t.scrollHeight,n.scrollHeight),s=u(t.clientHeight,n.clientHeight),l=u(t.offsetHeight,n.offsetHeight),{width:o>i?r:i,height:l>a?s:a}}function n(e){var t,n;if(e.changedTouches)for(t="screenX screenY pageX pageY clientX clientY".split(" "),n=0;n<t.length;n++)e[t[n]]=e.changedTouches[0][t[n]]}return function(i,r){function o(){return s.getElementById(r.handle||i)}var a,s=document,l,u,c,d,f,h;r=r||{},u=function(i){var u=t(),p,m;n(i),i.preventDefault(),l=i.button,p=o(),f=i.screenX,h=i.screenY,m=window.getComputedStyle?window.getComputedStyle(p,null).getPropertyValue("cursor"):p.runtimeStyle.cursor,a=e("<div>").css({position:"absolute",top:0,left:0,width:u.width,height:u.height,zIndex:2147483647,opacity:1e-4,cursor:m}).appendTo(s.body),e(s).on("mousemove touchmove",d).on("mouseup touchend",c),r.start(i)},d=function(e){return n(e),e.button!==l?c(e):(e.deltaX=e.screenX-f,e.deltaY=e.screenY-h,e.preventDefault(),void r.drag(e))},c=function(t){n(t),e(s).off("mousemove touchmove",d).off("mouseup touchend",c),a.remove(),r.stop&&r.stop(t)},this.destroy=function(){e(o()).off()},e(o()).on("mousedown touchstart",u)}}),i(j,[O],function(e){return e.extend({Defaults:{layout:"flex",align:"center",defaults:{flex:1}},renderHtml:function(){var e=this,t=e._layout,n=e.classPrefix;return e.classes.add("formitem"),t.preRender(e),'<div id="'+e._id+'" class="'+e.classes+'" hidefocus="1" tabindex="-1">'+(e.settings.title?'<div id="'+e._id+'-title" class="'+n+'title">'+e.settings.title+"</div>":"")+'<div id="'+e._id+'-body" class="'+e.bodyClasses+'">'+(e.settings.html||"")+t.renderHtml(e)+"</div></div>"}})}),i(V,[O,j,c],function(e,t,n){return e.extend({Defaults:{containerCls:"form",layout:"flex",direction:"column",align:"stretch",flex:1,padding:20,labelGap:30,spacing:10,callbacks:{submit:function(){this.submit()}}},preRender:function(){var e=this,i=e.items();e.settings.formItemDefaults||(e.settings.formItemDefaults={layout:"flex",autoResize:"overflow",defaults:{flex:1}}),i.each(function(i){var r,o=i.settings.label;o&&(r=new t(n.extend({items:{type:"label",id:i._id+"-l",text:o,flex:0,forId:i._id,disabled:i.disabled()}},e.settings.formItemDefaults)),r.type="formitem",i.aria("labelledby",i._id+"-l"),"undefined"==typeof i.settings.flex&&(i.settings.flex=1),e.replace(i,r),r.add(i))})},submit:function(){return this.fire("submit",{data:this.toJSON()})},postRender:function(){var e=this;e._super(),e.fromJSON(e.settings.data)},bindStates:function(){function e(){var e=0,n=[],i,r,o;if(t.settings.labelGapCalc!==!1)for(o="children"==t.settings.labelGapCalc?t.find("formitem"):t.items(),o.filter("formitem").each(function(t){var i=t.items()[0],r=i.getEl().clientWidth;e=r>e?r:e,n.push(i)}),r=t.settings.labelGap||0,i=n.length;i--;)n[i].settings.minWidth=e+r}var t=this;t._super(),t.on("show",e),e()}})}),i(q,[V],function(e){return e.extend({Defaults:{containerCls:"fieldset",layout:"flex",direction:"column",align:"stretch",flex:1,padding:"25 15 5 15",labelGap:30,spacing:10,border:1},renderHtml:function(){var e=this,t=e._layout,n=e.classPrefix;return e.preRender(),t.preRender(e),'<fieldset id="'+e._id+'" class="'+e.classes+'" hidefocus="1" tabindex="-1">'+(e.settings.title?'<legend id="'+e._id+'-title" class="'+n+'fieldset-title">'+e.settings.title+"</legend>":"")+'<div id="'+e._id+'-body" class="'+e.bodyClasses+'">'+(e.settings.html||"")+t.renderHtml(e)+"</div></fieldset>"}})}),i(X,[h],function(e){return e.extend({recalc:function(e){var t=e.layoutRect(),n=e.paddingBox;e.items().filter(":visible").each(function(e){e.layoutRect({x:n.left,y:n.top,w:t.innerW-n.right-n.left,h:t.innerH-n.top-n.bottom}),e.recalc&&e.recalc()})}})}),i(Y,[h],function(e){return e.extend({recalc:function(e){var t,n,i,r,o,a,s,l,u,c,d,f,h,p,m,g,v=[],y,x,b,w,_,E,R,C,T,S,A,k,D,N,I,M,F,P,O,H,L,z,B=Math.max,W=Math.min;for(i=e.items().filter(":visible"),r=e.layoutRect(),o=e.paddingBox,a=e.settings,f=e.isRtl()?a.direction||"row-reversed":a.direction,s=a.align,l=e.isRtl()?a.pack||"end":a.pack,u=a.spacing||0,("row-reversed"==f||"column-reverse"==f)&&(i=i.set(i.toArray().reverse()),f=f.split("-")[0]),"column"==f?(T="y",R="h",C="minH",S="maxH",k="innerH",A="top",D="deltaH",N="contentH",O="left",F="w",I="x",M="innerW",P="minW",H="right",L="deltaW",z="contentW"):(T="x",R="w",C="minW",S="maxW",k="innerW",A="left",D="deltaW",N="contentW",O="top",F="h",I="y",M="innerH",P="minH",H="bottom",L="deltaH",z="contentH"),d=r[k]-o[A]-o[A],E=c=0,t=0,n=i.length;n>t;t++)h=i[t],p=h.layoutRect(),m=h.settings,g=m.flex,d-=n-1>t?u:0,g>0&&(c+=g,p[S]&&v.push(h),p.flex=g),d-=p[C],y=o[O]+p[P]+o[H],y>E&&(E=y);if(w={},0>d?w[C]=r[C]-d+r[D]:w[C]=r[k]-d+r[D],w[P]=E+r[L],w[N]=r[k]-d,w[z]=E,w.minW=W(w.minW,r.maxW),w.minH=W(w.minH,r.maxH),w.minW=B(w.minW,r.startMinWidth),w.minH=B(w.minH,r.startMinHeight),!r.autoResize||w.minW==r.minW&&w.minH==r.minH){for(b=d/c,t=0,n=v.length;n>t;t++)h=v[t],p=h.layoutRect(),x=p[S],y=p[C]+p.flex*b,y>x?(d-=p[S]-p[C],c-=p.flex,p.flex=0,p.maxFlexSize=x):p.maxFlexSize=0;for(b=d/c,_=o[A],w={},0===c&&("end"==l?_=d+o[A]:"center"==l?(_=Math.round(r[k]/2-(r[k]-d)/2)+o[A],0>_&&(_=o[A])):"justify"==l&&(_=o[A],u=Math.floor(d/(i.length-1)))),w[I]=o[O],t=0,n=i.length;n>t;t++)h=i[t],p=h.layoutRect(),y=p.maxFlexSize||p[C],"center"===s?w[I]=Math.round(r[M]/2-p[F]/2):"stretch"===s?(w[F]=B(p[P]||0,r[M]-o[O]-o[H]),w[I]=o[O]):"end"===s&&(w[I]=r[M]-p[F]-o.top),p.flex>0&&(y+=p.flex*b),w[R]=y,w[T]=_,h.layoutRect(w),h.recalc&&h.recalc(),_+=y+u}else if(w.w=w.minW,w.h=w.minH,e.layoutRect(w),this.recalc(e),null===e._lastRect){var U=e.parent();U&&(U._lastRect=null,U.recalc())}}})}),i($,[T,U],function(e,t){return{init:function(){var e=this;e.on("repaint",e.renderScroll)},renderScroll:function(){function n(){function t(t,a,s,l,u,c){var d,f,h,p,m,g,v,y,x;if(f=r.getEl("scroll"+t)){if(y=a.toLowerCase(),x=s.toLowerCase(),e(r.getEl("absend")).css(y,r.layoutRect()[l]-1),!u)return void e(f).css("display","none");e(f).css("display","block"),d=r.getEl("body"),h=r.getEl("scroll"+t+"t"),p=d["client"+s]-2*o,p-=n&&i?f["client"+c]:0,m=d["scroll"+s],g=p/m,v={},v[y]=d["offset"+a]+o,v[x]=p,e(f).css(v),v={},v[y]=d["scroll"+a]*g,v[x]=p*g,e(h).css(v)}}var n,i,a;a=r.getEl("body"),n=a.scrollWidth>a.clientWidth,i=a.scrollHeight>a.clientHeight,t("h","Left","Width","contentW",n,"Height"),t("v","Top","Height","contentH",i,"Width")}function i(){function n(n,i,a,s,l){var u,c=r._id+"-scroll"+n,d=r.classPrefix;e(r.getEl()).append('<div id="'+c+'" class="'+d+"scrollbar "+d+"scrollbar-"+n+'"><div id="'+c+'t" class="'+d+'scrollbar-thumb"></div></div>'),r.draghelper=new t(c+"t",{start:function(){u=r.getEl("body")["scroll"+i],e("#"+c).addClass(d+"active")},drag:function(e){var t,c,d,f,h=r.layoutRect();c=h.contentW>h.innerW,d=h.contentH>h.innerH,f=r.getEl("body")["client"+a]-2*o,f-=c&&d?r.getEl("scroll"+n)["client"+l]:0,t=f/r.getEl("body")["scroll"+a],r.getEl("body")["scroll"+i]=u+e["delta"+s]/t},stop:function(){e("#"+c).removeClass(d+"active")}})}r.classes.add("scroll"),n("v","Top","Height","Y","Width"),n("h","Left","Width","X","Height")}var r=this,o=2;r.settings.autoScroll&&(r._hasScroll||(r._hasScroll=!0,i(),r.on("wheel",function(e){var t=r.getEl("body");t.scrollLeft+=10*(e.deltaX||0),t.scrollTop+=10*e.deltaY,n()}),e(r.getEl("body")).on("scroll",n)),n())}}}),i(G,[O,$],function(e,t){return e.extend({Defaults:{layout:"fit",containerCls:"panel"},Mixins:[t],renderHtml:function(){var e=this,t=e._layout,n=e.settings.html;return e.preRender(),t.preRender(e),"undefined"==typeof n?n='<div id="'+e._id+'-body" class="'+e.bodyClasses+'">'+t.renderHtml(e)+"</div>":("function"==typeof n&&(n=n.call(e)),e._hasBody=!1),'<div id="'+e._id+'" class="'+e.classes+'" hidefocus="1" tabindex="-1" role="group">'+(e._preBodyHtml||"")+n+"</div>"}})}),i(J,[_],function(e){return{resizeToContent:function(){this._layoutRect.autoResize=!0,this._lastRect=null,this.reflow()},resizeTo:function(t,n){if(1>=t||1>=n){var i=e.getWindowSize();t=1>=t?t*i.w:t,n=1>=n?n*i.h:n}return this._layoutRect.autoResize=!1,this.layoutRect({minW:t,minH:n,w:t,h:n}).reflow()},resizeBy:function(e,t){var n=this,i=n.layoutRect();return n.resizeTo(i.w+e,i.h+t)}}}),i(K,[G,D,J,_,T],function(e,t,n,i,r){function o(){function e(e,t){for(;e;){if(e==t)return!0;e=e.parent()}}d||(d=function(t){if(2!=t.button)for(var n=p.length;n--;){var i=p[n],r=i.getParentCtrl(t.target);if(i.settings.autohide){if(r&&(e(r,i)||i.parent()===r))continue;t=i.fire("autohide",{target:t.target}),t.isDefaultPrevented()||i.hide()}}},r(document).on("click touchstart",d))}function a(){f||(f=function(){var e;for(e=p.length;e--;)l(p[e])},r(window).on("scroll",f))}function s(){if(!h){var e=document.documentElement,t=e.clientWidth,n=e.clientHeight;h=function(){document.all&&t==e.clientWidth&&n==e.clientHeight||(t=e.clientWidth,n=e.clientHeight,v.hideAll())},r(window).on("resize",h)}}function l(e){function t(t,n){for(var i,r=0;r<p.length;r++)if(p[r]!=e)for(i=p[r].parent();i&&(i=i.parent());)i==e&&p[r].fixed(t).moveBy(0,n).repaint()}var n=i.getViewPort().y;e.settings.autofix&&(e.state.get("fixed")?e._autoFixY>n&&(e.fixed(!1).layoutRect({y:e._autoFixY}).repaint(),t(!1,e._autoFixY-n)):(e._autoFixY=e.layoutRect().y,e._autoFixY<n&&(e.fixed(!0).layoutRect({y:0}).repaint(),t(!0,n-e._autoFixY))))}function u(e,t){var n,i=v.zIndex||65535,o;if(e)m.push(t);else for(n=m.length;n--;)m[n]===t&&m.splice(n,1);if(m.length)for(n=0;n<m.length;n++)m[n].modal&&(i++,o=m[n]),m[n].getEl().style.zIndex=i,m[n].zIndex=i,i++;var a=document.getElementById(t.classPrefix+"modal-block");o?r(a).css("z-index",o.zIndex-1):a&&(a.parentNode.removeChild(a),g=!1),v.currentZIndex=i}function c(e){var t;for(t=p.length;t--;)p[t]===e&&p.splice(t,1);for(t=m.length;t--;)m[t]===e&&m.splice(t,1)}var d,f,h,p=[],m=[],g,v=e.extend({Mixins:[t,n],init:function(e){var t=this;t._super(e),t._eventsRoot=t,t.classes.add("floatpanel"),e.autohide&&(o(),s(),p.push(t)),e.autofix&&(a(),t.on("move",function(){l(this)})),t.on("postrender show",function(e){if(e.control==t){var n,i=t.classPrefix;t.modal&&!g&&(n=r("#"+i+"modal-block"),n[0]||(n=r('<div id="'+i+'modal-block" class="'+i+"reset "+i+'fade"></div>').appendTo(t.getContainerElm())),setTimeout(function(){n.addClass(i+"in"),r(t.getEl()).addClass(i+"in")},0),g=!0),u(!0,t)}}),t.on("show",function(){t.parents().each(function(e){return e.state.get("fixed")?(t.fixed(!0),!1):void 0})}),e.popover&&(t._preBodyHtml='<div class="'+t.classPrefix+'arrow"></div>',t.classes.add("popover").add("bottom").add(t.isRtl()?"end":"start"))},fixed:function(e){var t=this;if(t.state.get("fixed")!=e){if(t.state.get("rendered")){var n=i.getViewPort();e?t.layoutRect().y-=n.y:t.layoutRect().y+=n.y}t.classes.toggle("fixed",e),t.state.set("fixed",e)}return t},show:function(){var e=this,t,n=e._super();for(t=p.length;t--&&p[t]!==e;);return-1===t&&p.push(e),n},hide:function(){return c(this),u(!1,this),this._super()},hideAll:function(){v.hideAll()},close:function(){var e=this;return e.fire("close").isDefaultPrevented()||(e.remove(),u(!1,e)),e},remove:function(){c(this),this._super()},postRender:function(){var e=this;return e.settings.bodyRole&&this.getEl("body").setAttribute("role",e.settings.bodyRole),e._super()}});return v.hideAll=function(){for(var e=p.length;e--;){var t=p[e];t&&t.settings.autohide&&(t.hide(),p.splice(e,1))}},v}),i(Z,[f],function(e){return e.extend({Defaults:{containerClass:"flow-layout",controlClass:"flow-layout-item",endClass:"break"},recalc:function(e){e.items().filter(":visible").each(function(e){e.recalc&&e.recalc()})},isNative:function(){return!0}})}),i(Q,[h],function(e){return e.extend({recalc:function(e){var t=e.settings,n,i,r,o,a,s,l,u,c,d,f,h,p,m,g,v,y,x,b,w,_,E,R=[],C=[],T,S,A,k,D,N;t=e.settings,r=e.items().filter(":visible"),o=e.layoutRect(),i=t.columns||Math.ceil(Math.sqrt(r.length)),n=Math.ceil(r.length/i),y=t.spacingH||t.spacing||0,x=t.spacingV||t.spacing||0,b=t.alignH||t.align,w=t.alignV||t.align,g=e.paddingBox,D="reverseRows"in t?t.reverseRows:e.isRtl(),b&&"string"==typeof b&&(b=[b]),w&&"string"==typeof w&&(w=[w]);for(d=0;i>d;d++)R.push(0);for(f=0;n>f;f++)C.push(0);for(f=0;n>f;f++)for(d=0;i>d&&(c=r[f*i+d],c);d++)u=c.layoutRect(),T=u.minW,S=u.minH,R[d]=T>R[d]?T:R[d],C[f]=S>C[f]?S:C[f];for(A=o.innerW-g.left-g.right,_=0,d=0;i>d;d++)_+=R[d]+(d>0?y:0),A-=(d>0?y:0)+R[d];for(k=o.innerH-g.top-g.bottom,E=0,f=0;n>f;f++)E+=C[f]+(f>0?x:0),k-=(f>0?x:0)+C[f];if(_+=g.left+g.right,E+=g.top+g.bottom,l={},l.minW=_+(o.w-o.innerW),l.minH=E+(o.h-o.innerH),l.contentW=l.minW-o.deltaW,l.contentH=l.minH-o.deltaH,l.minW=Math.min(l.minW,o.maxW),l.minH=Math.min(l.minH,o.maxH),l.minW=Math.max(l.minW,o.startMinWidth),l.minH=Math.max(l.minH,o.startMinHeight),!o.autoResize||l.minW==o.minW&&l.minH==o.minH){o.autoResize&&(l=e.layoutRect(l),l.contentW=l.minW-o.deltaW,l.contentH=l.minH-o.deltaH);var I;I="start"==t.packV?0:k>0?Math.floor(k/n):0;var M=0,F=t.flexWidths;if(F)for(d=0;d<F.length;d++)M+=F[d];else M=i;var P=A/M;for(d=0;i>d;d++)R[d]+=F?F[d]*P:P;for(p=g.top,f=0;n>f;f++){for(h=g.left,s=C[f]+I,d=0;i>d&&(N=D?f*i+i-1-d:f*i+d,c=r[N],c);d++)m=c.settings,u=c.layoutRect(),a=Math.max(R[d],u.startMinWidth),u.x=h,u.y=p,v=m.alignH||(b?b[d]||b[0]:null),"center"==v?u.x=h+a/2-u.w/2:"right"==v?u.x=h+a-u.w:"stretch"==v&&(u.w=a),v=m.alignV||(w?w[d]||w[0]:null),"center"==v?u.y=p+s/2-u.h/2:"bottom"==v?u.y=p+s-u.h:"stretch"==v&&(u.h=s),c.layoutRect(u),h+=a+y,c.recalc&&c.recalc();p+=s+x}}else if(l.w=l.minW,l.h=l.minH,e.layoutRect(l),this.recalc(e),null===e._lastRect){var O=e.parent();O&&(O._lastRect=null,O.recalc())}}})}),i(ee,[I],function(e){return e.extend({renderHtml:function(){var e=this;return e.classes.add("iframe"),e.canFocus=!1,'<iframe id="'+e._id+'" class="'+e.classes+'" tabindex="-1" src="'+(e.settings.url||"javascript:''")+'" frameborder="0"></iframe>'},src:function(e){
this.getEl().src=e},html:function(e,t){var n=this,i=this.getEl().contentWindow.document.body;return i?(i.innerHTML=e,t&&t()):setTimeout(function(){n.html(e)},0),this}})}),i(te,[],function(){function e(e){return"[object Number]"==Object.prototype.toString.call(e)}function n(e,t,n){var i=e.createShader(t);if(e.shaderSource(i,n),e.compileShader(i),!e.getShaderParameter(i,e.COMPILE_STATUS))throw"compile error: "+e.getShaderInfoLog(i);return i}var i="attribute vec2 vertex;attribute vec2 _texCoord;varying vec2 texCoord;void main() {texCoord = _texCoord;gl_Position = vec4(vertex * 2.0 - 1.0, 0.0, 1.0);}",r="uniform sampler2D texture;varying vec2 texCoord;void main() {gl_FragColor = texture2D(texture, texCoord);}";return function(o,a,s){function l(t){o.useProgram(h);for(var n in t)if(t.hasOwnProperty(n)){var i=o.getUniformLocation(h,n);if(null!==i){var r=t[n];if(r.length)switch(r.length){case 1:o.uniform1fv(i,new Float32Array(r));break;case 2:o.uniform2fv(i,new Float32Array(r));break;case 3:o.uniform3fv(i,new Float32Array(r));break;case 4:o.uniform4fv(i,new Float32Array(r));break;case 9:o.uniformMatrix3fv(i,!1,new Float32Array(r));break;case 16:o.uniformMatrix4fv(i,!1,new Float32Array(r));break;default:throw"dont't know how to load uniform \""+n+'" of length '+r.length}else{if(!e(r))throw'attempted to set uniform "'+n+'" to invalid value '+(r||"undefined").toString();o.uniform1f(i,r)}}}return f}function u(e){o.useProgram(h);for(var t in e)e.hasOwnProperty(t)&&o.uniform1i(o.getUniformLocation(h,t),e[t]);return f}function c(e,n,i,r){var a=o.getParameter(o.VIEWPORT);return n=n!==t?(n-a[1])/a[3]:0,e=e!==t?(e-a[0])/a[2]:0,i=i!==t?(i-a[0])/a[2]:1,r=r!==t?(r-a[1])/a[3]:1,o.vertexBuffer||(o.vertexBuffer=o.createBuffer()),o.bindBuffer(o.ARRAY_BUFFER,o.vertexBuffer),o.bufferData(o.ARRAY_BUFFER,new Float32Array([e,n,e,r,i,n,i,r]),o.STATIC_DRAW),o.texCoordBuffer||(o.texCoordBuffer=o.createBuffer(),o.bindBuffer(o.ARRAY_BUFFER,o.texCoordBuffer),o.bufferData(o.ARRAY_BUFFER,new Float32Array([0,0,0,1,1,0,1,1]),o.STATIC_DRAW)),p||(p=o.getAttribLocation(h,"vertex"),o.enableVertexAttribArray(p)),m||(m=o.getAttribLocation(h,"_texCoord"),o.enableVertexAttribArray(m)),o.useProgram(h),o.bindBuffer(o.ARRAY_BUFFER,o.vertexBuffer),o.vertexAttribPointer(p,2,o.FLOAT,!1,0,0),o.bindBuffer(o.ARRAY_BUFFER,o.texCoordBuffer),o.vertexAttribPointer(m,2,o.FLOAT,!1,0,0),o.drawArrays(o.TRIANGLE_STRIP,0,4),f}function d(){o.deleteProgram(h),h=null}var f=this,h,p,m;if(h=o.createProgram(),a=a||i,s=s||r,s="precision highp float;"+s,o.attachShader(h,n(o,o.VERTEX_SHADER,a)),o.attachShader(h,n(o,o.FRAGMENT_SHADER,s)),o.linkProgram(h),!o.getProgramParameter(h,o.LINK_STATUS))throw"link error: "+o.getProgramInfoLog(h);f.uniforms=l,f.textures=u,f.drawRect=c,f.destroy=d}}),i(ne,[],function(){return function(e,t,n,i,r){function o(o){t=o.width||o.videoWidth,n=o.height||o.videoHeight,e.bindTexture(e.TEXTURE_2D,u),e.texImage2D(e.TEXTURE_2D,0,i,i,r,o)}function a(){e.deleteTexture(u),u=null}function s(t){e.activeTexture(e.TEXTURE0+(t||0)),e.bindTexture(e.TEXTURE_2D,u)}function l(i){e.framebuffer=e.framebuffer||e.createFramebuffer(),e.bindFramebuffer(e.FRAMEBUFFER,e.framebuffer),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,u,0),e.viewport(0,0,t,n),i(),e.bindFramebuffer(e.FRAMEBUFFER,null)}var u=e.createTexture();return i=i||e.RGBA,r=r||e.UNSIGNED_BYTE,e.bindTexture(e.TEXTURE_2D,u),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),t&&n&&e.texImage2D(e.TEXTURE_2D,0,i,t,n,0,i,r,null),{loadContentsOf:o,destroy:a,use:s,drawTo:l,width:t,height:n}}}),i(ie,[te,ne],function(e,t){var n={vibrance:"uniform sampler2D texture;\nuniform float adjust;\nvarying vec2 texCoord;\n\nvoid main() {\n    vec4 color = texture2D(texture, texCoord);\n    float average = (color.r + color.g + color.b) / 3.0;\n    float mx = max(color.r, max(color.g, color.b));\n    float amt = (mx - average) * (-adjust * 3.0);\n\n    color.rgb = mix(color.rgb, vec3(mx), amt);\n\n    gl_FragColor = color;\n}\n",triangleblur:"uniform sampler2D texture;\nuniform vec2 delta;\nvarying vec2 texCoord;\n\nfloat random(vec3 scale, float seed) {\n	/* use the fragment position for a different seed per-pixel */\n	return fract(sin(dot(gl_FragCoord.xyz + seed, scale)) * 43758.5453 + seed);\n}\n\nvoid main() {\n	vec4 color = vec4(0.0);\n	float total = 0.0;\n\n	/* randomize the lookup values to hide the fixed number of samples */\n	float offset = random(vec3(12.9898, 78.233, 151.7182), 0.0);\n\n	for (float t = -30.0; t <= 30.0; t++) {\n		float percent = (t + offset - 0.5) / 30.0;\n		float weight = 1.0 - abs(percent);\n		vec4 sample = texture2D(texture, texCoord + delta * percent);\n\n		/* switch to pre-multiplied alpha to correctly blur transparent images */\n		sample.rgb *= sample.a;\n\n		color += sample * weight;\n		total += weight;\n	}\n\n	gl_FragColor = color / total;\n\n	/* switch back from pre-multiplied alpha */\n	gl_FragColor.rgb /= gl_FragColor.a + 0.00001;\n}\n\n",sepia:"uniform sampler2D texture;\r\nuniform float adjust;\r\nvarying vec2 texCoord;\r\n\r\nvoid main() {\r\n	vec4 color = texture2D(texture, texCoord);\r\n\r\n	float r = color.r;\r\n	float g = color.g;\r\n	float b = color.b;\r\n\r\n	color.r = min(1.0, (r * (1.0 - (0.607 * adjust))) + (g * (0.769 * adjust)) + (b * (0.189 * adjust)));\r\n	color.g = min(1.0, (r * 0.349 * adjust) + (g * (1.0 - (0.314 * adjust))) + (b * 0.168 * adjust));\r\n	color.b = min(1.0, (r * 0.272 * adjust) + (g * 0.534 * adjust) + (b * (1.0 - (0.869 * adjust))));\r\n\r\n	gl_FragColor = color;\r\n}",saturate:"uniform sampler2D texture;\r\nuniform float adjust;\r\nvarying vec2 texCoord;\r\n\r\nvoid main() {\r\n	vec4 color = texture2D(texture, texCoord);\r\n	float average = (color.r + color.g + color.b) / 3.0;\r\n\r\n	color.rgb += (average - color.rgb) * (adjust * -1.0);\r\n\r\n	gl_FragColor = color;\r\n}\r\n",invert:"uniform sampler2D texture;\r\nvarying vec2 texCoord;\r\n\r\nvoid main() {\r\n	vec4 sample = texture2D(texture, texCoord);\r\n\r\n	sample.r = 1.0 - sample.r;\r\n	sample.g = 1.0 - sample.g;\r\n	sample.b = 1.0 - sample.b;\r\n\r\n	gl_FragColor = sample;\r\n}\r\n",hue:"uniform sampler2D texture;\nuniform float adjust;\nvarying vec2 texCoord;\n\nvoid main() {\n	vec4 color = texture2D(texture, texCoord);\n\n	/* hue adjustment, wolfram alpha: RotationTransform[angle, {1, 1, 1}][{x, y, z}] */\n	float angle = adjust * 3.14159265;\n	float s = sin(angle), c = cos(angle);\n	vec3 weights = (vec3(2.0 * c, -sqrt(3.0) * s - c, sqrt(3.0) * s - c) + 1.0) / 3.0;\n	float len = length(color.rgb);\n	color.rgb = vec3(\n		dot(color.rgb, weights.xyz),\n		dot(color.rgb, weights.zxy),\n		dot(color.rgb, weights.yzx)\n	);\n\n	gl_FragColor = color;\n}\n",grayscale:"uniform sampler2D texture;\r\nvarying vec2 texCoord;\r\n\r\nvoid main() {\r\n	vec4 sample = texture2D(texture, texCoord);\r\n\r\n	sample.r = sample.g = sample.b = (sample.r + sample.g + sample.b) / 3.0;\r\n\r\n	gl_FragColor = sample;\r\n}\r\n",gamma:"uniform sampler2D texture;\r\nvarying vec2 texCoord;\r\nuniform float adjust;\r\n\r\nvoid main() {\r\n	vec4 sample = texture2D(texture, texCoord);\r\n\r\n	sample.r = pow(sample.r / 1.0, adjust) * 1.0;\r\n	sample.g = pow(sample.g / 1.0, adjust) * 1.0;\r\n	sample.b = pow(sample.b / 1.0, adjust) * 1.0;\r\n\r\n	gl_FragColor = sample;\r\n}\r\n",flippedshader:"uniform sampler2D texture;\r\nvarying vec2 texCoord;\r\n\r\nvoid main() {\r\n	gl_FragColor = texture2D(texture, vec2(texCoord.x, 1.0 - texCoord.y));\r\n}\r\n",exposure:"uniform sampler2D texture;\nvarying vec2 texCoord;\nuniform float adjust;\n\nvoid main() {\n	vec4 sample = texture2D(texture, texCoord);\n\n	sample.rgb = (1.0 - exp(-(sample.rgb) * adjust));\n\n	gl_FragColor = sample;\n}\n",convolute:"uniform sampler2D texture;\r\nuniform mat3 kernel;\r\nvarying vec2 texCoord;\r\nuniform vec2 textureSize;\r\n\r\nvoid main() {\r\n	vec2 onePixel = vec2(1.0, 1.0) / textureSize;\r\n	vec4 colorSum;\r\n\r\n	float kernelWeight;\r\n	for (int y = 0; y < 3; y++) {\r\n		for (int x = 0; x < 3; x++) {\r\n			colorSum += texture2D(texture, texCoord + onePixel * vec2(1 - y, 1 - x)) * kernel[x][y];\r\n			kernelWeight += kernel[y][x];\r\n		}\r\n	}\r\n\r\n	if (kernelWeight <= 0.0) {\r\n		kernelWeight = 1.0;\r\n	}\r\n\r\n	gl_FragColor = vec4((colorSum / kernelWeight).rgb, 1);\r\n}\r\n",contrast:"uniform sampler2D texture;\r\nvarying vec2 texCoord;\r\nuniform float adjust;\r\n\r\nvoid main() {\r\n	vec4 color = texture2D(texture, texCoord);\r\n\r\n	if (adjust > 0.0) {\r\n		color.rgb = (color.rgb - 0.5) / (1.0 - adjust) + 0.5;\r\n	} else {\r\n		color.rgb = (color.rgb - 0.5) * (1.0 + adjust) + 0.5;\r\n	}\r\n\r\n	gl_FragColor = color;\r\n}\r\n",colorize:"uniform sampler2D texture;\r\nvarying vec2 texCoord;\r\nuniform float adjustR, adjustG, adjustB;\r\n\r\nvoid main() {\r\n	vec4 sample = texture2D(texture, texCoord);\r\n\r\n	sample.r *= adjustR;\r\n	sample.g *= adjustG;\r\n	sample.b *= adjustB;\r\n\r\n	gl_FragColor = sample;\r\n}\r\n",brightness:"uniform sampler2D texture;\r\nvarying vec2 texCoord;\r\nuniform float adjust;\r\n\r\nvoid main() {\r\n	vec4 color = texture2D(texture, texCoord);\r\n\r\n	color.rgb += adjust;\r\n\r\n	gl_FragColor = color;\r\n}\r\n"};return function(i){function r(e,t){j[e]=t}function o(e,t){H.width=e,H.height=t}function a(e){o(e.naturalWidth||e.width,e.naturalHeight||e.height),L.drawImage(e,0,0)}function s(e,t){var n=new Image;n.onload=function(){n.onload=null,a(n),t&&t.call(O),k()},n.src=e}function l(e,t){t.width=e.width,t.height=e.height,t.getContext("2d").drawImage(e,0,0)}function u(e){V=!e}function c(){V||(W<B.length&&B.splice(W,B.length),i.undoLevels&&W>=i.undoLevels&&(B.shift(),W--),B.push(L.getImageData(0,0,H.width,H.height)),W=B.length)}function d(){return U.getElementById(i.image)}function f(e){var t=d();t.onload=function(){t.onload=null,t.style.display="",e()},t.src=D()}function h(e){return function(){c(),L.save(),e.apply(O,arguments),L.restore(),L.setTransform(1,0,0,1,0,0),f(k)}}function p(){return W>0}function m(){return W<B.length-1}function g(e){var t;e="undefined"!=typeof e?e:B.length-1,t=B[e],o(t.width,t.height),L.putImageData(t,0,0)}function v(e,t){g(e),W=e,t&&B.splice(e)}function y(e){p()&&(W==B.length&&B.push(L.getImageData(0,0,H.width,H.height)),v(W-1,e),f(k))}function x(e){m()&&(v(W+1,e),f(k))}function b(){p()&&(v(0,!0),f(k))}function w(e){var t=0,n=0,i,r;e=0>e?360+e:e,90==e||270==e?(i=H.height,r=H.width):(i=H.width,r=H.height),(90==e||180==e)&&(t=i),(270==e||180==e)&&(n=r),l(H,z),o(i,r),L.translate(t,n),L.rotate(e*Math.PI/180),L.drawImage(z,0,0)}function _(e){l(H,z),o(e.w,e.h),E(z,H)}function E(e,t){var n=e.width,i=e.height,r=t.width,o=t.height,a=e.getContext("2d").getImageData(0,0,n,i),s=e.getContext("2d").getImageData(0,0,r,o),l=a.data,u=s.data,c=n/r,d=i/o,f=Math.ceil(c/2),h=Math.ceil(d/2),p,m,g,v,y,x,b,w,_,E,R,C,T,S,A,k,D,N;for(_=0;o>_;_++)for(w=0;r>w;w++){for(C=4*(w+_*r),x=b=p=m=g=v=y=0,k=(_+.5)*d,R=Math.floor(_*d);(_+1)*d>R;R++)for(S=Math.abs(k-(R+.5))/h,A=(w+.5)*c,D=S*S,E=Math.floor(w*c);(w+1)*c>E;E++)T=Math.abs(A-(E+.5))/f,N=Math.sqrt(D+T*T),N>=-1&&1>=N&&(x=2*N*N*N-3*N*N+1,x>0&&(T=4*(E+R*n),y+=x*l[T+3],p+=x,l[T+3]<255&&(x=x*l[T+3]/250),m+=x*l[T],g+=x*l[T+1],v+=x*l[T+2],b+=x));u[C]=m/b,u[C+1]=g/b,u[C+2]=v/b,u[C+3]=y/p}t.getContext("2d").putImageData(s,0,0)}function R(e){"v"==e?(L.scale(1,-1),L.drawImage(H,0,-H.height)):(L.scale(-1,1),L.drawImage(H,-H.width,0))}function C(e){l(H,z),o(e.w,e.h),L.drawImage(z,-e.x,-e.y)}function T(e,t,n,r,o,a){function s(){var t=d[m++],o;t?(e(t.x,t.y,t.x+t.w,t.y+t.h,f.data,f.width,f.height,h?h.data:null),o=(new Date).getTime(),1e3>o-p?s():(p=o,window.setTimeout(s,0),i.onprogress&&i.onprogress.call(O,m/d.length))):(L.putImageData(h||f,n,r),i.onprogress&&i.onprogress.call(O,1),k())}var l,u,c=600,d=[],f,h,p,m=0;for(n=n||0,r=r||0,o=o||H.width,a=a||H.height,u=r;a>u;u+=c)for(l=n;o>l;l+=c)d.push({x:l,y:u,w:l+c>o?o-l:c,h:u+c>a?a-u:c});p=(new Date).getTime(),f=L.getImageData(n,r,o,a),t&&(h=L.getImageData(n,r,o,a)),s()}function S(e){T(function(t,n,i,r,o,a,s,l){var u,c,d,f,h,p,m,g,v,y,x,b,w;for(u=Math.round(Math.sqrt(e.length)),c=Math.floor(u/2),f=n;r>f;f++)for(d=t;i>d;d++){for(h=p=m=0,v=0;u>v;v++)for(g=0;u>g;g++)y=d+g-c,x=f+v-c,y=0>y?0:y>a?a:y,x=0>x?0:x>s?s:x,b=4*(x*a+y),w=e[v*u+g],h+=o[b]*w,p+=o[b+1]*w,m+=o[b+2]*w;b=4*(f*a+d),l[b]=h>255?255:0>h?0:h,l[b+1]=p>255?255:0>p?0:p,l[b+2]=m>255?255:0>m?0:m}},!0)}function A(){return{w:H.width,h:H.height}}function k(){i.onchange&&i.onchange.call(this)}function D(e){var t,n={jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png"};return e=e||"png",t=n[e.substr(e.lastIndexOf(".")+1).toLowerCase()],H.toDataURL(t)}function N(){var i,r;q.width=i=H.width,q.height=r=H.height,q.style.cssText=H.style.cssText,H.style.display="none",J=new e(X,null,n.flippedshader),Y=new t(X,i,r),$=new t(X,i,r),G=new t(X,i,r),Y.loadContentsOf(H)}function I(e,t,n,i){(n||Y).use(),(i||$).drawTo(function(){e.uniforms(t).drawRect()})}function M(){$.use(),J.drawRect()}function F(){function t(t,i){var r=K[t]=K[t]||new e(X,null,n[t]);I.call(this,r,i),M()}function i(e){r(e,function(n){t(e,{adjust:n})})}function o(e,n){r(e,function(){t("convolute",{textureSize:[Y.width,Y.height],kernel:n})})}if(X){for(var a in n)i(a);o("sharpen",[0,-1,0,-1,5,-1,0,-1,0]),o("emboss",[-2,-1,0,-1,1,1,0,1,2]),r("colorize",function(e,n,i){t("colorize",{adjustR:e,adjustG:n,adjustB:i})}),r("triangleblur",function(t){var i=K.triangleblur=K.triangleblur||new e(X,null,n.triangleblur);I.call(this,i,{delta:[t/Y.width,0]},Y,G),I.call(this,i,{delta:[0,t/Y.height]},G,$),M()})}}function P(e){function t(){n?X||g():(n=!0,d().style.display="none",X?(N(),q.style.display=""):(c(),H.style.display=""),e.init&&e.init())}var n;return{live:function(){var n=arguments;X&&(t(),e.liveGPU.apply(this,n))},apply:function(){var n=arguments;t(),X?e.liveGPU.apply(this,n):e.live.apply(this,n)},save:function(){function e(){var e=$.width,t=$.height,n=new Uint8Array(e*t*4);return $.drawTo(function(){X.readPixels(0,0,e,t,X.RGBA,X.UNSIGNED_BYTE,n)}),n}if(n=!1,X){for(var t=e(),i=L.createImageData($.width,$.height),r=i.data,o=t.length;o--;)r[o]=t[o];c(),L.putImageData(i,0,0),H.style.display="",q.style.display="none"}f(function(){H.style.display="none",k()})},cancel:function(){n=!1,d().style.display="",H.style.display="none",q.style.display="none",X||(v(W-1),k())}}}var O=this,H,L,z,B=[],W=0,U=document,j={},V,q,X,Y,$,G,J,K={};if(H=U.createElement("canvas"),i.canEdit&&H.getContext){L=H.getContext("2d"),H.style.display="none",U.getElementById(i.viewport).appendChild(H),z=U.createElement("canvas"),z.getContext("2d"),q=U.createElement("canvas"),q.style.display="none";try{X=q.getContext("experimental-webgl",{premultipliedAlpha:!1}),window.opera&&(X=null),X&&U.getElementById(i.viewport).appendChild(q)}catch(Z){}}return r("grayscale",function(){T(function(e,t,n,i,r,o){var a,s,l;for(s=t;i>s;s++)for(a=e;n>a;a++)l=4*(s*o+a),r[l]=r[l+1]=r[l+2]=(r[l]+r[l+1]+r[l+2])/3})}),r("invert",function(){T(function(e,t,n,i,r,o){var a,s,l;for(s=t;i>s;s++)for(a=e;n>a;a++)l=4*(s*o+a),r[l]=255-r[l],r[l+1]=255-r[l+1],r[l+2]=255-r[l+2]})}),r("gamma",function(e){for(var t=256,n=[];t--;)n[t]=255*Math.pow(t/255,e);T(function(e,t,i,r,o,a){var s,l,u,c=n;for(l=t;r>l;l++)for(s=e;i>s;s++)u=4*(l*a+s),o[u]=c[o[u]],o[u+1]=c[o[u+1]],o[u+2]=c[o[u+2]]})}),r("brightness",function(e){e=Math.floor(255*e),T(function(t,n,i,r,o,a){var s,l,u;for(l=n;r>l;l++)for(s=t;i>s;s++)u=4*(l*a+s),o[u]+=e,o[u+1]+=e,o[u+2]+=e})}),r("contrast",function(e){for(var t=256,n,i=[];t--;)n=255*((t/255-.5)*e+.5),i[t]=0>n?0:n>255?255:n;T(function(e,t,n,r,o,a){var s,l,u,c=i;for(l=t;r>l;l++)for(s=e;n>s;s++)u=4*(l*a+s),o[u]=c[o[u]],o[u+1]=c[o[u+1]],o[u+2]=c[o[u+2]]})}),r("colorize",function(e,t,n){for(var i=256,r=[],o=[],a=[];i--;)r[i]=i*e,o[i]=i*t,a[i]=i*n;T(function(e,t,n,i,s,l){var u,c,d,f=r,h=o,p=a;for(c=t;i>c;c++)for(u=e;n>u;u++)d=4*(c*l+u),s[d]=f[s[d]],s[d+1]=h[s[d+1]],s[d+2]=p[s[d+2]]})}),r("exposure",function(e){for(var t=256,n=[];t--;)n[t]=255*(1-Math.exp(-(t/255)*e));T(function(e,t,i,r,o,a){var s,l,u,c=n;for(l=t;r>l;l++)for(s=e;i>s;s++)u=4*(l*a+s),o[u]=c[o[u]],o[u+1]=c[o[u+1]],o[u+2]=c[o[u+2]]})}),r("sepia",function(e){T(function(t,n,i,r,o,a){var s,l,u,c;for(l=n;r>l;l++)for(s=t;i>s;s++)u=4*(l*a+s),c=.3*o[u]+.59*o[u+1]+.11*o[u+2],o[u]=c+40,o[u+1]=c+20,o[u+2]=c-e})}),r("saturate",function(e){e=-1*e,T(function(t,n,i,r,o,a){var s,l,u,c,d,f,h;for(l=n;r>l;l++)for(s=t;i>s;s++)u=4*(l*a+s),c=o[u],d=o[u+1],f=o[u+2],h=(c+d+f)/3,o[u]+=(h-c)*e,o[u+1]+=(h-d)*e,o[u+2]+=(h-f)*e})}),r("sharpen",function(){S([0,-1,0,-1,5,-1,0,-1,0])}),r("emboss",function(){S([-2,-1,0,-1,1,1,0,1,2])}),r("blur",function(e){e=e||1,T(function(t,n,i,r,o,a,s){var l,u,c,d,f,h,p,m,g,v,y,x;for(c=e;c--;)for(u=n;r>u;u++)for(l=t;i>l;l++){for(m=f=h=p=0,v=-1;2>v;v++)for(g=-1;2>g;g++)y=l+g,x=u+v,y=0>y?0:y>a?a:y,x=0>x?0:x>s?s:x,d=4*(x*a+y),f+=o[d],h+=o[d+1],p+=o[d+2],m++;d=4*(u*a+l),o[d]=f/m,o[d+1]=h/m,o[d+2]=p/m}})}),F(),{loadFromUrl:s,loadFromImage:a,getAsDataUrl:D,getSize:A,undo:y,redo:x,revert:b,canUndo:p,canRedo:m,addUndoLevel:c,setUndoEnabled:u,onChange:k,getCanvas:function(){return H},getWebGlCanvas:function(){return q},filter:function(e){return P({liveGPU:function(){j[e].apply(this,arguments)},live:function(){j[e].apply(this,arguments)}})},resize:h(_),crop:h(C),flip:h(R),rotate:h(w),hasCanvasSupport:function(){return!!L},hasWebGlSupport:function(){return!!X}}}}),i(re,[],function(){function e(e){return o.lastIndex=0,o.test(e)?'"'+e.replace(o,function(e){var t=l[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function n(t,i){var r,o,l,c,d=a,f,h=i[t];switch(h&&"object"==typeof h&&"function"==typeof h.toJSON&&(h=h.toJSON(t)),"function"==typeof u&&(h=u.call(i,t,h)),typeof h){case"string":return e(h);case"number":return isFinite(h)?String(h):"null";case"boolean":case"null":return String(h);case"object":if(!h)return"null";if(a+=s,f=[],"[object Array]"===Object.prototype.toString.apply(h)){for(c=h.length,r=0;c>r;r+=1)f[r]=n(r,h)||"null";return l=0===f.length?"[]":a?"[\n"+a+f.join(",\n"+a)+"\n"+d+"]":"["+f.join(",")+"]",a=d,l}if(u&&"object"==typeof u)for(c=u.length,r=0;c>r;r+=1)"string"==typeof u[r]&&(o=u[r],l=n(o,h),l&&f.push(e(o)+(a?": ":":")+l));else for(o in h)Object.prototype.hasOwnProperty.call(h,o)&&(l=n(o,h),l&&f.push(e(o)+(a?": ":":")+l));return l=0===f.length?"{}":a?"{\n"+a+f.join(",\n"+a)+"\n"+d+"}":"{"+f.join(",")+"}",a=d,l}}var i={};if("undefined"!=typeof JSON)return JSON;var r=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,o=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,a,s,l={"\b":"\\b","	":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},u;return"function"!=typeof i.stringify&&(i.stringify=function(e,t,i){var r;if(a="",s="","number"==typeof i)for(r=0;i>r;r+=1)s+=" ";else"string"==typeof i&&(s=i);if(u=t,t&&"function"!=typeof t&&("object"!=typeof t||"number"!=typeof t.length))throw new Error("jsJSON.stringify");return n("",{"":e})}),"function"!=typeof i.parse&&(i.parse=function(e,n){function i(e,r){var o,a,s=e[r];if(s&&"object"==typeof s)for(o in s)Object.prototype.hasOwnProperty.call(s,o)&&(a=i(s,o),a!==t?s[o]=a:delete s[o]);return n.call(e,r,s)}var o;if(e=String(e),r.lastIndex=0,r.test(e)&&(e=e.replace(r,function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})),/^[\],:{}\s]*$/.test(e.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))return o=window[String.fromCharCode(101)+"val"]("("+e+")"),"function"==typeof n?i({"":o},""):o;throw new SyntaxError("jsJSON.parse")}),i}),i(oe,[],function(){return{send:function(e){function t(){}function n(){if(!l||4==i.readyState||r++>a){if(a>r&&200==i.status)c.call(o,i.responseText);else{var t=r>a?1:2;d.call(o,{code:t,message:-1==t?"Request timed out":"Unknown error",httpRequest:i,options:e,status:i.status})}i=null}else s.setTimeout(n,10)}var i,r=0,o=this,a=1e4,s=window,l,u,c,d;if(l=e.async!==!1,u=e.data||"",c=e.success||t,d=e.error||t,u&&!e.contentType&&(e.contentType="application/x-www-form-urlencoded; charset=UTF-8"),s.XMLHttpRequest?i=new XMLHttpRequest:s.ActiveXObject&&(i=new ActiveXObject("Microsoft.XMLHTTP")),i){if(i.overrideMimeType&&i.overrideMimeType(e.contenType),i.open(e.method||(u?"POST":"GET"),e.url,l),e.contentType&&i.setRequestHeader("Content-Type",e.contentType),i.setRequestHeader("X-Requested-With","XMLHttpRequest"),e.csrfToken&&i.setRequestHeader("X-Csrf-Token",e.csrfToken),i.send(u),!l)return n();s.setTimeout(n,10)}}}}),i(ae,[],function(){function e(e){return"function"==typeof e||"object"==typeof e&&null!==e}function n(e){return"function"==typeof e}function i(e){return"object"==typeof e&&null!==e}function r(){return function(){process.nextTick(l)}}function o(){var e=0,t=new O(l),n=document.createTextNode("");return t.observe(n,{characterData:!0}),function(){n.data=e=++e%2}}function a(){var e=new MessageChannel;return e.port1.onmessage=l,function(){e.port2.postMessage(0)}}function s(){return function(){setTimeout(l,1)}}function l(){for(var e=0;M>e;e+=2){var n=L[e],i=L[e+1];n(i),L[e]=t,L[e+1]=t}M=0}function u(){}function c(){return new TypeError("You cannot resolve a promise with itself")}function d(){return new TypeError("A promises callback cannot return that same promise.")}function f(e){try{return e.then}catch(t){return j.error=t,j}}function h(e,t,n,i){try{e.call(t,n,i)}catch(r){return r}}function p(e,t,n){F(function(e){var i=!1,r=h(n,t,function(n){i||(i=!0,t!==n?v(e,n):x(e,n))},function(t){i||(i=!0,b(e,t))},"Settle: "+(e._label||" unknown promise"));!i&&r&&(i=!0,b(e,r))},e)}function m(e,n){n._state===W?x(e,n._result):e._state===U?b(e,n._result):w(n,t,function(t){v(e,t)},function(t){b(e,t)})}function g(e,i){if(i.constructor===e.constructor)m(e,i);else{var r=f(i);r===j?b(e,j.error):r===t?x(e,i):n(r)?p(e,i,r):x(e,i)}}function v(t,n){t===n?b(t,c()):e(n)?g(t,n):x(t,n)}function y(e){e._onerror&&e._onerror(e._result),_(e)}function x(e,t){e._state===B&&(e._result=t,e._state=W,0!==e._subscribers.length&&F(_,e))}function b(e,t){e._state===B&&(e._state=U,e._result=t,F(y,e))}function w(e,t,n,i){var r=e._subscribers,o=r.length;e._onerror=null,r[o]=t,r[o+W]=n,r[o+U]=i,0===o&&e._state&&F(_,e)}function _(e){var t=e._subscribers,n=e._state;if(0!==t.length){for(var i,r,o=e._result,a=0;a<t.length;a+=3)i=t[a],r=t[a+n],i?C(n,i,r,o):r(o);e._subscribers.length=0}}function E(){this.error=null}function R(e,t){try{return e(t)}catch(n){return V.error=n,V}}function C(e,t,i,r){var o=n(i),a,s,l,u;if(o){if(a=R(i,r),a===V?(u=!0,s=a.error,a=null):l=!0,t===a)return void b(t,d())}else a=r,l=!0;t._state!==B||(o&&l?v(t,a):u?b(t,s):e===W?x(t,a):e===U&&b(t,a))}function T(e,t){try{t(function i(t){v(e,t)},function r(t){b(e,t)})}catch(n){b(e,n)}}function S(e,t,n,i){this._instanceConstructor=e,this.promise=new e(u,i),this._abortOnReject=n,this._validateInput(t)?(this._input=t,this.length=t.length,this._remaining=t.length,this._init(),0===this.length?x(this.promise,this._result):(this.length=this.length||0,this._enumerate(),0===this._remaining&&x(this.promise,this._result))):b(this.promise,this._validationError())}function A(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}function k(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}function D(e,i){this._id=J++,this._label=i,this._state=t,this._result=t,this._subscribers=[],u!==e&&(n(e)||A(),this instanceof D||k(),T(this,e))}if(window.Promise)return window.Promise;var N;N=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)};var I=N,M=0,F=function Z(e,t){L[M]=e,L[M+1]=t,M+=2,2===M&&z()},P="undefined"!=typeof window?window:{},O=P.MutationObserver||P.WebKitMutationObserver,H="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel,L=new Array(1e3),z;z="undefined"!=typeof process&&"[object process]"==={}.toString.call(process)?r():O?o():H?a():s();var B=void 0,W=1,U=2,j=new E,V=new E;S.prototype._validateInput=function(e){return I(e)},S.prototype._validationError=function(){return new Error("Array Methods must be provided an Array")},S.prototype._init=function(){this._result=new Array(this.length)};var q=S;S.prototype._enumerate=function(){for(var e=this.length,t=this.promise,n=this._input,i=0;t._state===B&&e>i;i++)this._eachEntry(n[i],i)},S.prototype._eachEntry=function(e,t){var n=this._instanceConstructor;i(e)?e.constructor===n&&e._state!==B?(e._onerror=null,this._settledAt(e._state,t,e._result)):this._willSettleAt(n.resolve(e),t):(this._remaining--,this._result[t]=this._makeResult(W,t,e))},S.prototype._settledAt=function(e,t,n){var i=this.promise;i._state===B&&(this._remaining--,this._abortOnReject&&e===U?b(i,n):this._result[t]=this._makeResult(e,t,n)),0===this._remaining&&x(i,this._result)},S.prototype._makeResult=function(e,t,n){return n},S.prototype._willSettleAt=function(e,n){var i=this;w(e,t,function(e){i._settledAt(W,n,e)},function(e){i._settledAt(U,n,e)})};var X=function Q(e,t){return new q(this,e,!0,t).promise},Y=function ee(e,n){function i(e){v(a,e)}function r(e){b(a,e)}var o=this,a=new o(u,n);if(!I(e))return b(a,new TypeError("You must pass an array to race.")),a;for(var s=e.length,l=0;a._state===B&&s>l;l++)w(o.resolve(e[l]),t,i,r);return a},$=function te(e,t){var n=this;if(e&&"object"==typeof e&&e.constructor===n)return e;var i=new n(u,t);return v(i,e),i},G=function ne(e,t){var n=this,i=new n(u,t);return b(i,e),i},J=0,K=D;return D.all=X,D.race=Y,D.resolve=$,D.reject=G,D.prototype={constructor:D,then:function(e,t,n){var i=this,r=i._state;if(r===W&&!e||r===U&&!t)return this;i._onerror=null;var o=new this.constructor(u,n),a=i._result;if(r){var s=arguments[r-1];F(function(){C(r,o,s,a)})}else w(i,o,e,t);return o},"catch":function(e,t){return this.then(null,e,t)}},K}),i(se,[C,re,oe,ae],function(e,t,n,i){var r,o,a={request:function(s){return s=s||{},new i(function(i,l){var u=a.token;return u&&s.cache!==!1&&(new Date).getTime()-r<18e5?void i({token:u}):void n.send({url:e.apiPageUrl+"?action=auth",method:"POST",data:"time="+(new Date).getTime(),success:function(e){try{e=t.parse(e)}catch(n){return void l(new Error("Invalid JSON."))}return e.error?void l(new Error(e.error.message)):(a.setToken(e.token),o=e,void i(e))},error:function(){l(new Error("Authentication failed."))}})})},setToken:function(e){r=(new Date).getTime(),e&&(a.token=e)},getInfo:function(){return o},isStandalone:function(){return!!o.standalone}};return a}),i(le,[re,oe,se,ae,C],function(e,t,n,i,r){function o(e,t,n){var i=new Error(e);return i.code=t,i.data=n,i}function a(l){l=l||{},this.exec=function(u,c){return new i(function(i,d){function f(e){d(new o("Server returned an invalid response",-32603,e))}var h;h=e.stringify({id:"i"+s++,method:u,params:c||[],jsonrpc:"2.0"}),n.request().then(function(s){t.send({url:l.url||a.url||r.apiPageUrl,data:"json="+encodeURIComponent(h)+"&csrf="+encodeURIComponent(s.token),success:function(t){try{t=e.parse(t)}catch(r){return void f(t)}n.setToken(t.token),t.error?d(new o(t.error.message,t.error.code,t.error.data)):i(t.result)},error:function(e){f({statusCode:e.status})}})},function(){f(null)})})}}var s=0;return a.exec=function(e,t,n,i){return(new a).exec(e,t,n||a.successCallback,i||a.errorCallback)},a}),i(ue,[],function(){var e={normalize:function(e){var t,n,i,r=[];for(e=e.split("/"),t=0,n=e.length;n>t;t++)i=e[t],"."!==i&&(0===t||t==n-1||i.length>0)&&(".."===i?r.length>1&&r.pop():r.push(i));return 1===r.length&&0===r[0].length?"/":r.join("/")},join:function(e,t){return e.replace(/\/$/,"")+"/"+t.replace(/^\//,"")},resolve:function(t,n){return e.normalize(/^\//.test(n)?n:e.join(t,n))},relative:function(t,n){var i,r,o=[],a;if(t=e.normalize(t).split("/"),n=e.normalize(n).split("/"),t.length>=n.length){for(i=0,r=t.length;r>i;i++)if(i>=n.length||t[i]!=n[i]){a=i;break}}else{for(i=0,r=n.length;r>i;i++)if(i>=t.length||t[i]!=n[i]){a=i;break}a>t.length-1&&(t=[])}if(0===a)return o.join("/");for(i=0,r=t.length-a;r>i;i++)o.push("..");for(i=a,r=n.length;r>i;i++)o.push(n[i]);return t.length>0&&o.length>0&&(0===t[t.length-1].length&&t.pop(),0===o[0].length&&o.shift()),t.concat(o).join("/")},isChildOf:function(t,n){return 0===e.join(t,"/").indexOf(e.join(n,"/"))},dirname:function(e){return e.replace(/\\/g,"/").replace(/\/[^\/]*\/?$/,"")},basename:function(e,t){return e=e.replace(/\/$/,"").replace(/^.*[\/\\]/g,""),t&&e.substr(e.length-t.length)==t&&(e=e.substr(0,e.length-t.length)),e},extname:function(e){var t=e.lastIndexOf(".");return-1!=t?e.substr(t):""},isValidFileName:function(e){return!(/[\\\/:*?"<>\|]/.test(e)||/^\s*$/.test(e))}};return e}),i(ce,[],function(){var e=function(e){var t;return e===t?"undefined":null===e?"null":e.nodeType?"node":{}.toString.call(e).match(/\s([a-z|A-Z]+)/)[1].toLowerCase()},t=function(i){var r;return n(arguments,function(o,s){s>0&&n(o,function(n,o){n!==r&&(e(i[o])===e(n)&&~a(e(n),["array","object"])?t(i[o],n):i[o]=n)})}),i},n=function(e,t){var n,i,r,o;if(e){try{n=e.length}catch(a){n=o}if(n===o){for(i in e)if(e.hasOwnProperty(i)&&t(e[i],i)===!1)return}else for(r=0;n>r;r++)if(t(e[r],r)===!1)return}},i=function(t){var n;if(!t||"object"!==e(t))return!0;for(n in t)return!1;return!0},r=function(t,n){function i(r){"function"===e(t[r])&&t[r](function(e){++r<o&&!e?i(r):n(e)})}var r=0,o=t.length;"function"!==e(n)&&(n=function(){}),t&&t.length||n(),i(r)},o=function(e,t){var i=0,r=e.length,o=new Array(r);n(e,function(e,n){e(function(e){if(e)return t(e);var a=[].slice.call(arguments);a.shift(),o[n]=a,i++,i===r&&(o.unshift(null),t.apply(this,o))})})},a=function(e,t){if(t){if(Array.prototype.indexOf)return Array.prototype.indexOf.call(t,e);for(var n=0,i=t.length;i>n;n++)if(t[n]===e)return n}return-1},s=function(t,n){var i=[];"array"!==e(t)&&(t=[t]),"array"!==e(n)&&(n=[n]);for(var r in t)-1===a(t[r],n)&&i.push(t[r]);return i.length?i:!1},l=function(e,t){var i=[];return n(e,function(e){-1!==a(e,t)&&i.push(e)}),i.length?i:null},u=function(e){var t,n=[];for(t=0;t<e.length;t++)n[t]=e[t];return n},c=function(){var e=0;return function(t){var n=(new Date).getTime().toString(32),i;for(i=0;5>i;i++)n+=Math.floor(65535*Math.random()).toString(32);return(t||"o_")+n+(e++).toString(32)}}(),d=function(e){return e?String.prototype.trim?String.prototype.trim.call(e):e.toString().replace(/^\s*/,"").replace(/\s*$/,""):e},f=function(e){if("string"!=typeof e)return e;var t={t:1099511627776,g:1073741824,m:1048576,k:1024},n;return e=/^([0-9]+)([mgk]?)$/.exec(e.toLowerCase().replace(/[^0-9mkg]/g,"")),n=e[2],e=+e[1],t.hasOwnProperty(n)&&(e*=t[n]),e};return{guid:c,typeOf:e,extend:t,each:n,isEmptyObj:i,inSeries:r,inParallel:o,inArray:a,arrayDiff:s,arrayIntersect:l,toArray:u,trim:d,parseSizeStr:f}}),i(de,[ce],function(e){function t(e,t){var n;for(n in e)if(e[n]===t)return n;return null}return{RuntimeError:function(){function n(e){this.code=e,this.name=t(i,e),this.message=this.name+": RuntimeError "+this.code}var i={NOT_INIT_ERR:1,NOT_SUPPORTED_ERR:9,JS_ERR:4};return e.extend(n,i),n.prototype=Error.prototype,n}(),OperationNotAllowedException:function(){function t(e){this.code=e,this.name="OperationNotAllowedException"}return e.extend(t,{NOT_ALLOWED_ERR:1}),t.prototype=Error.prototype,t}(),ImageError:function(){function n(e){this.code=e,this.name=t(i,e),this.message=this.name+": ImageError "+this.code}var i={WRONG_FORMAT:1,MAX_RESOLUTION_ERR:2};return e.extend(n,i),n.prototype=Error.prototype,n}(),FileException:function(){function n(e){this.code=e,this.name=t(i,e),this.message=this.name+": FileException "+this.code}var i={NOT_FOUND_ERR:1,
SECURITY_ERR:2,ABORT_ERR:3,NOT_READABLE_ERR:4,ENCODING_ERR:5,NO_MODIFICATION_ALLOWED_ERR:6,INVALID_STATE_ERR:7,SYNTAX_ERR:8};return e.extend(n,i),n.prototype=Error.prototype,n}(),DOMException:function(){function n(e){this.code=e,this.name=t(i,e),this.message=this.name+": DOMException "+this.code}var i={INDEX_SIZE_ERR:1,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:3,WRONG_DOCUMENT_ERR:4,INVALID_CHARACTER_ERR:5,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:7,NOT_FOUND_ERR:8,NOT_SUPPORTED_ERR:9,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:11,SYNTAX_ERR:12,INVALID_MODIFICATION_ERR:13,NAMESPACE_ERR:14,INVALID_ACCESS_ERR:15,VALIDATION_ERR:16,TYPE_MISMATCH_ERR:17,SECURITY_ERR:18,NETWORK_ERR:19,ABORT_ERR:20,URL_MISMATCH_ERR:21,QUOTA_EXCEEDED_ERR:22,TIMEOUT_ERR:23,INVALID_NODE_TYPE_ERR:24,DATA_CLONE_ERR:25};return e.extend(n,i),n.prototype=Error.prototype,n}(),EventException:function(){function t(e){this.code=e,this.name="EventException"}return e.extend(t,{UNSPECIFIED_EVENT_TYPE_ERR:0}),t.prototype=Error.prototype,t}()}}),i(fe,[de,ce],function(e,t){function n(){var n={};t.extend(this,{uid:null,init:function(){this.uid||(this.uid=t.guid("uid_"))},addEventListener:function(e,i,r,o){var a=this,s;return e=t.trim(e),/\s/.test(e)?void t.each(e.split(/\s+/),function(e){a.addEventListener(e,i,r,o)}):(e=e.toLowerCase(),r=parseInt(r,10)||0,s=n[this.uid]&&n[this.uid][e]||[],s.push({fn:i,priority:r,scope:o||this}),n[this.uid]||(n[this.uid]={}),void(n[this.uid][e]=s))},hasEventListener:function(e){return e?!(!n[this.uid]||!n[this.uid][e]):!!n[this.uid]},removeEventListener:function(e,i){e=e.toLowerCase();var r=n[this.uid]&&n[this.uid][e],o;if(r){if(i){for(o=r.length-1;o>=0;o--)if(r[o].fn===i){r.splice(o,1);break}}else r=[];r.length||(delete n[this.uid][e],t.isEmptyObj(n[this.uid])&&delete n[this.uid])}},removeAllEventListeners:function(){n[this.uid]&&delete n[this.uid]},dispatchEvent:function(i){var r,o,a,s,l={},u=!0,c;if("string"!==t.typeOf(i)){if(s=i,"string"!==t.typeOf(s.type))throw new e.EventException(e.EventException.UNSPECIFIED_EVENT_TYPE_ERR);i=s.type,s.total!==c&&s.loaded!==c&&(l.total=s.total,l.loaded=s.loaded),l.async=s.async||!1}if(-1!==i.indexOf("::")?!function(e){r=e[0],i=e[1]}(i.split("::")):r=this.uid,i=i.toLowerCase(),o=n[r]&&n[r][i]){o.sort(function(e,t){return t.priority-e.priority}),a=[].slice.call(arguments),a.shift(),l.type=i,a.unshift(l);var d=[];t.each(o,function(e){a[0].target=e.scope,d.push(l.async?function(t){setTimeout(function(){t(e.fn.apply(e.scope,a)===!1)},1)}:function(t){t(e.fn.apply(e.scope,a)===!1)})}),d.length&&t.inSeries(d,function(e){u=!e})}return u},bind:function(){this.addEventListener.apply(this,arguments)},unbind:function(){this.removeEventListener.apply(this,arguments)},unbindAll:function(){this.removeAllEventListeners.apply(this,arguments)},trigger:function(){return this.dispatchEvent.apply(this,arguments)},convertEventPropsToHandlers:function(e){var n;"array"!==t.typeOf(e)&&(e=[e]);for(var i=0;i<e.length;i++)n="on"+e[i],"function"===t.typeOf(this[n])?this.addEventListener(e[i],this[n]):"undefined"===t.typeOf(this[n])&&(this[n]=null)}})}return n.instance=new n,n}),i(he,[],function(){var e=function(e){return unescape(encodeURIComponent(e))},t=function(e){return decodeURIComponent(escape(e))},n=function(e,n){if("function"==typeof window.atob)return n?t(window.atob(e)):window.atob(e);var i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r,o,a,s,l,u,c,d,f=0,h=0,p="",m=[];if(!e)return e;e+="";do s=i.indexOf(e.charAt(f++)),l=i.indexOf(e.charAt(f++)),u=i.indexOf(e.charAt(f++)),c=i.indexOf(e.charAt(f++)),d=s<<18|l<<12|u<<6|c,r=d>>16&255,o=d>>8&255,a=255&d,64==u?m[h++]=String.fromCharCode(r):64==c?m[h++]=String.fromCharCode(r,o):m[h++]=String.fromCharCode(r,o,a);while(f<e.length);return p=m.join(""),n?t(p):p},i=function(t,n){if(n&&e(t),"function"==typeof window.btoa)return window.btoa(t);var i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r,o,a,s,l,u,c,d,f=0,h=0,p="",m=[];if(!t)return t;do r=t.charCodeAt(f++),o=t.charCodeAt(f++),a=t.charCodeAt(f++),d=r<<16|o<<8|a,s=d>>18&63,l=d>>12&63,u=d>>6&63,c=63&d,m[h++]=i.charAt(s)+i.charAt(l)+i.charAt(u)+i.charAt(c);while(f<t.length);p=m.join("");var g=t.length%3;return(g?p.slice(0,g-3):p)+"===".slice(g||3)};return{utf8_encode:e,utf8_decode:t,atob:n,btoa:i}}),i(pe,[],function(){var e=function(t,n){for(var i=["source","scheme","authority","userInfo","user","pass","host","port","relative","path","directory","file","query","fragment"],r=i.length,o={http:80,https:443},a={},s=/^(?:([^:\/?#]+):)?(?:\/\/()(?:(?:()(?:([^:@]*):?([^:@]*))?@)?([^:\/?#]*)(?::(\d*))?))?()(?:(()(?:(?:[^?#\/]*\/)*)()(?:[^?#]*))(?:\\?([^#]*))?(?:#(.*))?)/,l=s.exec(t||"");r--;)l[r]&&(a[i[r]]=l[r]);if(!a.scheme){n&&"string"!=typeof n||(n=e(n||document.location.href)),a.scheme=n.scheme,a.host=n.host,a.port=n.port;var u="";/^[^\/]/.test(a.path)&&(u=n.path,/(\/|\/[^\.]+)$/.test(u)?u+="/":u=u.replace(/\/[^\/]+$/,"/")),a.path=u+(a.path||"")}return a.port||(a.port=o[a.scheme]||80),a.port=parseInt(a.port,10),a.path||(a.path="/"),delete a.source,a},t=function(t){var n={http:80,https:443},i=e(t);return i.scheme+"://"+i.host+(i.port!==n[i.scheme]?":"+i.port:"")+i.path+(i.query?i.query:"")},n=function(t){function n(e){return[e.scheme,e.host,e.port].join("/")}return"string"==typeof t&&(t=e(t)),n(e())===n(t)};return{parseUrl:e,resolveUrl:t,hasSameOrigin:n}}),i(me,[ce],function(e){function t(e,t,n){var i=0,r=0,o=0,a={dev:-6,alpha:-5,a:-5,beta:-4,b:-4,RC:-3,rc:-3,"#":-2,p:1,pl:1},s=function(e){return e=(""+e).replace(/[_\-+]/g,"."),e=e.replace(/([^.\d]+)/g,".$1.").replace(/\.{2,}/g,"."),e.length?e.split("."):[-8]},l=function(e){return e?isNaN(e)?a[e]||-7:parseInt(e,10):0};for(e=s(e),t=s(t),r=Math.max(e.length,t.length),i=0;r>i;i++)if(e[i]!=t[i]){if(e[i]=l(e[i]),t[i]=l(t[i]),e[i]<t[i]){o=-1;break}if(e[i]>t[i]){o=1;break}}if(!n)return o;switch(n){case">":case"gt":return o>0;case">=":case"ge":return o>=0;case"<=":case"le":return 0>=o;case"==":case"=":case"eq":return 0===o;case"<>":case"!=":case"ne":return 0!==o;case"":case"<":case"lt":return 0>o;default:return null}}var n=function(e){var t="",n="?",i="function",r="undefined",o="object",a="major",s="model",l="name",u="type",c="vendor",d="version",f="architecture",h="console",p="mobile",m="tablet",g={has:function(e,t){return-1!==t.toLowerCase().indexOf(e.toLowerCase())},lowerize:function(e){return e.toLowerCase()}},v={rgx:function(){for(var t,n=0,a,s,l,u,c,d,f=arguments;n<f.length;n+=2){var h=f[n],p=f[n+1];if(typeof t===r){t={};for(l in p)u=p[l],typeof u===o?t[u[0]]=e:t[u]=e}for(a=s=0;a<h.length;a++)if(c=h[a].exec(this.getUA())){for(l=0;l<p.length;l++)d=c[++s],u=p[l],typeof u===o&&u.length>0?2==u.length?typeof u[1]==i?t[u[0]]=u[1].call(this,d):t[u[0]]=u[1]:3==u.length?typeof u[1]!==i||u[1].exec&&u[1].test?t[u[0]]=d?d.replace(u[1],u[2]):e:t[u[0]]=d?u[1].call(this,d,u[2]):e:4==u.length&&(t[u[0]]=d?u[3].call(this,d.replace(u[1],u[2])):e):t[u]=d?d:e;break}if(c)break}return t},str:function(t,i){for(var r in i)if(typeof i[r]===o&&i[r].length>0){for(var a=0;a<i[r].length;a++)if(g.has(i[r][a],t))return r===n?e:r}else if(g.has(i[r],t))return r===n?e:r;return t}},y={browser:{oldsafari:{major:{1:["/8","/1","/3"],2:"/4","?":"/"},version:{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}}},device:{sprint:{model:{"Evo Shift 4G":"7373KT"},vendor:{HTC:"APA",Sprint:"Sprint"}}},os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2000:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",RT:"ARM"}}}},x={browser:[[/(opera\smini)\/((\d+)?[\w\.-]+)/i,/(opera\s[mobiletab]+).+version\/((\d+)?[\w\.-]+)/i,/(opera).+version\/((\d+)?[\w\.]+)/i,/(opera)[\/\s]+((\d+)?[\w\.]+)/i],[l,d,a],[/\s(opr)\/((\d+)?[\w\.]+)/i],[[l,"Opera"],d,a],[/(kindle)\/((\d+)?[\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?((\d+)?[\w\.]+)*/i,/(avant\s|iemobile|slim|baidu)(?:browser)?[\/\s]?((\d+)?[\w\.]*)/i,/(?:ms|\()(ie)\s((\d+)?[\w\.]+)/i,/(rekonq)((?:\/)[\w\.]+)*/i,/(chromium|flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron)\/((\d+)?[\w\.-]+)/i],[l,d,a],[/(trident).+rv[:\s]((\d+)?[\w\.]+).+like\sgecko/i],[[l,"IE"],d,a],[/(yabrowser)\/((\d+)?[\w\.]+)/i],[[l,"Yandex"],d,a],[/(comodo_dragon)\/((\d+)?[\w\.]+)/i],[[l,/_/g," "],d,a],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?((\d+)?[\w\.]+)/i],[l,d,a],[/(dolfin)\/((\d+)?[\w\.]+)/i],[[l,"Dolphin"],d,a],[/((?:android.+)crmo|crios)\/((\d+)?[\w\.]+)/i],[[l,"Chrome"],d,a],[/((?:android.+))version\/((\d+)?[\w\.]+)\smobile\ssafari/i],[[l,"Android Browser"],d,a],[/version\/((\d+)?[\w\.]+).+?mobile\/\w+\s(safari)/i],[d,a,[l,"Mobile Safari"]],[/version\/((\d+)?[\w\.]+).+?(mobile\s?safari|safari)/i],[d,a,l],[/webkit.+?(mobile\s?safari|safari)((\/[\w\.]+))/i],[l,[a,v.str,y.browser.oldsafari.major],[d,v.str,y.browser.oldsafari.version]],[/(konqueror)\/((\d+)?[\w\.]+)/i,/(webkit|khtml)\/((\d+)?[\w\.]+)/i],[l,d,a],[/(navigator|netscape)\/((\d+)?[\w\.-]+)/i],[[l,"Netscape"],d,a],[/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?((\d+)?[\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix)\/((\d+)?[\w\.-]+)/i,/(mozilla)\/((\d+)?[\w\.]+).+rv\:.+gecko\/\d+/i,/(uc\s?browser|polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|qqbrowser)[\/\s]?((\d+)?[\w\.]+)/i,/(links)\s\(((\d+)?[\w\.]+)/i,/(gobrowser)\/?((\d+)?[\w\.]+)*/i,/(ice\s?browser)\/v?((\d+)?[\w\._]+)/i,/(mosaic)[\/\s]((\d+)?[\w\.]+)/i],[l,d,a]],engine:[[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[l,d],[/rv\:([\w\.]+).*(gecko)/i],[d,l]],os:[[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*|windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)/i],[l,[d,v.str,y.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[l,"Windows"],[d,v.str,y.os.windows.version]],[/\((bb)(10);/i],[[l,"BlackBerry"],d],[/(blackberry)\w*\/?([\w\.]+)*/i,/(tizen)\/([\w\.]+)/i,/(android|webos|palm\os|qnx|bada|rim\stablet\sos|meego)[\/\s-]?([\w\.]+)*/i],[l,d],[/(symbian\s?os|symbos|s60(?=;))[\/\s-]?([\w\.]+)*/i],[[l,"Symbian"],d],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[l,"Firefox OS"],d],[/(nintendo|playstation)\s([wids3portablevu]+)/i,/(mint)[\/\s\(]?(\w+)*/i,/(joli|[kxln]?ubuntu|debian|[open]*suse|gentoo|arch|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk)[\/\s-]?([\w\.-]+)*/i,/(hurd|linux)\s?([\w\.]+)*/i,/(gnu)\s?([\w\.]+)*/i],[l,d],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[l,"Chromium OS"],d],[/(sunos)\s?([\w\.]+\d)*/i],[[l,"Solaris"],d],[/\s([frentopc-]{0,4}bsd|dragonfly)\s?([\w\.]+)*/i],[l,d],[/(ip[honead]+)(?:.*os\s*([\w]+)*\slike\smac|;\sopera)/i],[[l,"iOS"],[d,/_/g,"."]],[/(mac\sos\sx)\s?([\w\s\.]+\w)*/i],[l,[d,/_/g,"."]],[/(haiku)\s(\w+)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.]*)*/i,/(macintosh|mac(?=_powerpc)|plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos)/i,/(unix)\s?([\w\.]+)*/i],[l,d]]},b=function(e){var n=e||(window&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:t);this.getBrowser=function(){return v.rgx.apply(this,x.browser)},this.getEngine=function(){return v.rgx.apply(this,x.engine)},this.getOS=function(){return v.rgx.apply(this,x.os)},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS()}},this.getUA=function(){return n},this.setUA=function(e){return n=e,this},this.setUA(n)};return(new b).getResult()}(),i=function(){var t={define_property:function(){return!1}(),create_canvas:function(){var e=document.createElement("canvas");return!(!e.getContext||!e.getContext("2d"))}(),return_response_type:function(t){try{if(-1!==e.inArray(t,["","text","document"]))return!0;if(window.XMLHttpRequest){var n=new XMLHttpRequest;if(n.open("get","/"),"responseType"in n)return n.responseType=t,n.responseType!==t?!1:!0}}catch(i){}return!1},use_data_uri:function(){var e=new Image;return e.onload=function(){t.use_data_uri=1===e.width&&1===e.height},setTimeout(function(){e.src="data:image/gif;base64,R0lGODlhAQABAIAAAP8AAAAAACH5BAAAAAAALAAAAAABAAEAAAICRAEAOw=="},1),!1}(),use_data_uri_over32kb:function(){return t.use_data_uri&&("IE"!==r.browser||r.version>=9)},use_data_uri_of:function(e){return t.use_data_uri&&33e3>e||t.use_data_uri_over32kb()},use_fileinput:function(){var e=document.createElement("input");return e.setAttribute("type","file"),!e.disabled}};return function(n){var i=[].slice.call(arguments);return i.shift(),"function"===e.typeOf(t[n])?t[n].apply(this,i):!!t[n]}}(),r={can:i,browser:n.browser.name,version:parseFloat(n.browser.major),os:n.os.name,osVersion:n.os.version,verComp:t,swf_url:"../flash/Moxie.swf",xap_url:"../silverlight/Moxie.xap",global_event_dispatcher:"moxie.core.EventTarget.instance.dispatchEvent"};return r.OS=r.os,r}),i(ge,[me],function(e){var t=function(e){return"string"!=typeof e?e:document.getElementById(e)},n=function(e,t){if(!e.className)return!1;var n=new RegExp("(^|\\s+)"+t+"(\\s+|$)");return n.test(e.className)},i=function(e,t){n(e,t)||(e.className=e.className?e.className.replace(/\s+$/,"")+" "+t:t)},r=function(e,t){if(e.className){var n=new RegExp("(^|\\s+)"+t+"(\\s+|$)");e.className=e.className.replace(n,function(e,t,n){return" "===t&&" "===n?" ":""})}},o=function(e,t){return e.currentStyle?e.currentStyle[t]:window.getComputedStyle?window.getComputedStyle(e,null)[t]:void 0},a=function(t,n){function i(e){var t,n,i=0,r=0;return e&&(n=e.getBoundingClientRect(),t="CSS1Compat"===s.compatMode?s.documentElement:s.body,i=n.left+t.scrollLeft,r=n.top+t.scrollTop),{x:i,y:r}}var r=0,o=0,a,s=document,l,u;if(t=t,n=n||s.body,t&&t.getBoundingClientRect&&"IE"===e.browser&&(!s.documentMode||s.documentMode<8))return l=i(t),u=i(n),{x:l.x-u.x,y:l.y-u.y};for(a=t;a&&a!=n&&a.nodeType;)r+=a.offsetLeft||0,o+=a.offsetTop||0,a=a.offsetParent;for(a=t.parentNode;a&&a!=n&&a.nodeType;)r-=a.scrollLeft||0,o-=a.scrollTop||0,a=a.parentNode;return{x:r,y:o}},s=function(e){return{w:e.offsetWidth||e.clientWidth,h:e.offsetHeight||e.clientHeight}};return{get:t,hasClass:n,addClass:i,removeClass:r,getStyle:o,getPos:a,getSize:s}}),i(ve,[ce,ge,fe],function(e,t,n){function i(n,r,a,s,l){var u=this,c,d=e.guid(r+"_"),f=l||"browser";n=n||{},o[d]=this,a=e.extend({access_binary:!1,access_image_binary:!1,display_media:!1,do_cors:!1,drag_and_drop:!1,filter_by_extension:!0,resize_image:!1,report_upload_progress:!1,return_response_headers:!1,return_response_type:!1,return_status_code:!0,send_custom_headers:!1,select_file:!1,select_folder:!1,select_multiple:!0,send_binary_string:!1,send_browser_cookies:!0,send_multipart:!0,slice_blob:!1,stream_upload:!1,summon_file_dialog:!1,upload_filesize:!0,use_http_method:!0},a),n.preferred_caps&&(f=i.getMode(s,n.preferred_caps,f)),c=function(){var t={};return{exec:function(e,n,i,r){return c[n]&&(t[e]||(t[e]={context:this,instance:new c[n]}),t[e].instance[i])?t[e].instance[i].apply(this,r):void 0},removeInstance:function(e){delete t[e]},removeAllInstances:function(){var n=this;e.each(t,function(t,i){"function"===e.typeOf(t.instance.destroy)&&t.instance.destroy.call(t.context),n.removeInstance(i)})}}}(),e.extend(this,{initialized:!1,uid:d,type:r,mode:i.getMode(s,n.required_caps,f),shimid:d+"_container",clients:0,options:n,can:function(t,n){var r=arguments[2]||a;if("string"===e.typeOf(t)&&"undefined"===e.typeOf(n)&&(t=i.parseCaps(t)),"object"===e.typeOf(t)){for(var o in t)if(!this.can(o,t[o],r))return!1;return!0}return"function"===e.typeOf(r[t])?r[t].call(this,n):n===r[t]},getShimContainer:function(){var n,i=t.get(this.shimid);return i||(n=this.options.container?t.get(this.options.container):document.body,i=document.createElement("div"),i.id=this.shimid,i.className="moxie-shim moxie-shim-"+this.type,e.extend(i.style,{position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),n.appendChild(i),n=null),i},getShim:function(){return c},shimExec:function(e,t){var n=[].slice.call(arguments,2);return u.getShim().exec.call(this,this.uid,e,t,n)},exec:function(e,t){var n=[].slice.call(arguments,2);return u[e]&&u[e][t]?u[e][t].apply(this,n):u.shimExec.apply(this,arguments)},destroy:function(){if(u){var e=t.get(this.shimid);e&&e.parentNode.removeChild(e),c&&c.removeAllInstances(),this.unbindAll(),delete o[this.uid],this.uid=null,d=u=c=e=null}}}),this.mode&&n.required_caps&&!this.can(n.required_caps)&&(this.mode=!1)}var r={},o={};return i.order="html5,flash,silverlight,html4",i.getRuntime=function(e){return o[e]?o[e]:!1},i.addConstructor=function(e,t){t.prototype=n.instance,r[e]=t},i.getConstructor=function(e){return r[e]||null},i.getInfo=function(e){var t=i.getRuntime(e);return t?{uid:t.uid,type:t.type,mode:t.mode,can:function(){return t.can.apply(t,arguments)}}:null},i.parseCaps=function(t){var n={};return"string"!==e.typeOf(t)?t||{}:(e.each(t.split(","),function(e){n[e]=!0}),n)},i.can=function(e,t){var n,r=i.getConstructor(e),o;return r?(n=new r({required_caps:t}),o=n.mode,n.destroy(),!!o):!1},i.thatCan=function(e,t){var n=(t||i.order).split(/\s*,\s*/);for(var r in n)if(i.can(n[r],e))return n[r];return null},i.getMode=function(t,n,i){var r=null;if("undefined"===e.typeOf(i)&&(i="browser"),n&&!e.isEmptyObj(t)){if(e.each(n,function(n,i){if(t.hasOwnProperty(i)){var o=t[i](n);if("string"==typeof o&&(o=[o]),r){if(!(r=e.arrayIntersect(r,o)))return r=!1}else r=o}}),r)return-1!==e.inArray(i,r)?i:r[0];if(r===!1)return!1}return i},i.capTrue=function(){return!0},i.capFalse=function(){return!1},i.capTest=function(e){return function(){return!!e}},i}),i(ye,[de,ce,ve],function(e,t,n){return function i(){var i;t.extend(this,{connectRuntime:function(r){function o(t){var s,l;return t.length?(s=t.shift(),(l=n.getConstructor(s))?(i=new l(r),i.bind("Init",function(){i.initialized=!0,setTimeout(function(){i.clients++,a.trigger("RuntimeInit",i)},1)}),i.bind("Error",function(){i.destroy(),o(t)}),i.mode?void i.init():void i.trigger("Error")):void o(t)):(a.trigger("RuntimeError",new e.RuntimeError(e.RuntimeError.NOT_INIT_ERR)),void(i=null))}var a=this,s;if("string"===t.typeOf(r)?s=r:"string"===t.typeOf(r.ruid)&&(s=r.ruid),s){if(i=n.getRuntime(s))return i.clients++,i;throw new e.RuntimeError(e.RuntimeError.NOT_INIT_ERR)}o((r.runtime_order||n.order).split(/\s*,\s*/))},getRuntime:function(){return i&&i.uid?i:(i=null,null)},disconnectRuntime:function(){i&&--i.clients<=0&&(i.destroy(),i=null)}})}}),i(xe,[ce,ye,fe],function(e,t,n){function i(){this.uid=e.guid("uid_"),t.call(this),this.destroy=function(){this.disconnectRuntime(),this.unbindAll()}}return i.prototype=n.instance,i}),i(be,[ce,he,ye],function(e,t,n){function i(o,a){function s(t,n,o){var a,s=r[this.uid];return"string"===e.typeOf(s)&&s.length?(a=new i(null,{type:o,size:n-t}),a.detach(s.substr(t,a.size)),a):null}n.call(this),o&&this.connectRuntime(o),a?"string"===e.typeOf(a)&&(a={data:a}):a={},e.extend(this,{uid:a.uid||e.guid("uid_"),ruid:o,size:a.size||0,type:a.type||"",slice:function(e,t,n){return this.isDetached()?s.apply(this,arguments):this.getRuntime().exec.call(this,"Blob","slice",this.getSource(),e,t,n)},getSource:function(){return r[this.uid]?r[this.uid]:null},detach:function(e){this.ruid&&(this.getRuntime().exec.call(this,"Blob","destroy"),this.disconnectRuntime(),this.ruid=null),e=e||"";var n=e.match(/^data:([^;]*);base64,/);n&&(this.type=n[1],e=t.atob(e.substring(e.indexOf("base64,")+7))),this.size=e.length,r[this.uid]=e},isDetached:function(){return!this.ruid&&"string"===e.typeOf(r[this.uid])},destroy:function(){this.detach(),delete r[this.uid]}}),a.data?this.detach(a.data):r[this.uid]=a}var r={};return i}),i(we,[ce,ye,he],function(e,t,n){return function(){function i(e,t){if(!t.isDetached()){var i=this.connectRuntime(t.ruid).exec.call(this,"FileReaderSync","read",e,t);return this.disconnectRuntime(),i}var r=t.getSource();switch(e){case"readAsBinaryString":return r;case"readAsDataURL":return"data:"+t.type+";base64,"+n.btoa(r);case"readAsText":for(var o="",a=0,s=r.length;s>a;a++)o+=String.fromCharCode(r[a]);return o}}t.call(this),e.extend(this,{uid:e.guid("uid_"),readAsBinaryString:function(e){return i.call(this,"readAsBinaryString",e)},readAsDataURL:function(e){return i.call(this,"readAsDataURL",e)},readAsText:function(e){return i.call(this,"readAsText",e)}})}}),i(_e,[de,ce,be],function(e,t,n){function i(){var e,i=[];t.extend(this,{append:function(r,o){var a=this,s=t.typeOf(o);o instanceof n?e={name:r,value:o}:"array"===s?(r+="[]",t.each(o,function(e){a.append(r,e)})):"object"===s?t.each(o,function(e,t){a.append(r+"["+t+"]",e)}):"null"===s||"undefined"===s||"number"===s&&isNaN(o)?a.append(r,"false"):i.push({name:r,value:o.toString()})},hasBlob:function(){return!!this.getBlob()},getBlob:function(){return e&&e.value||null},getBlobName:function(){return e&&e.name||null},each:function(n){t.each(i,function(e){n(e.value,e.name)}),e&&n(e.value,e.name)},destroy:function(){e=null,i=[]}})}return i}),i(Ee,[ce],function(e){var t={};return{addI18n:function(n){return e.extend(t,n)},translate:function(e){return t[e]||e},_:function(e){return this.translate(e)},sprintf:function(t){var n=[].slice.call(arguments,1);return t.replace(/%[a-z]/g,function(){var t=n.shift();return"undefined"!==e.typeOf(t)?t:""})}}}),i(Re,[ce,Ee],function(e,t){var n="application/msword,doc dot,application/pdf,pdf,application/pgp-signature,pgp,application/postscript,ps ai eps,application/rtf,rtf,application/vnd.ms-excel,xls xlb,application/vnd.ms-powerpoint,ppt pps pot,application/zip,zip,application/x-shockwave-flash,swf swfl,application/vnd.openxmlformats-officedocument.wordprocessingml.document,docx,application/vnd.openxmlformats-officedocument.wordprocessingml.template,dotx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,xlsx,application/vnd.openxmlformats-officedocument.presentationml.presentation,pptx,application/vnd.openxmlformats-officedocument.presentationml.template,potx,application/vnd.openxmlformats-officedocument.presentationml.slideshow,ppsx,application/x-javascript,js,application/json,json,audio/mpeg,mp3 mpga mpega mp2,audio/x-wav,wav,audio/x-m4a,m4a,audio/ogg,oga ogg,audio/aiff,aiff aif,audio/flac,flac,audio/aac,aac,audio/ac3,ac3,audio/x-ms-wma,wma,image/bmp,bmp,image/gif,gif,image/jpeg,jpg jpeg jpe,image/photoshop,psd,image/png,png,image/svg+xml,svg svgz,image/tiff,tiff tif,text/plain,asc txt text diff log,text/html,htm html xhtml,text/css,css,text/csv,csv,text/rtf,rtf,video/mpeg,mpeg mpg mpe m2v,video/quicktime,qt mov,video/mp4,mp4,video/x-m4v,m4v,video/x-flv,flv,video/x-ms-wmv,wmv,video/avi,avi,video/webm,webm,video/3gpp,3gpp 3gp,video/3gpp2,3g2,video/vnd.rn-realvideo,rv,video/ogg,ogv,video/x-matroska,mkv,application/vnd.oasis.opendocument.formula-template,otf,application/octet-stream,exe",i={mimes:{},extensions:{},addMimeType:function(e){var t=e.split(/,/),n,i,r;for(n=0;n<t.length;n+=2){for(r=t[n+1].split(/ /),i=0;i<r.length;i++)this.mimes[r[i]]=t[n];this.extensions[t[n]]=r}},extList2mimes:function(t,n){var i=this,r,o,a,s,l=[];for(o=0;o<t.length;o++)for(r=t[o].extensions.split(/\s*,\s*/),a=0;a<r.length;a++){if("*"===r[a])return[];if(s=i.mimes[r[a]])-1===e.inArray(s,l)&&l.push(s);else{if(!n||!/^\w+$/.test(r[a]))return[];l.push("."+r[a])}}return l},mimes2exts:function(t){var n=this,i=[];return e.each(t,function(t){if("*"===t)return i=[],!1;var r=t.match(/^(\w+)\/(\*|\w+)$/);r&&("*"===r[2]?e.each(n.extensions,function(e,t){new RegExp("^"+r[1]+"/").test(t)&&[].push.apply(i,n.extensions[t])}):n.extensions[t]&&[].push.apply(i,n.extensions[t]))}),i},mimes2extList:function(n){var i=[],r=[];return"string"===e.typeOf(n)&&(n=e.trim(n).split(/\s*,\s*/)),r=this.mimes2exts(n),i.push({title:t.translate("Files"),extensions:r.length?r.join(","):"*"}),i.mimes=n,i},getFileExtension:function(e){var t=e&&e.match(/\.([^.]+)$/);return t?t[1].toLowerCase():""},getFileMime:function(e){return this.mimes[this.getFileExtension(e)]||""}};return i.addMimeType(n),i}),i(Ce,[ce,de,fe,he,pe,ve,xe,be,we,_e,me,Re],function(e,t,n,i,r,o,a,s,l,u,c,d){function f(){this.uid=e.guid("uid_")}function h(){function n(e,t){return y.hasOwnProperty(e)?1===arguments.length?c.can("define_property")?y[e]:v[e]:void(c.can("define_property")?y[e]=t:v[e]=t):void 0}function l(t){function i(){L.destroy(),L=null,s.dispatchEvent("loadend"),s=null}function r(r){L.bind("LoadStart",function(e){n("readyState",h.LOADING),s.dispatchEvent("readystatechange"),s.dispatchEvent(e),k&&s.upload.dispatchEvent(e)}),L.bind("Progress",function(e){n("readyState")!==h.LOADING&&(n("readyState",h.LOADING),s.dispatchEvent("readystatechange")),s.dispatchEvent(e)}),L.bind("UploadProgress",function(e){k&&s.upload.dispatchEvent({type:"progress",lengthComputable:!1,total:e.total,loaded:e.loaded})}),L.bind("Load",function(t){n("readyState",h.DONE),n("status",Number(r.exec.call(L,"XMLHttpRequest","getStatus")||0)),n("statusText",p[n("status")]||""),n("response",r.exec.call(L,"XMLHttpRequest","getResponse",n("responseType"))),~e.inArray(n("responseType"),["text",""])?n("responseText",n("response")):"document"===n("responseType")&&n("responseXML",n("response")),z=r.exec.call(L,"XMLHttpRequest","getAllResponseHeaders"),s.dispatchEvent("readystatechange"),n("status")>0?(k&&s.upload.dispatchEvent(t),s.dispatchEvent(t)):(N=!0,s.dispatchEvent("error")),i()}),L.bind("Abort",function(e){s.dispatchEvent(e),i()}),L.bind("Error",function(e){N=!0,n("readyState",h.DONE),s.dispatchEvent("readystatechange"),D=!0,s.dispatchEvent(e),i()}),r.exec.call(L,"XMLHttpRequest","send",{url:b,method:w,async:x,user:E,password:R,headers:_,mimeType:T,encoding:C,responseType:s.responseType,withCredentials:s.withCredentials,options:H},t)}var s=this;M=(new Date).getTime(),L=new a,"string"==typeof H.required_caps&&(H.required_caps=o.parseCaps(H.required_caps)),H.required_caps=e.extend({},H.required_caps,{return_response_type:s.responseType}),t instanceof u&&(H.required_caps.send_multipart=!0),I||(H.required_caps.do_cors=!0),H.ruid?r(L.connectRuntime(H)):(L.bind("RuntimeInit",function(e,t){r(t)}),L.bind("RuntimeError",function(e,t){s.dispatchEvent("RuntimeError",t)}),L.connectRuntime(H))}function g(){n("responseText",""),n("responseXML",null),n("response",null),n("status",0),n("statusText",""),M=F=null}var v=this,y={timeout:0,readyState:h.UNSENT,withCredentials:!1,status:0,statusText:"",responseType:"",responseXML:null,responseText:null,response:null},x=!0,b,w,_={},E,R,C=null,T=null,S=!1,A=!1,k=!1,D=!1,N=!1,I=!1,M,F,P=null,O=null,H={},L,z="",B;e.extend(this,y,{uid:e.guid("uid_"),upload:new f,open:function(o,a,s,l,u){var c;if(!o||!a)throw new t.DOMException(t.DOMException.SYNTAX_ERR);if(/[\u0100-\uffff]/.test(o)||i.utf8_encode(o)!==o)throw new t.DOMException(t.DOMException.SYNTAX_ERR);if(~e.inArray(o.toUpperCase(),["CONNECT","DELETE","GET","HEAD","OPTIONS","POST","PUT","TRACE","TRACK"])&&(w=o.toUpperCase()),~e.inArray(w,["CONNECT","TRACE","TRACK"]))throw new t.DOMException(t.DOMException.SECURITY_ERR);if(a=i.utf8_encode(a),c=r.parseUrl(a),I=r.hasSameOrigin(c),b=r.resolveUrl(a),(l||u)&&!I)throw new t.DOMException(t.DOMException.INVALID_ACCESS_ERR);if(E=l||c.user,R=u||c.pass,x=s||!0,x===!1&&(n("timeout")||n("withCredentials")||""!==n("responseType")))throw new t.DOMException(t.DOMException.INVALID_ACCESS_ERR);S=!x,A=!1,_={},g.call(this),n("readyState",h.OPENED),this.convertEventPropsToHandlers(["readystatechange"]),this.dispatchEvent("readystatechange")},setRequestHeader:function(r,o){var a=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","content-transfer-encoding","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","user-agent","via"];if(n("readyState")!==h.OPENED||A)throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);if(/[\u0100-\uffff]/.test(r)||i.utf8_encode(r)!==r)throw new t.DOMException(t.DOMException.SYNTAX_ERR);return r=e.trim(r).toLowerCase(),~e.inArray(r,a)||/^(proxy\-|sec\-)/.test(r)?!1:(_[r]?_[r]+=", "+o:_[r]=o,!0)},getAllResponseHeaders:function(){return z||""},getResponseHeader:function(t){return t=t.toLowerCase(),N||~e.inArray(t,["set-cookie","set-cookie2"])?null:z&&""!==z&&(B||(B={},e.each(z.split(/\r\n/),function(t){var n=t.split(/:\s+/);2===n.length&&(n[0]=e.trim(n[0]),B[n[0].toLowerCase()]={header:n[0],value:e.trim(n[1])})})),B.hasOwnProperty(t))?B[t].header+": "+B[t].value:null},overrideMimeType:function(i){var r,o;if(~e.inArray(n("readyState"),[h.LOADING,h.DONE]))throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);if(i=e.trim(i.toLowerCase()),/;/.test(i)&&(r=i.match(/^([^;]+)(?:;\scharset\=)?(.*)$/))&&(i=r[1],r[2]&&(o=r[2])),!d.mimes[i])throw new t.DOMException(t.DOMException.SYNTAX_ERR);P=i,O=o},send:function(n,r){if(H="string"===e.typeOf(r)?{ruid:r}:r?r:{},this.convertEventPropsToHandlers(m),this.upload.convertEventPropsToHandlers(m),this.readyState!==h.OPENED||A)throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);if(n instanceof s)H.ruid=n.ruid,T=n.type||"application/octet-stream";else if(n instanceof u){if(n.hasBlob()){var o=n.getBlob();H.ruid=o.ruid,T=o.type||"application/octet-stream"}}else"string"==typeof n&&(C="UTF-8",T="text/plain;charset=UTF-8",n=i.utf8_encode(n));this.withCredentials||(this.withCredentials=H.required_caps&&H.required_caps.send_browser_cookies&&!I),k=!S&&this.upload.hasEventListener(),N=!1,D=!n,S||(A=!0),l.call(this,n)},abort:function(){if(N=!0,S=!1,~e.inArray(n("readyState"),[h.UNSENT,h.OPENED,h.DONE]))n("readyState",h.UNSENT);else{if(n("readyState",h.DONE),A=!1,!L)throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);L.getRuntime().exec.call(L,"XMLHttpRequest","abort",D),D=!0}},destroy:function(){L&&("function"===e.typeOf(L.destroy)&&L.destroy(),L=null),this.unbindAll(),this.upload&&(this.upload.unbindAll(),this.upload=null)}})}var p={100:"Continue",101:"Switching Protocols",102:"Processing",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",306:"Reserved",307:"Temporary Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Request Entity Too Large",414:"Request-URI Too Long",415:"Unsupported Media Type",416:"Requested Range Not Satisfiable",417:"Expectation Failed",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",426:"Upgrade Required",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",510:"Not Extended"};f.prototype=n.instance;var m=["loadstart","progress","abort","error","load","timeout","loadend"],g=1,v=2;return h.UNSENT=0,h.OPENED=1,h.HEADERS_RECEIVED=2,h.LOADING=3,h.DONE=4,h.prototype=n.instance,h}),i(Te,[K,G,_,T,U,m,C],function(e,t,n,i,r,o,a){function s(e){var t="width=device-width,initial-scale=1.0,user-scalable=0,minimum-scale=1.0,maximum-scale=1.0",n=i("meta[name=viewport]")[0],r;n||(n=document.createElement("meta"),n.setAttribute("name","viewport"),document.getElementsByTagName("head")[0].appendChild(n)),r=n.getAttribute("content"),r&&"undefined"!=typeof d&&(d=r),n.setAttribute("content",e?t:d)}function l(e){for(var t=0;t<c.length;t++)if(c[t]._fullscreen)return;i([document.documentElement,document.body]).removeClass(e+"fullscreen")}function u(){function e(){var e,t=n.getWindowSize(),i;for(e=0;e<c.length;e++)i=c[e].layoutRect(),c[e].moveTo(c[e].settings.x||Math.max(0,t.w/2-i.w/2),c[e].settings.y||Math.max(0,t.h/2-i.h/2))}var t={w:window.innerWidth,h:window.innerHeight};window.setInterval(function(){var e=window.innerWidth,n=window.innerHeight;
(t.w!=e||t.h!=n)&&(t={w:e,h:n},i(window).trigger("resize"))},0),i(window).on("resize",e)}var c=[],d="",f=e.extend({modal:!0,Defaults:{border:1,layout:"flex",containerCls:"panel",role:"dialog",callbacks:{submit:function(){this.fire("submit",{data:this.toJSON()})},close:function(){this.close()}}},init:function(e){var n=this;n._super(e),n.isRtl()&&n.classes.add("rtl"),n.classes.add("window"),n.state.set("fixed",!0),e.buttons&&(n.statusbar=new t({layout:"flex",border:"1 0 0 0",spacing:3,padding:10,align:"center",pack:n.isRtl()?"start":"end",defaults:{type:"button"},items:e.buttons}),n.statusbar.classes.add("foot"),n.statusbar.parent(n)),n.on("click",function(e){-1!=e.target.className.indexOf(n.classPrefix+"close")&&n.close()}),n.on("cancel",function(){n.close()}),n.aria("describedby",n.describedBy||n._id+"-none"),n.aria("label",e.title),n._fullscreen=!1},recalc:function(){var e=this,t=e.statusbar,i,r,o,a;e._fullscreen&&(e.layoutRect(n.getWindowSize()),e.layoutRect().contentH=e.layoutRect().innerH),e._super(),i=e.layoutRect(),e.settings.title&&!e._fullscreen&&(r=i.headerW,r>i.w&&(o=i.x-Math.max(0,r/2),e.layoutRect({w:r,x:o}),a=!0)),t&&(t.layoutRect({w:e.layoutRect().innerW}).recalc(),r=t.layoutRect().minW+i.deltaW,r>i.w&&(o=i.x-Math.max(0,r-i.w),e.layoutRect({w:r,x:o}),a=!0)),a&&e.recalc()},initLayoutRect:function(){var e=this,t=e._super(),i=0,r;if(e.settings.title&&!e._fullscreen){r=e.getEl("head");var o=n.getSize(r);t.headerW=o.width,t.headerH=o.height,i+=t.headerH}e.statusbar&&(i+=e.statusbar.layoutRect().h),t.deltaH+=i,t.minH+=i,t.h+=i;var a=n.getWindowSize();return t.x=e.settings.x||Math.max(0,a.w/2-t.w/2),t.y=e.settings.y||Math.max(0,a.h/2-t.h/2),t},renderHtml:function(){var e=this,t=e._layout,n=e._id,i=e.classPrefix,r=e.settings,o="",a="",s=r.html;return e.preRender(),t.preRender(e),r.title&&(o='<div id="'+n+'-head" class="'+i+'window-head"><div id="'+n+'-title" class="'+i+'title">'+e.encode(r.title)+'</div><button type="button" class="'+i+'close" aria-hidden="true">\xd7</button><div id="'+n+'-dragh" class="'+i+'dragh"></div></div>'),r.url&&(s='<iframe src="'+r.url+'" tabindex="-1"></iframe>'),"undefined"==typeof s&&(s=t.renderHtml(e)),e.statusbar&&(a=e.statusbar.renderHtml()),'<div id="'+n+'" class="'+e.classes+'" hidefocus="1"><div class="'+e.classPrefix+'reset" role="application">'+o+'<div id="'+n+'-body" class="'+e.bodyClasses+'">'+s+"</div>"+a+"</div></div>"},fullscreen:function(e){var t=this,r=document.documentElement,a,s=t.classPrefix,l;if(e!=t._fullscreen)if(i(window).on("resize",function(){var e;if(t._fullscreen)if(a)t._timer||(t._timer=setTimeout(function(){var e=n.getWindowSize();t.moveTo(0,0).resizeTo(e.w,e.h),t._timer=0},50));else{e=(new Date).getTime();var i=n.getWindowSize();t.moveTo(0,0).resizeTo(i.w,i.h),(new Date).getTime()-e>50&&(a=!0)}}),l=t.layoutRect(),t._fullscreen=e,e){t._initial={x:l.x,y:l.y,w:l.w,h:l.h},t.borderBox=o.parseBox("0"),t.getEl("head").style.display="none",l.deltaH-=l.headerH+2,i([r,document.body]).addClass(s+"fullscreen"),t.classes.add("fullscreen");var u=n.getWindowSize();t.moveTo(0,0).resizeTo(u.w,u.h)}else t.borderBox=o.parseBox(t.settings.border),t.getEl("head").style.display="",l.deltaH+=l.headerH,i([r,document.body]).removeClass(s+"fullscreen"),t.classes.remove("fullscreen"),t.moveTo(t._initial.x,t._initial.y).resizeTo(t._initial.w,t._initial.h);return t.reflow()},postRender:function(){var e=this,t;setTimeout(function(){e.classes.add("in")},0),e._super(),e.statusbar&&e.statusbar.postRender(),e.focus(),this.dragHelper=new r(e._id+"-dragh",{start:function(){t={x:e.layoutRect().x,y:e.layoutRect().y}},drag:function(n){e.moveTo(t.x+n.deltaX,t.y+n.deltaY)}}),e.on("submit",function(t){t.isDefaultPrevented()||e.close()}),c.push(e),s(!0)},submit:function(){return this.fire("submit",{data:this.toJSON()})},remove:function(){var e=this,t;for(e.dragHelper.destroy(),e._super(),e.statusbar&&this.statusbar.remove(),t=c.length;t--;)c[t]===e&&c.splice(t,1);s(c.length>0),l(e.classPrefix)},getContentWindow:function(){var e=this.getEl().getElementsByTagName("iframe")[0];return e?e.contentWindow:null}});return a.desktop||u(),f}),i(Se,[Te],function(e){var t=e.extend({init:function(e){e={border:1,padding:20,layout:"flex",pack:"center",align:"center",containerCls:"panel",autoScroll:!0,buttons:{type:"button",text:"Ok",action:"ok"},items:{type:"label",multiline:!0,maxWidth:500,maxHeight:200}},this._super(e)},Statics:{OK:1,OK_CANCEL:2,YES_NO:3,YES_NO_CANCEL:4,msgBox:function(n){function i(e,t,n){return{type:"button",text:e,subtype:n?"primary":"",onClick:function(e){e.control.parents()[1].close(),o(t)}}}var r,o=n.callback||function(){};switch(n.buttons){case t.OK_CANCEL:r=[i("Ok",!0,!0),i("Cancel",!1)];break;case t.YES_NO:case t.YES_NO_CANCEL:r=[i("Yes",1,!0),i("No",0)],n.buttons==t.YES_NO_CANCEL&&r.push(i("Cancel",-1));break;default:r=[i("Ok",!0,!0)]}return new e({padding:20,x:n.x,y:n.y,minWidth:300,minHeight:100,layout:"flex",pack:"center",align:"center",buttons:r,title:n.title,role:"alertdialog",items:{type:"label",multiline:!0,maxWidth:500,maxHeight:200,text:n.text},onPostRender:function(){this.aria("describedby",this.items()[0]._id)},onClose:n.onClose,onCancel:function(){o(!1)}}).renderTo(document.body).reflow()},alert:function(e,n){return"string"==typeof e&&(e={text:e}),e.callback=n,t.msgBox(e)},confirm:function(e,n){return"string"==typeof e&&(e={text:e}),e.callback=n,e.buttons=t.OK_CANCEL,t.msgBox(e)}}});return t}),i(Ae,[ae],function(e){return{map:function(t,n){return new e(function(e){function i(o){n(t[o],function(n){r.push(n),o<t.length-1?i(o+1):e(r)})}var r=[];0===t.length?e(r):i(0)})}}}),i(ke,[],function(){function e(e){for(var t=window,n=e.split(/\//),i=0;i<n.length;++i){if(!t[n[i]])return;t=t[n[i]]}return t}var t=e("moxman/util/I18n");if(t)return t;var n={};return{add:function(e,t){for(var i in t)n[i]=t[i]},translate:function(e,t){if("undefined"==typeof e)return e;if("string"!=typeof e&&"raw"in e)return e.raw;if(e.push){var i=e.slice(1);e=(n[e[0]]||e[0]).replace(/\{([^\}]+)\}/g,function(e,t){return i[t]})}return n[e]||t||e},data:n}}),i(De,[F,Se,c,Ae,ke,C],function(e,t,n,i,r,o){var a={renderConfirmOverwriteDialog:function(t,n,i){function r(e,t){return function(){var i=o.statusbar.find("checkbox").value();n&&(n(e,i),n=null),t||o.close()}}var o=e.create({type:"window",title:"Confirm overwrite",onclose:r("cancel",!0),items:{type:"form",items:{type:"label",text:t,multiline:!0,maxWidth:500,maxHeight:200}},buttons:[i?null:{type:"checkbox",text:"Apply to all"},{type:"spacer",flex:1},{text:"Overwrite",subtype:"primary",autofocus:!0,onclick:r("overwrite")},{text:"Rename",onclick:r("rename")},i?null:{text:"Skip",onclick:r("skip")},{text:"Cancel",onclick:r("cancel")}]});o.renderTo(document.body).reflow()},renderViewAsDialog:function(t,i,r){function a(e){r.close===!1&&e.preventDefault()}var s,l;r=r||{},s=new t({data:{settings:r}}),i=n.extend({type:"window",minWidth:r.width||i.width,minHeight:r.height||i.height,items:s,onclose:a,onsubmit:function(e){e.preventDefault()},onCloseView:function(){this.close()},callbacks:{submit:function(){s.parent().items()[0].submit()},close:function(){s.fire("CloseView")}}},i),"auto"!=i.fullscreen&&(i.y=o.desktop?0:30),l=e.create(i).renderTo(document.body).reflow(),l.on("close",function(e){!e.isDefaultPrevented()&&r.onclose&&r.onclose()}),"auto"!=i.fullscreen||o.desktop||(r.fullscreen=!0),r.fullscreen&&l.fullscreen(!0),s.fire("OpenView")},displayErrors:function(e,t){i.map(e,function(e,t){a.displayError(e,function(){t()})}).then(t)},displayError:function(e,n){var i;"string"==typeof e&&(e={message:e}),i=e.message,i=r.translate(e.args?[].concat(i).concat(e.args):i),"string"==typeof e.data&&(i+="\n"+e.data),t.alert({title:"Error",text:i},n)}};return a}),i(Ne,[x,ae],function(e,t){var n="c"+(new Date).getTime().toString(32),i=0;return e.extend({init:function(e){var t,r;if(e)for(t in this.fields)r=this.fields[t],"undefined"==typeof e[t]&&(e[t]=r.defaultValue);this._super(e),this.id=this.get(this.idAttribute),this.cid=n+i++},idAttribute:"id",isNew:function(){return!this.has(this.idAttribute)},sync:function(e,n){return this.storage?this.storage.sync(e,this,n):new t(function(e){e()})},fetch:function(e){return this.sync("read",e)},save:function(e){return this.sync(this.isNew()?"create":"update",e)},parent:function(){return this._parent},destroy:function(e){var t=this;return this.sync("delete",e).then(function(){t.fire("destroy")})}})}),i(Ie,[x],function(e){function t(e,t){var n;if(t)for(t=t.split(","),n=0;n<t.length;n++)if(t[n].trim()===e.trim())return!0;return!1}var n=e.extend({isDisabled:function(e){return t(e,this.get("general.disabled_tools"))},isHidden:function(e){return t(e,this.get("general.hidden_tools"))}});return n}),i(Me,[Ne,le,ue,Ie],function(e,t,n,i){var r=e.extend({idAttribute:"path",fields:{path:{type:"string"},name:{type:"string"},size:{type:"number"},lastModified:{type:"date"},isFile:{type:"boolean"},canRead:{type:"boolean"},canWrite:{type:"boolean"},canRename:{type:"boolean"},canEdit:{type:"boolean"},canView:{type:"boolean"},canPreview:{type:"boolean"},exists:{type:"boolean"},meta:{type:"object",defaultValue:{}},link:{type:"object"}},sync:function(e){return"delete"==e?t.exec("delete",{paths:[this.get("path")]}):void 0},toJSON:function(){var e={},t,n;for(t in this.fields){e[t]=this.get(t),e.meta={},n=this.get("meta");for(t in n)e.meta[t]=n[t]}return e}});return r.fromJSON=function(e){var t,i=e.attrs,o,a,s;if(i&&(e.isFile="d"!=i[0],e.canRead="-"!=i[1],e.canWrite="-"!=i[2],e.canRename="-"!=i[3],e.canEdit="-"!=i[4],e.canView="-"!=i[5],e.canPreview="-"!=i[6]),t=e.name||n.basename(e.path),t=t.replace(/_\$\$\[([^\]]+)\]$/,""),"modified"in e&&(e.lastModified=e.modified),o={path:e.path,name:t,size:e.size,lastModified:new Date(1e3*e.lastModified),isFile:e.isFile,canRead:e.canRead,canWrite:e.canWrite,canRename:e.canRename,canEdit:e.canEdit,canView:e.canView,canPreview:e.canPreview,exists:e.exists,meta:e.meta,link:null},e.info&&e.info.link){a={};for(s in o)a[s]=o[s];a.path=o.meta.link,o.link=new r(a)}return new r(o)},r.createDirectory=function(e,n){return t.exec("createDirectory",{path:e,template:n}).then(function(e){return r.fromJSON(e)})},r.createDocument=function(e,i,o){return t.exec("createDocument",{path:n.dirname(e),name:n.basename(e),template:i,fields:o}).then(function(e){return r.fromJSON(e)})},r.putContents=function(e,n){return t.exec("putFileContents",{path:e,content:n}).then(function(e){return r.fromJSON(e)})},r.getContents=function(e){return t.exec("getFileContents",{path:e}).then(function(e){return e.content})},r.moveTo=function(e,n,i){return t.exec("moveTo",{from:e,to:n,resolution:i}).then(function(e){return r.fromJSON(e)})},r.copyTo=function(e,n,i){return t.exec("copyTo",{from:e,to:n,resolution:i}).then(function(e){return r.fromJSON(e)})},r.remove=function(e){return t.exec("delete",{paths:[e]}).then(function(e){return r.fromJSON(e)})},r.getConfig=function(e){return t.exec("getConfig",{path:e}).then(function(e){return new i(e)})},r}),i(Fe,[y,d],function(e,t){var n=Array.prototype.push,i=Array.prototype.slice,r=Array.prototype.splice,o=t.extend({Mixins:[e],length:0,init:function(e){e&&this.push.apply(this,e)},push:function(){var e,t=this.length;return e=Array.prototype.slice.call(arguments),n.apply(this,e),this.fire("add",{items:e,index:t}),e.length},pop:function(){return this.splice(this.length-1,1)[0]},slice:function(e,t){return i.call(this,e,t)},splice:function(e){var t,n,o=i.call(arguments);return 1===o.length&&(o[1]=this.length),n=r.apply(this,o),t=o.slice(2),n.length>0&&this.fire("remove",{items:n,index:e}),t.length>0&&this.fire("add",{items:t,index:e}),n},shift:function(){return this.splice(0,1)[0]},unshift:function(){var e=i.call(arguments);return this.splice.apply(this,[0,0].concat(e)),e.length},forEach:function(e,t){var n;for(t=t||this,n=0;n<this.length;n++)e.call(t,this[n],n,this)},indexOf:function(e){for(var t=0;t<this.length;t++)if(this[t]===e)return t;return-1},filter:function(e,t){var n=this,i=new o;return this.forEach(function(r,o){e.call(t||n,r,o,n)&&i.push(r)}),i}});return o}),i(Pe,[Fe,Ne],function(e,t){return e.extend({offset:0,pageSize:1/0,complete:!0,init:function(e){var n=this,i={};n._idLookup=i,n.on("add",function(e){var r=e.items,o,a,s=e.index;for(a=0;a<r.length;a++)o=r[a],o instanceof t||(o=e.items[a]=new n.model(o),n[s]=o),o._parent=n,o.id&&i[o.id]?(e.stopImmediatePropagation(),Array.prototype.splice.call(n,s,1)):(s++,i[o.id]=i[o.cid]=o)}).on("remove",function(e){for(var t=e.items,n=t.length;n--;)delete i[t[n].id],delete i[t[n].cid]}),n._super(e)},add:function(e){return"length"in e?this.push.apply(this,e):this.push.apply(this,[e]),this},remove:function(e){var t=this.length,n,i;for(i=e.id||e.cid;t--&&(n=this[t].id||this[t].cid,n!==i););return-1!==t?(this.splice(t,1),!0):!1},get:function(e){return e instanceof t?this.get(e.id):this._idLookup[e]},reset:function(){return this.splice(0),this},sort:function(){},sync:function(){},fetch:function(e){return this.sync("read",e)},parent:function(){return this._parent}})}),i(Oe,[Pe,Me,le,ue,ae,C],function(e,t,n,i,r,o){var a=e.extend({model:t,pageSize:200,sortBy:"name",sortOrder:"asc",init:function(e){this._super(e),this._loading=0,this._loadingId=0},sort:function(e,t){this.sortBy=e,this.sortOrder=t,this.fire("sort")},reset:function(){return this.splice(0),this.offset=0,this.complete=!1,this},insert:function(e){function t(e){return e instanceof Date?e.getTime():"string"==typeof e?e.toLowerCase():e}var n=0,i,r,o,a,s,l;for(e instanceof this.model||(e=new this.model(e)),a=t(e.get(this.sortBy)),l="desc"==this.sortOrder,o=0;o<this.length;o++)if(this[o].get("isFile")){n=o;break}for(0===n&&o>0&&(n=this.length),e.get("isFile")?(i=n,r=this.length):(i=0,r=Math.min(this.length,n)),0===o&&this[0]&&".."===this[0].get("name")&&(o=1),o=i;r>o&&(s=t(this[o].get(this.sortBy)),!(l&&a>s||!l&&s>a));o++);(this.complete||o!==this.length)&&this.splice(o,0,e)},sync:function(e,o){function a(e){var t,n,i=e.columns,r=e.data,o,a=[];for(t=0;t<r.length;t++){for(o={},n=0;n<i.length;n++)o[i[n]]=r[t][n];a.push(o)}return a}function s(){l._loading--,0===l._loading&&l.fire("loading",{state:!1})}var l=this,u;return l.complete?new r(function(){}):(o=o||l.lastArgs||{},o.offset=l.offset,o.length=l.pageSize,o.orderBy=l.sortBy,o.desc="desc"==l.sortOrder,l.lastArgs&&o.path==l.lastArgs.path&&o.url==l.lastArgs.url||l._loadingId++,u=l._loadingId,l.lastArgs=o,l._loading++,1===l._loading&&l.fire("loading",{state:!0}),"read"==e?new r(function(e,r){n.exec("listFiles",o).then(function(n){var r,o,c;if(s(),u==l._loadingId){for(c=a(n),l.file=t.fromJSON(n.file),l.urlFile=n.urlFile?t.fromJSON(n.urlFile):null,l.config=n.config,l.complete=n.last,l.offset+=l.pageSize,r=0;r<c.length;r++)o=c[r],o.meta=o.meta||{},o.path=i.join(n.file.path,o.name),o.meta.link=o.info.link,o.exists=!0,"-"!=o.attrs[7]&&n.url&&!o.meta.thumb_url&&(o.meta.thumb_url=i.join(i.join(n.url,n.thumbnailFolder),n.thumbnailPrefix+o.name)),c[r]=t.fromJSON(o);l.add(c),e(l)}},function(e){s(),r(e)})["catch"](function(e){s(),r(e)})}):void 0)},next:function(e){return this.fetch(e||this.lastArgs)},destroy:function(){var e=this;return new r(function(t,n){var i=e.length;e.forEach(function(r){r.destroy().then(function(){i--,0===i&&t(e)},n)})})},populateMeta:function(e){var t=this;return e=e||{},n.exec("FileInfo",{paths:t.toPathArray(),meta:!0,insert:!0,create:e.create}).then(function(e){return t.forEach(function(t,n){var r,o=e[n];for(r in o)t.set(r,o[r]);t.set("name",i.basename(o.path))}),t})},getDownloadUrl:function(e){var t=this,n=[],r,a;return 0===this.length?"":(a=o.apiPageUrl+"?action=download",this.length>1?(e=e||"files.zip",t.forEach(function(e){r=i.dirname(e.get("path")),n.push(e.get("name"))}),a+="&path="+encodeURIComponent(r)+"&names="+encodeURIComponent(n.join("/"))+"&zipname="+encodeURIComponent(e)):a+="&path="+encodeURIComponent(this[0].get("path")),a)},zip:function(e){var r=[],o;return this.forEach(function(e){o=i.dirname(e.get("path")),r.push(e.get("name"))}),n.exec("zip",{to:e,path:o,names:r}).then(function(e){return t.fromJSON(e)})},unzip:function(e){return n.exec("unzip",{from:this[0].get("path"),to:e}).then(function(e){return a.fromJSON(e)})},toPathArray:function(){var e=[];return this.forEach(function(t){e.push(t.get("path"))}),e},toJSON:function(){var e=[];return this.forEach(function(t){e.push(t.toJSON())}),e}});return a.getFiles=function(e){return n.exec("FileInfo",{paths:e}).then(function(e){return a.fromJSON(e)})},a.fromJSON=function(e){var n,i;for(i=new a,n=0;n<e.length;n++)i.add(t.fromJSON(e[n]));return i},a}),i(He,[ue,ae,De,Me,Oe],function(e,t,n,i,r){return{confirmOverwrite:function(i,o){return new t(function(t){function a(){function e(e,n){"cancel"!=e&&("skip"!=e&&u.push({file:n,type:e}),0===c.length?u.length>0&&t(u):a())}var i;return i=c.shift(),l?void e(l,i):void(i.get("exists")?n.renderConfirmOverwriteDialog(['The "{0}" file/directory already exists. Are you sure you want to overwrite this file?',i.get("name")],function(t,n){n&&(l=t),e(t,i)},1===d):e("default",i))}var s,l,u=[],c=new r,d=i.length;if(!i.length)return void t(u);for(s=0;s<i.length;s++)c.push({path:e.join(o,e.basename(i[s]))});c.populateMeta({create:!0}).then(function(){a()})})}}}),i(Le,[k,T,U,c,ie,le,ue,C,re,Ce,_e,be,he,me,fe,se,De,He],function(e,t,n,i,r,o,a,s,l,u,c,d,f,h,p,m,g,v){function y(e,t,n){return e>n?e=n:t>e&&(e=t),e}function x(){}var b=Math.floor,w={n:[.5,0,0,-1,0,1],e:[1,.5,1,0,0,0],s:[.5,1,0,1,0,0],w:[0,.5,-1,0,1,0],nw:[0,0,-1,-1,1,1],ne:[1,0,1,-1,0,1],se:[1,1,1,1,0,0],sw:[0,1,-1,1,1,0]};return e.extend({init:function(e){var t=this;t._super(e),t.classes.add("imagecanvas"),t._mirror=t._rotation=0},applyAction:x,endAction:x,renderHtml:function(){var e=this,t=e._id,n=e.classPrefix;return'<div id="'+t+'" class="'+e.classes+'"><div id="'+t+'-clip" class="'+n+'imagecanvas-clip"></div></div>'},filter:function(e){var t=this;return t._currentFilter=t._currentFilter||t._imageEditor.filter(e),t._currentFilter},revert:function(){var e=this;e._imageEditor.hasCanvasSupport()?e._imageEditor.revert():e.loadFromPath(e.path,function(){e.tempname=null,e.zoom("fit"),e.fire("change")})},undo:function(){this._imageEditor.undo()},redo:function(){this._imageEditor.redo()},canUndo:function(){return this._imageEditor.canUndo()||!!this.tempname},canRedo:function(){return this._imageEditor.canRedo()},zoom:function(e){var n=this,i,r,o,a,s,l,u,c,d=20;return"undefined"!=typeof e?.1>e?n:(o=n.getEl(),a=o.offsetWidth-2,s=o.offsetHeight-2,l=n.size(),"fit"==e?(e=Math.min((a-d)/l.w,(s-d)/l.h),e>=1&&(e=1),n._autoFit=!0):n._autoFit=!1,n._zoom=e,i=b(l.w*e),r=b(l.h*e),n._zoomPos=u=n._zoomPos||{},u.x=a>i?b(a/2-i/2):0,u.y=s>r?b(s/2-r/2):0,u.w=i,u.h=r,c=n.getCanvas(),c&&t(c).css({left:u.x,top:u.y,width:i,height:r}),t(n.getEl("clip")).css({left:u.x,top:u.y,width:i,height:r}),t("#"+n._id+"-img").css({width:i,height:r}),n.repaintCropRect(!0),n):n._zoom},startTransation:function(){this._imageEditor.hasCanvasSupport()&&(this.inTransact=!0,this._imageEditor.addUndoLevel(),this._imageEditor.setUndoEnabled(!1))},apply:function(){var e=this;e._currentFilter&&(e._currentFilter.save(),e._currentFilter=null),e.inTransact=!1,e._imageEditor.setUndoEnabled(!0),e.applyAction(),e.applyAction=x},cancel:function(){var e=this;e._currentFilter&&(e._currentFilter.cancel(),e._currentFilter=null),e.inTransact&&(e._imageEditor.undo(!0),e.inTransact=!1,e._imageEditor.setUndoEnabled(!0)),e.hasCanvasSupport()||(e._mirror=e._rotation=0,e.updateOldIE()),e.endAction(),e.applyAction=x},resize:function(e,t){var n=this;n.hasCanvasSupport()?n._imageEditor.resize({w:e,h:t}):o.exec("alterimage",{action:"alter",resize:{w:e,h:t},path:n.path,tempname:n.tempname},function(e){n.loadFromUrl(s.apiPageUrl+"?action=streamfile&stream=true&path="+encodeURIComponent(n.path)+"&tempname=true&r="+(new Date).getTime(),function(){n.zoom("fit"),n.tempname=e.tempname,n.fire("change")})})},flip:function(e){var t=this;t.hasCanvasSupport()?t._imageEditor.flip(e):(t._mirror=t._mirror?0:1,"v"==e&&(t._rotation=(t._rotation+180)%360),t.updateOldIE()),t.applyAction=function(){var e=t._imageEditor;e.hasCanvasSupport()||o.exec("alterimage",{action:"alter",rotate:t._rotation,flip:t._mirror?"h":null,path:t.path,tempname:t.tempname},function(e){t.loadFromUrl(s.apiPageUrl+"?action=streamfile&stream=true&path="+encodeURIComponent(t.path)+"&tempname=true&r="+(new Date).getTime(),function(){t.zoom("fit"),t.tempname=e.tempname,t.fire("change")})})}},rotate:function(e){var t=this;t.hasCanvasSupport()?t._imageEditor.rotate(e):(t._rotation=(t._rotation+(0>e?360+e:e))%360,t.updateOldIE()),t.applyAction=function(){var e=t._imageEditor;e.hasCanvasSupport()||o.exec("alterimage",{action:"alter",rotate:t._rotation,flip:t._mirror?"h":null,path:t.path,tempname:t.tempname},function(e){t.loadFromUrl(s.apiPageUrl+"?action=streamfile&stream=true&path="+encodeURIComponent(t.path)+"&tempname=true&r="+(new Date).getTime(),function(){t.zoom("fit"),t.tempname=e.tempname,t.fire("change")})})}},updateOldIE:function(){var e=this,n=t("#"+e._id+"-img")[0];n.style.filter="progid:DXImageTransform.Microsoft.BasicImage(rotation="+e._rotation/90+",mirror="+e._mirror+");",n.parentNode.style.width=n.offsetWidth+"px",n.parentNode.style.height=n.offsetHeight+"px"},crop:function(e,r,a,l){function u(){var e,n="",r=d._id,o=d.classPrefix;for(e in w)n+='<div id="'+r+"-"+e+'" class="'+o+'imagecanvas-handle" style="cursor:'+e+'-resize"></div>';i.each("top right bottom left middle".split(" "),function(e){n+='<div id="'+r+"-"+e+'" class="moxman-imagecanvas-ants moxman-imagecanvas-'+e+'"></div>'}),t(d.getEl()).append('<div id="'+r+'-overlay" class="moxman-imagecanvas-overlay">'+n+"</div>");var a=d.getEl("img").cloneNode(!0);a.id=r+"-mimg",d.getEl("middle").appendChild(a)}function c(){function e(e){var i=d.getEl(e);d.dragHelper=new n(d._id,{handle:i.id,start:function(e){m=d.zoom(),f=d._cropRect,r=e.screenX,o=e.screenY,a=f.x,s=f.y,l=f.w,u=f.h;for(var n in w)if(d._id+"-"+n==e.target.id){c=w[n],p=d.getEl(n);break}t(p).addClass(d.classPrefix+"imagecanvas-handle-selected")},drag:function(e){var t,n,i,h,p;i=h=0,t=b((e.screenX-r)/m),n=b((e.screenY-o)/m),f.x=t*c[4]+a,f.y=n*c[5]+s,f.x<0&&(i=f.x,f.x=0),f.y<0&&(h=f.y,f.y=0),f.w=t*c[2]+l+i,f.h=n*c[3]+u+h,p=d.size(),f.x+f.w>p.w&&(f.w=p.w-f.x,f.x=p.w-f.w),f.y+f.h>p.h&&(f.h=p.h-f.y,f.y=p.h-f.h),f.x=y(f.x,0,a+l),f.y=y(f.y,0,u+s),f.w=y(f.w,0,p.w),f.h=y(f.h,0,p.h),d.repaintCropRect()},stop:function(){t(p).removeClass(d.classPrefix+"imagecanvas-handle-selected")}})}var i,r,o,a,s,l,u,c,p,m;d.dragHelper=new n(d._id,{handle:d._id+"-mimg",start:function(e){f=d._cropRect,m=d.zoom(),r=e.screenX,o=e.screenY,a=f.x,s=f.y},drag:function(e){var t,n;t=b((e.screenX-r)/m),n=b((e.screenY-o)/m),f.x=t+a,f.y=n+s,f.x=y(f.x,0,h.w-f.w),f.y=y(f.y,0,h.h-f.h),d.repaintCropRect()}});for(i in w)e(i)}var d=this,f=d._cropRect,h=d.size();d._cropRect=f={x:e||0,y:r||0,w:a||h.w,h:l||h.h},f.w=y(f.w,0,h.w),f.h=y(f.h,0,h.h),f.x=y(f.x,0,h.w-f.w),f.y=y(f.y,0,h.h-f.h),d._cropInit?d.getEl("mimg").src=d.getEl("img").src:(d._cropInit=!0,u(),c()),d.classes.add("imagecanvas-crop"),d.repaintCropRect(!0),d.applyAction=function(){var e=d._imageEditor;e.hasCanvasSupport()?e.crop(d._cropRect):o.exec("alterimage",{action:"alter",crop:d._cropRect,path:d.path,tempname:d.tempname},function(e){d.loadFromUrl(s.apiPageUrl+"?action=streamfile&stream=true&path="+encodeURIComponent(d.path)+"&tempname=true&r="+(new Date).getTime(),function(){d.zoom("fit"),d.tempname=e.tempname,d.fire("change")})})},d.endAction=function(){d.classes.remove("imagecanvas-crop")}},repaintCropRect:function(e){function n(e,n){t(i.getEl(e)).css({left:n.x,top:n.y,width:n.w,height:n.h})}var i=this,r=i._cropRect,o,a,s,l,u=i.zoom(),c=i._zoomPos;if(r){o=b(r.x*u),a=b(r.y*u),s=b(r.w*u),l=b(r.h*u);for(var d in w){var f=w[d];n(d,{x:s*f[0]+o+c.x,y:l*f[1]+a+c.y})}if(e){n("clip",{x:c.x,y:c.y});var h=i.size();n("mimg",{w:b(h.w*u),h:b(h.h*u)}),n("img",{w:b(h.w*u),h:b(h.h*u)})}n("mimg",{x:-o,y:-a}),n("middle",{x:o+c.x,y:a+c.y,w:s,h:l}),n("top",{x:o+c.x,y:a+c.y,w:s}),n("right",{x:o+s+c.x,y:a+c.y,h:l}),n("bottom",{x:o+c.x,y:a+l+c.y,w:s}),n("left",{x:o+c.x,y:a+c.y,h:l}),i.fire("cropchange",{rect:r})}},loadFromPath:function(e,t){this.path=e,this.loadFromUrl(s.apiPageUrl+"?action=streamfile&stream=true&path="+encodeURIComponent(e)+"&r="+(new Date).getTime(),t)},loadFromUrl:function(e,n){var i=this,r,o=i.getEl("clip"),a=t("#"+i._id+"-img")[0];r=a||new Image,r.onload=function(){r.onload=x,i.size({w:r.naturalWidth||r.width||r.clientWidth,h:r.naturalHeight||r.height||r.clientHeight}),o.style.visibility="",n.call(i,r)},r.src=e,r.id=i._id+"-img",r.style.cssText="",o.style.visibility="hidden",a||o.appendChild(r)},saveAs:function(e,t,n,i){function r(e,i){m.request().then(function(r){var o=new u,c=s.apiPageUrl;c+="?action=upload&path="+encodeURIComponent(a.dirname(y.path)),c+="&name="+encodeURIComponent(e)+"&resolution="+i+"&csrf="+encodeURIComponent(r.token),o.open("POST",c),o.onload=function(){var e=l.parse(o.responseText);y.fire("change"),e.error?n(e.error):t(e.result)},o.send(b)},function(e){g.displayError(e.message)})}var y=this;if(y._imageEditor.hasCanvasSupport()){var x=y._imageEditor.getCanvas().toDataURL(/\.png$/i.test(e)?"image/png":"image/jpeg",.8);x=f.atob(x.substr(x.indexOf(",")+1)),h.swf_url=s.baseUrl+"/js/Moxie.swf",h.global_event_dispatcher="moxman.dispatchEvent",window.moxman.dispatchEvent=p.instance.dispatchEvent;var b=new c;if(b.append("file",new d(null,x)),"overwrite"==i)return void r(e,"overwrite");v.confirmOverwrite([e],a.dirname(y.path)).then(function(e){r(e[0].file.get("name"),e[0].type)})}else{var w=a.join(a.dirname(y.path),e);o.exec("alterimage",{action:"save",path:w,tempname:y.tempname},function(e){y.loadFromUrl(s.apiPageUrl+"?action=streamfile&stream=true&path="+encodeURIComponent(w)+"&tempname=true&r="+(new Date).getTime(),function(){y.zoom("fit"),y.tempname=e.tempname,y.fire("change"),t(e)})})}},size:function(e){var t=this;return e?(t._size=e,t):t._size},getCanvas:function(){var e=this._imageEditor;return e?e.getCanvas():null},postRender:function(){var e=this;e._imageEditor=new r({viewport:e._id+"-clip",image:e._id+"-img",canEdit:e.settings.canEdit,onchange:function(){e.size(e._imageEditor.getSize()),e.zoom(e._autoFit?"fit":e.zoom()),e.fire("change")}}),e._super()},hasCanvasSupport:function(){return this._imageEditor.hasCanvasSupport()},hasWebGlSupport:function(){return this._imageEditor.hasWebGlSupport()}})}),i(ze,[I,_],function(e,t){return e.extend({init:function(e){var t=this;t._super(e),t.classes.add("widget").add("label"),t.canFocus=!1,e.multiline&&t.classes.add("autoscroll"),e.strong&&t.classes.add("strong")},initLayoutRect:function(){var e=this,n=e._super();if(e.settings.multiline){var i=t.getSize(e.getEl());i.width>n.maxW&&(n.minW=n.maxW,e.classes.add("multiline")),e.getEl().style.width=n.minW+"px",n.startMinH=n.h=n.minH=Math.min(n.maxH,t.getSize(e.getEl()).height)}return n},repaint:function(){var e=this;return e.settings.multiline||(e.getEl().style.lineHeight=e.layoutRect().h+"px"),e._super()},renderHtml:function(){var e=this,t=e.settings.forId;return'<label id="'+e._id+'" class="'+e.classes+'"'+(t?' for="'+t+'"':"")+">"+e.encode(e.state.get("text"))+"</label>"},bindStates:function(){var e=this;return e.state.on("change:text",function(t){e.innerHtml(e.encode(t.value))}),e._super()}})}),i(Be,[O],function(e){return e.extend({Defaults:{role:"toolbar",layout:"flow"},init:function(e){var t=this;t._super(e),t.classes.add("toolbar")},postRender:function(){var e=this;return e.items().each(function(e){e.classes.add("toolbar-item")}),e._super()}})}),i(We,[Be],function(e){return e.extend({Defaults:{role:"menubar",containerCls:"menubar",ariaRoot:!0,defaults:{type:"menubutton"}}})}),i(Ue,[M,F,We],function(e,t,n){function i(e,t){for(;e;){if(t===e)return!0;e=e.parentNode}return!1}var r=e.extend({init:function(e){var t=this;t._renderOpen=!0,t._super(e),e=t.settings,t.classes.add("menubtn"),e.fixedWidth&&t.classes.add("fixed-width"),t.aria("haspopup",!0),t.state.set("menu",e.menu||t.render())},showMenu:function(){var e=this,n;return e.menu&&e.menu.visible()?e.hideMenu():(e.menu||(n=e.state.get("menu")||[],n.length?n={type:"menu",items:n}:n.type=n.type||"menu",n.renderTo?e.menu=n.parent(e).show().renderTo():e.menu=t.create(n).parent(e).renderTo(),e.fire("createmenu"),e.menu.reflow(),e.menu.on("cancel",function(t){t.control.parent()===e.menu&&(t.stopPropagation(),e.focus(),e.hideMenu())}),e.menu.on("select",function(){e.focus()}),e.menu.on("show hide",function(t){t.control==e.menu&&e.activeMenu("show"==t.type),e.aria("expanded","show"==t.type)}).fire("show")),e.menu.show(),e.menu.layoutRect({w:e.layoutRect().w}),void e.menu.moveRel(e.getEl(),e.isRtl()?["br-tr","tr-br"]:["bl-tl","tl-bl"]))},hideMenu:function(){var e=this;e.menu&&(e.menu.items().each(function(e){e.hideMenu&&e.hideMenu()}),e.menu.hide())},activeMenu:function(e){this.classes.toggle("active",e)},renderHtml:function(){var e=this,t=e._id,i=e.classPrefix,r=e.settings.icon,o,a=e.state.get("text");return o=e.settings.image,o?(r="none","string"!=typeof o&&(o=window.getSelection?o[0]:o[1]),o=" style=\"background-image: url('"+o+"')\""):o="",r=e.settings.icon?i+"ico "+i+"i-"+r:"",e.aria("role",e.parent()instanceof n?"menuitem":"button"),'<div id="'+t+'" class="'+e.classes+'" tabindex="-1" aria-labelledby="'+t+'"><button id="'+t+'-open" role="presentation" type="button" tabindex="-1">'+(r?'<i class="'+r+'"'+o+"></i>":"")+(a?(r?"\xa0":"")+e.encode(a):"")+' <i class="'+i+'caret"></i></button></div>'},postRender:function(){var e=this;return e.on("click",function(t){t.control===e&&i(t.target,e.getEl())&&(e.showMenu(),t.aria&&e.menu.items()[0].focus())}),e.on("mouseenter",function(t){var n=t.control,i=e.parent(),o;n&&i&&n instanceof r&&n.parent()==i&&(i.items().filter("MenuButton").each(function(e){e.hideMenu&&e!=n&&(e.menu&&e.menu.visible()&&(o=!0),e.hideMenu())}),o&&(n.focus(),n.showMenu()))}),e._super()},bindStates:function(){var e=this;return e.state.on("change:menu",function(){e.menu&&e.menu.remove(),e.menu=null}),e._super()},remove:function(){this._super(),this.menu&&this.menu.remove()}});return r}),i(je,[I,F,C],function(e,t,n){return e.extend({Defaults:{border:0,role:"menuitem"},init:function(e){var t=this,n;t._super(e),e=t.settings,t.classes.add("menu-item"),e.menu&&t.classes.add("menu-item-expand"),e.preview&&t.classes.add("menu-item-preview"),n=t.state.get("text"),("-"===n||"|"===n)&&(t.classes.add("menu-item-sep"),t.aria("role","separator"),t.state.set("text","-")),e.selectable&&(t.aria("role","menuitemcheckbox"),t.classes.add("menu-item-checkbox"),e.icon="selected"),e.preview||e.selectable||t.classes.add("menu-item-normal"),t.on("mousedown",function(e){e.preventDefault()}),e.menu&&!e.ariaHideMenu&&t.aria("haspopup",!0)},hasMenus:function(){return!!this.settings.menu},showMenu:function(){var e=this,n=e.settings,i,r=e.parent();if(r.items().each(function(t){t!==e&&t.hideMenu()}),n.menu){i=e.menu,i?i.show():(i=n.menu,i.length?i={type:"menu",items:i}:i.type=i.type||"menu",r.settings.itemDefaults&&(i.itemDefaults=r.settings.itemDefaults),i=e.menu=t.create(i).parent(e).renderTo(),i.reflow(),i.on("cancel",function(t){t.stopPropagation(),e.focus(),i.hide()}),i.on("show hide",function(e){e.control.items().each(function(e){e.active(e.settings.selected)})}).fire("show"),i.on("hide",function(t){t.control===i&&e.classes.remove("selected")}),i.submenu=!0),i._parentMenu=r,i.classes.add("menu-sub");var o=i.testMoveRel(e.getEl(),e.isRtl()?["tl-tr","bl-br","tr-tl","br-bl"]:["tr-tl","br-bl","tl-tr","bl-br"]);i.moveRel(e.getEl(),o),i.rel=o,o="menu-sub-"+o,i.classes.remove(i._lastRel).add(o),i._lastRel=o,e.classes.add("selected"),e.aria("expanded",!0)}},hideMenu:function(){var e=this;return e.menu&&(e.menu.items().each(function(e){e.hideMenu&&e.hideMenu()}),e.menu.hide(),e.aria("expanded",!1)),e},renderHtml:function(){function e(e){var t,i,r={};for(r=n.mac?{alt:"&#x2325;",ctrl:"&#x2318;",shift:"&#x21E7;",meta:"&#x2318;"}:{meta:"Ctrl"},e=e.split("+"),
t=0;t<e.length;t++)i=r[e[t].toLowerCase()],i&&(e[t]=i);return e.join("+")}var t=this,i=t._id,r=t.settings,o=t.classPrefix,a=t.encode(t.state.get("text")),s=t.settings.icon,l="",u=r.shortcut;return s&&t.parent().classes.add("menu-has-icons"),r.image&&(s="none",l=" style=\"background-image: url('"+r.image+"')\""),u&&(u=e(u)),s=o+"ico "+o+"i-"+(t.settings.icon||"none"),'<div id="'+i+'" class="'+t.classes+'" tabindex="-1">'+("-"!==a?'<i class="'+s+'"'+l+"></i>\xa0":"")+("-"!==a?'<span id="'+i+'-text" class="'+o+'text">'+a+"</span>":"")+(u?'<div id="'+i+'-shortcut" class="'+o+'menu-shortcut">'+u+"</div>":"")+(r.menu?'<div class="'+o+'caret"></div>':"")+"</div>"},postRender:function(){var e=this,t=e.settings,n=t.textStyle;if("function"==typeof n&&(n=n.call(this)),n){var i=e.getEl("text");i&&i.setAttribute("style",n)}return e.on("mouseenter click",function(n){n.control===e&&(t.menu||"click"!==n.type?(e.showMenu(),n.aria&&e.menu.focus(!0)):(e.fire("select"),e.parent().hideAll()))}),e._super(),e},active:function(e){return"undefined"!=typeof e&&this.aria("checked",e),this._super(e)},remove:function(){this._super(),this.menu&&this.menu.remove()}})}),i(Ve,[K,je,c],function(e,t,n){var i=e.extend({Defaults:{defaultType:"menuitem",border:1,layout:"stack",role:"application",bodyRole:"menu",ariaRoot:!0},init:function(e){var t=this;if(e.autohide=!0,e.constrainToViewport=!0,e.itemDefaults)for(var i=e.items,r=i.length;r--;)i[r]=n.extend({},e.itemDefaults,i[r]);t._super(e),t.classes.add("menu")},repaint:function(){return this.classes.toggle("menu-align",!0),this._super(),this.getEl().style.height="",this.getEl("body").style.height="",this},cancel:function(){var e=this;e.hideAll(),e.fire("select")},hideAll:function(){var e=this;return this.find("menuitem").exec("hideMenu"),e._super()},preRender:function(){var e=this;return e.items().each(function(t){var n=t.settings;return n.icon||n.selectable?(e._hasIcons=!0,!1):void 0}),e._super()}});return i}),i(qe,[Ue,Ve],function(e,t){return e.extend({init:function(e){function t(i){for(var a=0;a<i.length;a++){if(r=i[a].selected||e.value===i[a].value){o=o||i[a].text,n.state.set("value",i[a].value);break}i[a].menu&&t(i[a].menu)}}var n=this,i,r,o,a;n._super(e),e=n.settings,n._values=i=e.values,i&&("undefined"!=typeof e.value&&t(i),!r&&i.length>0&&(o=i[0].text,n.state.set("value",i[0].value)),n.state.set("menu",i)),n.state.set("text",e.text||o||i[0].text),n.classes.add("listbox"),n.on("select",function(t){var i=t.control;a&&(t.lastControl=a),e.multiple?i.active(!i.active()):n.value(t.control.value()),a=i})},bindStates:function(){function e(e,n){e instanceof t&&e.items().each(function(e){e.active(e.value()===n)})}function n(e,t){var i;if(e)for(var r=0;r<e.length;r++){if(e[r].value==t)return e[r];if(e[r].menu&&(i=n(e[r].menu)))return i}}var i=this;return i.on("show",function(t){e(t.control,i.value())}),i.state.on("change:value",function(e){var t=n(i.state.get("menu"),e.value);i.text(t?t.text:i.settings.text)}),i._super()}})}),i(Xe,[T,d,y,ke],function(e,t,n,i){function r(t){var n,i,r=0,o=e(t.notifier.elm).children();for(n=o.length-1;n>=0;n--)r+=t.notifier.spacing,i=o[n].getBoundingClientRect(),o.eq(n).css({bottom:r}),r+=i.bottom-i.top}var o=0,a="moxman-",s=t.extend({Mixins:[n],init:function(e){this.notifier=e,this._message="",this._fadeout=5e3},fadeout:function(e){this._fadeout=e},message:function(t){return t=i.translate(t),this._rendered?e("#"+this.id+" > div:nth-child(1)").text(t):this._message=t,this},percent:function(t){var n=this,i;return n._rendered?(i=e("#"+n.id+" div:nth-child(2)").children(),i.eq(0).text(Math.round(t)+"%"),i.eq(1).css("width",t+"%")):n._percent=t,100===t&&(n.timeout=setTimeout(function(){n.close()},n._fadeout)),n},render:function(){var t=this,n="",s;return this.id="mcenot"+o++,"number"==typeof t._percent&&(n='<div class="'+a+'progress"><div class="'+a+'text">0%</div><div class="'+a+'bar"></div></div>'),s=e('<div id="'+t.id+'" class="'+a+'notification" style="visibility: hidden"><div class="'+a+'label">'+i.translate(this._message)+"</div>"+n+'<button type="button" class="'+a+'close" aria-hidden="true">\xd7</button></div>'),e(t.notifier.elm).append(s),t.notifier.spacing=parseInt(s.css("bottom"),10),r(t),s.css("visibility",""),s.find("button").on("click",function(e){e.stopPropagation(),e.preventDefault(),t.close()}),n||(t.timeout=setTimeout(function(){t.close()},t._fadeout)),t._rendered=!0,t},close:function(){var t=this,n=e("#"+t.id);n.addClass(a+"fadeout"),t.timeout=setTimeout(function(){n.remove(),r(t)},500),t.fire("close")}});return t.extend({init:function(e){this.elm=e},info:function(e){return new s(this).message(e).render()},progress:function(e){return new s(this).percent(0).message(e).render()}})}),i(Ye,[M,K],function(e,t){return e.extend({showPanel:function(){var e=this,n=e.settings;if(e.active(!0),e.panel)e.panel.show();else{var i=n.panel;i.type&&(i={layout:"grid",items:i}),i.role=i.role||"dialog",i.popover=!0,i.autohide=!0,i.ariaRoot=!0,e.panel=new t(i).on("hide",function(){e.active(!1)}).on("cancel",function(t){t.stopPropagation(),e.focus(),e.hidePanel()}).parent(e).renderTo(e.getContainerElm()),e.panel.fire("show"),e.panel.reflow()}e.panel.moveRel(e.getEl(),n.popoverAlign||(e.isRtl()?["bc-tr","bc-tc"]:["bc-tl","bc-tc"]))},hidePanel:function(){var e=this;e.panel&&e.panel.hide()},postRender:function(){var e=this;return e.aria("haspopup",!0),e.on("click",function(t){t.control===e&&(e.panel&&e.panel.visible()?e.hidePanel():(e.showPanel(),e.panel.focus(!!t.aria)))}),e._super()},remove:function(){return this.panel&&(this.panel.remove(),this.panel=null),this._super()}})}),i($e,[I],function(e){return e.extend({init:function(e){var t=this;e.delimiter||(e.delimiter="\xbb"),t._super(e),t.classes.add("path"),t.canFocus=!0,t.on("click",function(e){var n,i=e.target;(n=i.getAttribute("data-index"))&&t.fire("select",{value:t.row()[n],index:n})}),t.row(t.settings.row)},focus:function(){var e=this;return e.getEl().firstChild.focus(),e},row:function(e){return arguments.length?(this.state.set("row",e),this):this.state.get("row")},renderHtml:function(){var e=this;return'<div id="'+e._id+'" class="'+e.classes+'">'+e._getDataPathHtml(e.state.get("row"))+"</div>"},bindStates:function(){var e=this;return e.state.on("change:row",function(t){e.innerHtml(e._getDataPathHtml(t.value))}),e._super()},_getDataPathHtml:function(e){var t=this,n=e||[],i,r,o="",a=t.classPrefix;for(i=0,r=n.length;r>i;i++)o+=(i>0?'<div class="'+a+'divider" aria-hidden="true"> '+t.settings.delimiter+" </div>":"")+'<div role="button" class="'+a+"path-item"+(i==r-1?" "+a+"last":"")+'" data-index="'+i+'" tabindex="-1" id="'+t._id+"-"+i+'" aria-level="'+i+'">'+n[i].name+"</div>";return o||(o='<div class="'+a+'path-item">\xa0</div>'),o}})}),i(Ge,[I],function(e){return e.extend({Defaults:{value:0},init:function(e){var t=this;t._super(e),t.classes.add("progress"),t.settings.filter||(t.settings.filter=function(e){return Math.round(e)})},renderHtml:function(){var e=this,t=e._id,n=this.classPrefix;return'<div id="'+t+'" class="'+e.classes+'"><div class="'+n+'text">0%</div><div class="'+n+'bar"></div></div>'},postRender:function(){var e=this;return e._super(),e.value(e.settings.value),e},bindStates:function(){function e(e){e=t.settings.filter(e),t.getEl().firstChild.innerHTML=e+"%",t.getEl().lastChild.style.width=e+"%"}var t=this;return t.state.on("change:value",function(t){e(t.value)}),e(t.state.get("value")),t._super()}})}),i(Je,[z],function(e){return e.extend({Defaults:{classes:"radio",role:"radio"}})}),i(Ke,[O],function(e){return e.extend({})}),i(Ze,[I,U,_],function(e,t,n){function i(e,t,n){return t>e&&(e=t),e>n&&(e=n),e}return e.extend({init:function(e){var t=this;e.previewFilter||(e.previewFilter=function(e){return Math.round(100*e)/100}),t._super(e),t.classes.add("slider"),"v"==e.orientation&&t.classes.add("vertical"),t._minValue=e.minValue||0,t._maxValue=e.maxValue||100,t._initValue=t.state.get("value")},renderHtml:function(){var e=this,t=e._id,n=e.classPrefix;return'<div id="'+t+'" class="'+e.classes+'"><div id="'+t+'-handle" class="'+n+'slider-handle"></div></div>'},reset:function(){this.value(this._initValue).repaint()},postRender:function(){var e=this,r,o,a=0,s,l,u,c,d,f,h,p;l=e._minValue,u=e._maxValue,s=e.value(),"v"==e.settings.orientation?(d="screenY",f="top",h="height",p="h"):(d="screenX",f="left",h="width",p="w"),e._super(),e._dragHelper=new t(e._id,{handle:e._id+"-handle",start:function(t){r=t[d],o=parseInt(e.getEl("handle").style[f],10),c=(e.layoutRect()[p]||100)-n.getSize(e.getEl("handle"))[h],e.fire("dragstart",{value:s})},drag:function(t){var n=t[d]-r,h=e.getEl("handle");a=i(o+n,0,c),h.style[f]=a+"px",s=l+a/c*(u-l),e.value(s),e.tooltip().text(""+e.settings.previewFilter(s)).show().moveRel(h,"bc tc"),e.fire("drag",{value:s})},stop:function(){e.tooltip().hide(),e.fire("dragend",{value:s})}})},bindStates:function(){function e(e){var i,r,o,a,s;"v"==t.settings.orientation?(a="top",o="height",r="h"):(a="left",o="width",r="w"),i=(t.layoutRect()[r]||100)-n.getSize(t.getEl("handle"))[o],s=i*((e-t._minValue)/(t._maxValue-t._minValue))+"px",t.getEl("handle").style[a]=s,t.getEl("handle").style.height=t.layoutRect().h+"px"}var t=this;return t.state.on("change:value",function(t){e(t.value)}),e(t.value()),t._super()}})}),i(Qe,[I],function(e){return e.extend({renderHtml:function(){var e=this;return e.classes.add("spacer"),e.canFocus=!1,'<div id="'+e._id+'" class="'+e.classes+'"></div>'}})}),i(et,[Ue,_,T],function(e,t,n){return e.extend({Defaults:{classes:"widget btn splitbtn",role:"button"},repaint:function(){var e=this,i=e.getEl(),r=e.layoutRect(),o,a;return e._super(),o=i.firstChild,a=i.lastChild,n(o).css({width:r.w-t.getSize(a).width,height:r.h-2}),n(a).css({height:r.h-2}),e},activeMenu:function(e){var t=this;n(t.getEl().lastChild).toggleClass(t.classPrefix+"active",e)},renderHtml:function(){var e=this,t=e._id,n=e.classPrefix,i,r=e.state.get("icon"),o=e.state.get("text");return i=e.settings.image,i?(r="none","string"!=typeof i&&(i=window.getSelection?i[0]:i[1]),i=" style=\"background-image: url('"+i+"')\""):i="",r=e.settings.icon?n+"ico "+n+"i-"+r:"",'<div id="'+t+'" class="'+e.classes+'" role="button" tabindex="-1"><button type="button" hidefocus="1" tabindex="-1">'+(r?'<i class="'+r+'"'+i+"></i>":"")+(o?(r?" ":"")+o:"")+'</button><button type="button" class="'+n+'open" hidefocus="1" tabindex="-1">'+(e._menuBtnText?(r?"\xa0":"")+e._menuBtnText:"")+' <i class="'+n+'caret"></i></button></div>'},postRender:function(){var e=this,t=e.settings.onclick;return e.on("click",function(e){var n=e.target;if(e.control==this)for(;n;){if(e.aria&&"down"!=e.aria.key||"BUTTON"==n.nodeName&&-1==n.className.indexOf("open"))return e.stopImmediatePropagation(),void(t&&t.call(this,e));n=n.parentNode}}),delete e.settings.onclick,e._super()}})}),i(tt,[Z],function(e){return e.extend({Defaults:{containerClass:"stack-layout",controlClass:"stack-layout-item",endClass:"break"},isNative:function(){return!0}})}),i(nt,[G,T,_],function(e,t,n){return e.extend({Defaults:{layout:"absolute",defaults:{type:"panel"}},activateTab:function(e){var n;this.activeTabId&&(n=this.getEl(this.activeTabId),t(n).removeClass(this.classPrefix+"active"),n.setAttribute("aria-selected","false")),this.activeTabId="t"+e,n=this.getEl("t"+e),n.setAttribute("aria-selected","true"),t(n).addClass(this.classPrefix+"active"),this.items()[e].show().fire("showtab"),this.reflow(),this.items().each(function(t,n){e!=n&&t.hide()})},renderHtml:function(){var e=this,t=e._layout,n="",i=e.classPrefix;return e.preRender(),t.preRender(e),e.items().each(function(t,r){var o=e._id+"-t"+r;t.aria("role","tabpanel"),t.aria("labelledby",o),n+='<div id="'+o+'" class="'+i+'tab" unselectable="on" role="tab" aria-controls="'+t._id+'" aria-selected="false" tabIndex="-1">'+e.encode(t.settings.title)+"</div>"}),'<div id="'+e._id+'" class="'+e.classes+'" hidefocus="1" tabindex="-1"><div id="'+e._id+'-head" class="'+i+'tabs" role="tablist">'+n+'</div><div id="'+e._id+'-body" class="'+e.bodyClasses+'">'+t.renderHtml(e)+"</div></div>"},postRender:function(){var e=this;e._super(),e.settings.activeTab=e.settings.activeTab||0,e.activateTab(e.settings.activeTab),this.on("click",function(t){var n=t.target.parentNode;if(t.target.parentNode.id==e._id+"-head")for(var i=n.childNodes.length;i--;)n.childNodes[i]==t.target&&e.activateTab(i)})},initLayoutRect:function(){var e=this,t,i,r;i=n.getSize(e.getEl("head")).width,i=0>i?0:i,r=0,e.items().each(function(e){i=Math.max(i,e.layoutRect().minW),r=Math.max(r,e.layoutRect().minH)}),e.items().each(function(e){e.settings.x=0,e.settings.y=0,e.settings.w=i,e.settings.h=r,e.layoutRect({x:0,y:0,w:i,h:r})});var o=n.getSize(e.getEl("head")).height;return e.settings.minWidth=i,e.settings.minHeight=r+o,t=e._super(),t.deltaH+=o,t.innerH=t.h-t.deltaH,t}})}),i(it,[I],function(e){return e.extend({init:function(e){var t=this;t._super(e),t.classes.add("textbox"),e.multiline?t.classes.add("multiline"):(t.on("keydown",function(e){var n;13==e.keyCode&&(e.preventDefault(),t.parents().reverse().each(function(e){return e.toJSON?(n=e,!1):void 0}),t.fire("submit",{data:n.toJSON()}))}),t.on("keyup",function(e){t.state.set("value",e.target.value)}))},repaint:function(){var e=this,t,n,i,r=0,o=0,a;t=e.getEl().style,n=e._layoutRect,a=e._lastRepaintRect||{};var s=document;return!e.settings.multiline&&s.all&&(!s.documentMode||s.documentMode<=8)&&(t.lineHeight=n.h-o+"px"),i=e.borderBox,r=i.left+i.right+8,o=i.top+i.bottom+(e.settings.multiline?8:0),n.x!==a.x&&(t.left=n.x+"px",a.x=n.x),n.y!==a.y&&(t.top=n.y+"px",a.y=n.y),n.w!==a.w&&(t.width=n.w-r+"px",a.w=n.w),n.h!==a.h&&(t.height=n.h-o+"px",a.h=n.h),e._lastRepaintRect=a,e.fire("repaint",{},!1),e},renderHtml:function(){var e=this,t=e._id,n=e.settings,i=e.encode(e.state.get("value"),!1),r="";return"spellcheck"in n&&(r+=' spellcheck="'+n.spellcheck+'"'),n.maxLength&&(r+=' maxlength="'+n.maxLength+'"'),n.size&&(r+=' size="'+n.size+'"'),n.subtype&&(r+=' type="'+n.subtype+'"'),e.disabled()&&(r+=' disabled="disabled"'),n.multiline?'<textarea id="'+t+'" class="'+e.classes+'" '+(n.rows?' rows="'+n.rows+'"':"")+' hidefocus="1"'+r+">"+i+"</textarea>":'<input id="'+t+'" class="'+e.classes+'" value="'+i+'" hidefocus="1"'+r+" />"},value:function(e){return arguments.length?(this.state.set("value",e),this):(this.state.get("rendered")&&this.state.set("value",this.getEl().value),this.state.get("value"))},postRender:function(){var e=this;e._super(),e.$el.on("change",function(t){e.state.set("value",t.target.value),e.fire("change",t)})},bindStates:function(){var e=this;return e.state.on("change:value",function(t){e.getEl().value=t.value}),e.state.on("change:disabled",function(t){e.getEl().disabled=t.value}),e._super()},remove:function(){this.$el.off(),this._super()}})}),i(rt,[T,k],function(e,t){return function(n,i){var r=this,o,a=t.classPrefix;r.show=function(t,s){return r.hide(),o=!0,window.setTimeout(function(){o&&(e(n).append('<div class="'+a+"throbber"+(i?" "+a+"throbber-inline":"")+'"></div>'),s&&s())},t||0),r},r.hide=function(){var e=n.lastChild;return e&&-1!=e.className.indexOf("throbber")&&e.parentNode.removeChild(e),o=!1,r}}}),i(ot,[I],function(e){return e.extend({init:function(e){var t=this;t._super(e),t.classes.add("thumb"),t.on("click",function(e){-1!=e.target.className.indexOf("checkbox")&&t.classes.toggle("checked",!0)})},renderHtml:function(){var e=this,t=e._id,n=e.classPrefix,i=e.settings,r=i.icon;return'<div id="'+t+'" class="'+e.classes+'">'+(r?'<i class="'+n+"ico "+n+"i-"+r+'"></i>':"")+(i.url?'<img src="'+i.url+'" />':"")+'<div class="'+n+'info">'+(i.selectable?'<i class="'+n+"ico "+n+'i-checkbox"></i>':"")+e.settings.text+"</div></div>"}})}),i(at,[O,S],function(e,t){var n=e.extend({Defaults:{defaultType:"treeitem",layout:"stack",expanded:!1},init:function(e){var n=this;this._super(e),e=this.settings,this.titleClasses=new t(function(){n.state.get("rendered")&&(n.getEl("title").className=this.toString())}),this.titleClasses.prefix=n.classPrefix,this.classes.add("treeitem"),this.bodyClasses.add("treeitem-body"),this.titleClasses.add("treeitem-title"),(e.expanded||e.fixed)&&this.open(),e.fixed&&this.titleClasses.add("treeitem-title-fixed")},open:function(){this.bodyClasses.add("treeitem-expanded"),this.titleClasses.add("treeitem-title-expanded"),this.expanded=!0},close:function(){this.bodyClasses.remove("treeitem-expanded"),this.titleClasses.remove("treeitem-title-expanded"),this.expanded=!1,this.find("treeitem").exec("close")},selected:function(e){this.titleClasses.remove("treeitem-title-selected"),e&&this.titleClasses.add("treeitem-title-selected")},renderHtml:function(){var e=this,t=e._layout,i=e.settings,r,o,a="",s=e.classPrefix,l=e.state.get("text");for(e.preRender(),t.preRender(e),o=e,r=0;o&&o instanceof n;)r++,o.depth=r,o=o.parent();return i.icon?a='<i class="'+s+"ico "+s+"i-"+i.icon+'"></i>':i.iconUrl&&(a='<i class="'+s+'ico" style="background-image: url(\''+i.iconUrl+"')\"></i>"),'<span id="'+e._id+'" class="'+e.classes+'"><span id="'+e._id+'-title" class="'+e.titleClasses+'">'+a+'<span id="'+e._id+'-text" class="'+s+'text" unselectable="true">'+e.encode(l)+'</span></span><span id="'+e._id+'-body" class="'+e.bodyClasses+'">'+t.renderHtml(e)+"</span></span>"}});return n}),i(st,[G,at],function(e,t){return e.extend({Defaults:{defaultType:"treeitem",layout:"stack"},init:function(e){var n=this;n._super(e),n.on("click",function(e){var i=e.control;i instanceof t&&(e.preventDefault(),i.settings.fixed||(n.find("treeitem").each(function(e){e.titleClasses.remove("treeitem-title-selected")}),i.titleClasses.add("treeitem-title-selected"),i.items().length>0&&(i.expanded?i.close():i.open())))})}})}),i(lt,[O,T,_],function(e,t,n){return e.extend({Defaults:{defaultType:"panel",layout:"fit",minWidth:1,minHeight:1},init:function(e){var t=this;this._super(e),t.classes.add("viewport")},postRender:function(){var e=this,n=document.documentElement,i,r=e.classPrefix;t([n,document.body]).addClass(r+"viewport"),t(window).on("resize",function(){var t;i?e._timer||(e._timer=setTimeout(function(){e.reflow(),e._timer=0},50)):(t=(new Date).getTime(),e.reflow(),(new Date).getTime()-t>50&&(i=!0))}),e._super(),e.reflow()},recalc:function(){var e=this,t=n.getWindowSize();return e.layoutRect(t),e._super()},remove:function(){var e=document,n=this.classPrefix;return t([e.documentElement,e.body]).removeClass(e.documentElement,n+"viewport"),this._super()}})}),i(ut,[],function(){return{createActions:function(e){function t(){function e(){for(var e=0;e<t.length;e++)t[e].apply(this,arguments)}var t=[];return e.listen=function(e){t.push(e)},e}var n,i={};for(n=0;n<e.length;n++)i[e[n]]=t();return i}}}),i(ct,[ke],function(e){var t,n,i,r;return{dateTime:function(t,n){return this.format(t,n||e.translate("_dateformatter.datetime","%D %H:%M:%S"))},format:function(o,a){function s(t){return e.translate(t).split(" ")}function l(e){return(10>e?"0":"")+e}function u(e,t){a=a.replace(e,t)}return t||(t=s("January February March April May June July August September October November December"),n=s("Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec"),i=s("Sunday Monday Tuesday Wednesday Thursday Friday Saturday"),r=s("Sun Mon Tue Wed Thu Fri Sat")),o.getTime||(o=new Date(o)),a=a||"%Y-%d-%m %H:%M:%S",u("%D","%m/%d/%Y"),u("%r","%I:%M:%S %p"),u("%Y",""+o.getFullYear()),u("%y",""+o.getYear()),u("%m",l(o.getMonth()+1)),u("%d",l(o.getDate())),u("%H",""+l(o.getHours())),u("%M",""+l(o.getMinutes())),u("%S",""+l(o.getSeconds())),u("%I",""+((o.getHours()+11)%12+1)),u("%p",""+(o.getHours()<12?"AM":"PM")),u("%B",""+t[o.getMonth()]),u("%b",""+n[o.getMonth()]),u("%A",""+i[o.getDay()]),u("%a",""+r[o.getDay()]),u("%%","%"),a}}}),i(dt,[c],function(e){function t(t){function n(e){return e?e.toLowerCase().split(/\s*,\s*/):[]}for(var i=n(t[0]),r=1;r<t.length;r++){var o=n(t[r]);if(o.length&&"*"!=o[0]){if("*"!=i[0])for(var a=0;a<o.length;a++)-1==e.inArray(o[a],i)&&o.splice(a,1);i=o}}return i}return{merge:t}}),i(ft,[],function(){return{format:function(e){var t,n,i;if(e>-1)for(i=["TB","GB","MB","KB","B"],t=0;t<i.length;t++)if(n=Math.pow(1024,4-t),e>n)return Math.round(e/n)+" "+i[t];return""},parse:function(e){var t;return"string"==typeof e&&(e=/^([0-9]+)([mgk]?)$/.exec(e.toLowerCase().replace(/[^0-9mkg]/g,"")),t=e[2],e=+e[1],"g"==t&&(e*=1073741824),"m"==t&&(e*=1048576),"k"==t&&(e*=1024)),e}}}),i(ht,[],function(){function e(e,t){var n,i,r;for(t=t.split(" "),r=t.length;r--;){if(n=t[r],i=e[n],"undefined"!=typeof i)return i;n=n.substr(0,1).toUpperCase()+n.substr(1);for(var o="o ms moz webkit".split(" "),a=o.length;a--;)if(i=e[o[a]+n],"undefined"!=typeof i)return i}}return{canFullScreen:function(){return e(document,"fullScreenEnabled FullscreenEnabled")===!0},isFullScreen:function(){return e(document,"isFullScreen fullScreen")},requestFullScreen:function(t){t=t||document.body,e(t,"requestFullScreen").call(t)},cancelFullScreen:function(){e(document,"cancelFullScreen").call(document)}}}),i(pt,[c],function(e){function t(e,t){function n(n){function o(o){a.parentNode?a.parentNode.removeChild(a):a.removeNode&&a.removeNode(),i[n]=o,r++==e.length-1&&t(i)}var a=new Image;a.onload=function(){o({url:e[n],width:Math.max(a.width,a.clientWidth),height:Math.max(a.height,a.clientHeight)})},a.onerror=function(){o({url:e[n],width:-1,height:-1})},a.src=e[n];var s=a.style;s.visibility="hidden",s.position="fixed",s.bottom=s.left=0,document.body.appendChild(a)}for(var i=new Array(e.length),r=0,o=0;o<e.length;o++)n(o)}function n(n,i){var r=[];return e.each(n,function(e){var t=/\.(gif|jpe?g|png)$/i.test(e.name);t&&(e.meta.thumb_url&&!e.meta.thumb_width&&r.push(e.meta.thumb_url),e.meta.width||r.push(e.meta.url)),e.meta.thumb_url=e.meta.thumb_url||e.meta.url}),r.length?void t(r,function(e){for(var t=0;t<e.length;t++)for(var r=0;r<n.length;r++)e[t].url==n[r].meta.url&&(n[r].meta.width=e[t].width,n[r].meta.height=e[t].height),e[t].url==n[r].meta.thumb_url&&(n[r].meta.thumb_width=e[t].width,n[r].meta.thumb_height=e[t].height);i(n)}):void i(n)}return{populateImageSizes:n}}),i(mt,[],function(){function e(){}function t(e){document.getElementsByTagName("head")[0].appendChild(e)}var n=0,i={},r={maxLoadTime:5,load:function(e,t,n){function i(){s.length?r.loadScript(s.shift(),i,n):o()}function o(){a.length?r.loadCss(a.shift(),o,n):t()}var a=e.css||[],s=e.js||[];i()},loadScript:function(n,r,o){function a(){i[n]=!0,r()}var s,l;if(i[n])return void r();if(r=r||e,o=o||e,l=document.createElement("script"),l.type="text/javascript","object"==typeof n)for(s in n)l.setAttribute(s,n[s]);else l.src=n;"onload"in l?(l.onload=a,l.onerror=o):(l.onreadystatechange=function(){var e=l.readyState;("complete"==e||"loaded"==e)&&a()},l.onerror=o),t(l)},loadCss:function(o,a,s){function l(){i[o]=!0,a()}function u(){var e=navigator.userAgent.match(/WebKit\/(\d*)/);return!!(e&&e[1]<536)}function c(){for(var e=f.styleSheets,t,n=e.length,i;n--;)if(t=e[n],i=t.ownerNode?t.ownerNode:t.owningElement,i&&i.id===h.id)return void l();(new Date).getTime()-m<1e3*r.maxLoadTime?window.setTimeout(c,0):s()}function d(){try{var e=p.sheet.cssRules;return l(),e}catch(t){}(new Date).getTime()-m<1e3*r.maxLoadTime?window.setTimeout(d,0):s()}var f=document,h,p,m;if(i[o])return void a();if(a=a||e,s=s||e,h=f.createElement("link"),h.rel="stylesheet",h.type="text/css",h.href=o,h.id="u"+n++,m=(new Date).getTime(),"onload"in h&&!u())h.onload=l,h.onerror=s;else{if(navigator.userAgent.indexOf("Firefox")>0)return p=f.createElement("style"),p.textContent='@import "'+o+'"',d(),void t(p);c()}t(h)}};return r}),i(gt,[c],function(e){var t=e.each,n={"delete":46};return function(e){var i=this,r={};e.on("keyup keypress keydown",function(e){/INPUT|TEXTAREA/.test(e.target.nodeName)||t(r,function(t){var n=e.metaKey||e.ctrlKey;if(t.ctrl==n&&t.alt==e.altKey&&t.shift==e.shiftKey)return e.keyCode==t.keyCode||e.charCode&&e.charCode==t.charCode?(e.preventDefault(),"keydown"==e.type&&t.func(),!0):void 0})}),i.add=function(e,i){t(e.toLowerCase().split(" "),function(e){var o={func:i,alt:!1,ctrl:!1,shift:!1};t(e.split("+"),function(e){switch(e){case"alt":case"ctrl":case"shift":o[e]=!0;break;default:o.charCode=e.charCodeAt(0),o.keyCode=n[e]||e.toUpperCase().charCodeAt(0)}}),r[(o.ctrl?"ctrl":"")+","+(o.alt?"alt":"")+","+(o.shift?"shift":"")+","+o.keyCode]=o})}}}),i(vt,[c],function(e){function n(e){return null===e||e===t?"":(""+e).replace(/^\s*|\s*$/g,"")}function i(e,t){var a=this,s,l;if(e=n(e),t=a.settings=t||{},/^([\w\-]+):([^\/]{2})/i.test(e)||/^\s*#/.test(e))return void(a.source=e);var u=0===e.indexOf("//");0!==e.indexOf("/")||u||(e=(t.base_uri?t.base_uri.protocol||"http":"http")+"://mce_host"+e),/^[\w\-]*:?\/\//.test(e)||(l=t.base_uri?t.base_uri.path:new i(location.href).directory,e=""===t.base_uri.protocol?"//mce_host"+a.toAbsPath(l,e):(t.base_uri&&t.base_uri.protocol||"http")+"://mce_host"+a.toAbsPath(l,e)),e=e.replace(/@@/g,"(mce_at)"),e=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*):?([^:@\/]*))?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/.exec(e),r(o,function(t,n){var i=e[n];i&&(i=i.replace(/\(mce_at\)/g,"@@")),a[t]=i}),s=t.base_uri,s&&(a.protocol||(a.protocol=s.protocol),a.userInfo||(a.userInfo=s.userInfo),a.port||"mce_host"!==a.host||(a.port=s.port),a.host&&"mce_host"!==a.host||(a.host=s.host),a.source=""),u&&(a.protocol="")}var r=e.each,o="source protocol authority userInfo user password host port relative path directory file query anchor".split(" ");return i.prototype={setPath:function(e){var t=this;e=/^(.*?)\/?(\w+)?$/.exec(e),t.path=e[0],t.directory=e[1],t.file=e[2],t.source="",t.getURI()},toRelative:function(e){var t=this,n;if("./"===e)return e;if(e=new i(e,{base_uri:t}),"mce_host"!=e.host&&t.host!=e.host&&e.host||t.port!=e.port||t.protocol!=e.protocol&&""!==e.protocol)return e.getURI();var r=t.getURI(),o=e.getURI();return r==o||"/"==r.charAt(r.length-1)&&r.substr(0,r.length-1)==o?r:(n=t.toRelPath(t.path,e.path),e.query&&(n+="?"+e.query),e.anchor&&(n+="#"+e.anchor),n)},toAbsolute:function(e,t){return e=new i(e,{base_uri:this}),e.getURI(this.host==e.host&&this.protocol==e.protocol?t:0)},toRelPath:function(e,t){var n,i=0,r="",o,a;if(e=e.substring(0,e.lastIndexOf("/")),e=e.split("/"),n=t.split("/"),e.length>=n.length)for(o=0,a=e.length;a>o;o++)if(o>=n.length||e[o]!=n[o]){i=o+1;break}if(e.length<n.length)for(o=0,a=n.length;a>o;o++)if(o>=e.length||e[o]!=n[o]){i=o+1;break}if(1===i)return t;for(o=0,a=e.length-(i-1);a>o;o++)r+="../";for(o=i-1,a=n.length;a>o;o++)r+=o!=i-1?"/"+n[o]:n[o];return r},toAbsPath:function(e,t){var n,i=0,o=[],a,s;for(a=/\/$/.test(t)?"/":"",e=e.split("/"),t=t.split("/"),r(e,function(e){e&&o.push(e)}),e=o,n=t.length-1,o=[];n>=0;n--)0!==t[n].length&&"."!==t[n]&&(".."!==t[n]?i>0?i--:o.push(t[n]):i++);return n=e.length-i,s=0>=n?o.reverse().join("/"):e.slice(0,n).join("/")+"/"+o.reverse().join("/"),0!==s.indexOf("/")&&(s="/"+s),a&&s.lastIndexOf("/")!==s.length-1&&(s+=a),s},getURI:function(e){var t,n=this;return(!n.source||e)&&(t="",e||(t+=n.protocol?n.protocol+"://":"//",n.userInfo&&(t+=n.userInfo+"@"),n.host&&(t+=n.host),n.port&&(t+=":"+n.port)),n.path&&(t+=n.path),n.query&&(t+="?"+n.query),n.anchor&&(t+="#"+n.anchor),n.source=t),n.source}},i.parseURI=function(t,n){return n=n||{},new i(t,e.extend({base_uri:new i(n.base_url||document.location.href.split("?")[0].replace(/\/[^\/]+$/,"")+"/")},n))},i}),i(yt,[c],function(e){function t(t,n){var i=[];return t=e.deepSplit(t),e.each(t,function(e,r){e=n(e,r,t.length),e&&i.push(e)}),i}var n={},i={};return{addButton:function(e,t){n[e]=t},addMenuItem:function(e,t){i[e]=t},addButtons:function(t){var n=this;e.each(t,function(e,t){n.addButton(t,e)})},addMenuItems:function(t){var n=this;e.each(t,function(e,t){n.addMenuItem(t,e)})},createToolbar:function(e,i,r){return"undefined"==typeof i&&(i=r),t(i,function(t){return t=n[e+"."+t],"function"==typeof t&&(t=t()),t})},createMenu:function(e,n){return t(n,function(t,n,r){return("-"==t||"|"==t)&&n>0&&r-1>=n?{text:"-"}:(t=i[e+"."+t],"function"==typeof t&&(t=t()),t)})}}}),i(xt,[],function(){return{NO_ACCESS:10,NEEDS_INSTALLATION:11,DEMO_MODE:100,NO_READ_ACCESS:101,NO_WRITE_ACCESS:102,FILE_EXISTS:103,FILE_SIZE_TO_LARGE:104,FILE_DOESNT_EXIST:105,INVALID_FILE_NAME:106,METHOD_NOT_FOUND:107,INVALID_FILE_TYPE:108,NO_ACCESS_EXTERNAL_AUTH:1009}}),i(bt,[vt,ct,ue,c],function(e,t,n,i){return{update:function(r,o){function a(e){var t,n,i,r;r="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/",i="",n=Math.floor(e);do t=n%64,i=r.charAt(t)+i,n=Math.floor(n/64);while(0!==n);return i}function s(t,n){var i;return n&&(r.unique_urls&&(i=a(t.size+t.lastModified),n+=-1===n.indexOf("?")?"?u="+i:"&u="+i),r.no_host=r.no_host||r.remove_script_host,r.relative_urls?n=e.parseURI(r.document_base_url||r.default_base_url).toRelative(n):(r.document_base_url||r.no_host)&&(n=e.parseURI(r.document_base_url).toAbsolute(e.parseURI(n).getURI(),r.no_host))),n}var l,u;if(i.isArray(o)){for(l=0;l<o.length;l++)o[l]=this.update(r,o[l]);return o}return u=n.extname(o.name),o.meta=o.meta||{},o.nameWithoutExtension=n.basename(o.name,u),o.extension=u,o.url=o.meta.url=s(o,o.meta.url),o.meta.thumb_url&&(o.thumbnailUrl=o.meta.thumb_url=s(o,o.meta.thumb_url)),r.insert_filter&&r.insert_filter(o),o.lastModified=t.dateTime(o.lastModified,r.dateFormat),o}}}),i(wt,[Ne],function(e){return e.extend({idAttribute:"path",fields:{path:{type:"string"},name:{type:"string"},meta:{type:"object"}},isLinked:function(){return this.get("meta").linked}})}),i(_t,[Pe,wt,le,ue],function(e,t,n,i){return e.extend({model:t,findByPath:function(e){var t;for(t=0;t<this.length;t++)if(i.isChildOf(e,this[t].get("path")))return this[t];return null},sync:function(e,t){function r(e){var t,n,r,o,a=[];if(e){for(e=e.split(";"),t=0;t<e.length;t++)o=e[t].split("="),n=i.basename(o[0].trim()),r=(o[1]||o[0]).trim(),a.push({name:n,path:r});return a}}function o(e,t){var n;for(n=0;n<t.length;n++)if(i.isChildOf(e,t[n].path))return t[n]}var a=this;return t=t||{},"read"==e?n.exec("listRoots",t).then(function(e){var n,i,s,l=[],u=[];for(n=0;n<e.length;n++)i=e[n],i.meta.linked?u.push(i):l.push(i);if(s=r(t.rootpath)){for(n=0;n<s.length;n++)i=o(s[n].path,l),i&&(s[n].meta=i.meta);l=s}a.add(l.concat(u))}):void 0}})}),i(Et,[Ne],function(e){return e.extend({fields:{type:{type:"string"},text:{type:"string"},percent:{type:"number"}}})}),i(Rt,[Pe,Et],function(e,t){return e.extend({model:t})}),i(Ct,[ce,Re,be],function(e,t,n){function i(i,r){var o,a;if(r||(r={}),a=r.type&&""!==r.type?r.type:t.getFileMime(r.name),r.name)o=r.name.replace(/\\/g,"/"),o=o.substr(o.lastIndexOf("/")+1);else{var s=a.split("/")[0];o=e.guid((""!==s?s:"file")+"_"),t.extensions[a]&&(o+="."+t.extensions[a][0])}n.apply(this,arguments),e.extend(this,{type:a||"",name:o||e.guid("file_"),lastModifiedDate:r.lastModifiedDate||(new Date).toLocaleString()})}return i.prototype=n.prototype,i}),i(Tt,[ce,Re,ge,de,fe,Ee,Ct,ve,ye],function(e,t,n,i,r,o,a,s,l){function u(r){var u=this,d,f,h;if(-1!==e.inArray(e.typeOf(r),["string","node"])&&(r={browse_button:r}),f=n.get(r.browse_button),!f)throw new i.DOMException(i.DOMException.NOT_FOUND_ERR);h={accept:[{title:o.translate("All Files"),extensions:"*"}],name:"file",multiple:!1,required_caps:!1,container:f.parentNode||document.body},r=e.extend({},h,r),"string"==typeof r.required_caps&&(r.required_caps=s.parseCaps(r.required_caps)),"string"==typeof r.accept&&(r.accept=t.mimes2extList(r.accept)),d=n.get(r.container),d||(d=document.body),"static"===n.getStyle(d,"position")&&(d.style.position="relative"),d=f=null,l.call(u),e.extend(u,{uid:e.guid("uid_"),ruid:null,shimid:null,files:null,init:function(){u.convertEventPropsToHandlers(c),u.bind("RuntimeInit",function(t,i){u.ruid=i.uid,u.shimid=i.shimid,u.bind("Ready",function(){u.trigger("Refresh")},999),u.bind("Change",function(){var t=i.exec.call(u,"FileInput","getFiles");u.files=[],e.each(t,function(e){return 0===e.size?!0:void u.files.push(new a(u.ruid,e))})},999),u.bind("Refresh",function(){var t,o,a,s;a=n.get(r.browse_button),
s=n.get(i.shimid),a&&(t=n.getPos(a,n.get(r.container)),o=n.getSize(a),s&&e.extend(s.style,{top:t.y+"px",left:t.x+"px",width:o.w+"px",height:o.h+"px"})),s=a=null}),i.exec.call(u,"FileInput","init",r)}),u.connectRuntime(e.extend({},r,{required_caps:{select_file:!0}}))},disable:function(t){var n=this.getRuntime();n&&n.exec.call(this,"FileInput","disable","undefined"===e.typeOf(t)?!0:t)},refresh:function(){u.trigger("Refresh")},destroy:function(){var t=this.getRuntime();t&&(t.exec.call(this,"FileInput","destroy"),this.disconnectRuntime()),"array"===e.typeOf(this.files)&&e.each(this.files,function(e){e.destroy()}),this.files=null}})}var c=["ready","change","cancel","mouseenter","mouseleave","mousedown","mouseup"];return u.prototype=r.instance,u}),i(St,[C,c,y,se,re,ue,dt,Me,Oe,xt,ft,He,Tt,Ce,_e,me,Re,fe],function(e,t,n,i,r,o,a,s,l,u,c,d,f,h,p,m,g,v){function y(){return"FormData"in window}function x(e){var t;return t=y()?new FormData:new p,t.append("file",e),t}function b(){var e;return e=y()?new XMLHttpRequest:new h}function w(){return(new Date).getTime()}function _(e,t){this.config=e,this.settings=t,this.files=[],this._disabled=!1}function E(e){return t.map(e,function(e){return e.code==u.FILE_SIZE_TO_LARGE&&(e={message:'The "{0}" file is too large. Maximum file size is: {1}',code:e.code,args:[e.data.fileName,c.format(e.data.maxSize)]}),e.code==u.INVALID_FILE_NAME&&(e={message:'The "{0}" has an invalid file name or file type. Valid extensions are: {1}',code:e.code,args:[e.data.fileName,e.data.extensions]}),e})}return g.addMimeType("application/epub+zip,epub"),t.extend(_.prototype,{picker:function(t){function n(e){var t;for(t=e.length;t--;)e[t].size>r&&e.splice(t,1);i.files=i.files.concat(e),i.fire("select",{files:e})}var i=this,r,o,a=t._id;r=c.parse(i.settings.maxsize)||1/0,y()?t.on("click",function(){o=document.createElement("input"),o.type="file",o.setAttribute("multiple",""),document.body.appendChild(o),o.onchange=function(e){n(Array.prototype.slice.call(e.target.files))},o.click(),o.parentNode.removeChild(o),i.fileInput=o}):(m.swf_url=e.baseUrl+"/js/Moxie.swf",m.global_event_dispatcher="moxman.dispatchEvent",window.moxman.dispatchEvent=v.instance.dispatchEvent,o=new f({browse_button:a,required_caps:"access_binary",multiple:!0}),o.onchange=function(e){n(Array.prototype.slice.call(e.target.files))},o.oncancel=function(){i.fire("cancel")},o.init(),this.fileInput=o)},getExtensions:function(){return a.merge([this.config.get("filesystem.extensions"),this.config.get("upload.extensions"),this.settings.extensions])},dropzone:function(e){var t=this,n=e.getEl();n.ondragover=function(){return!1},n.ondragend=function(){return!1},n.ondrop=function(e){e.preventDefault(),t._disabled||t.fire("drop",{files:e.dataTransfer.files})}},upload:function(n,a,f){function h(e){return"*"==C[0]||-1!==t.inArray(o.extname(e.name).toLowerCase().substr(1),C)}function p(t,n,i,o,l){var u=t.userFile,c=u.slice(i,i+y);R=b(),R.open("POST",e.apiPageUrl+"?action=upload&path="+encodeURIComponent(a)+"&name="+encodeURIComponent(u.name)+"&loaded="+i+"&total="+u.size+"&id="+n+"&csrf="+encodeURIComponent(o)+"&resolution="+encodeURIComponent(t.type)),R.upload.onprogress=function(e){var t,n=e.loaded-(e.total-c.size);I=Math.ceil(i+n/((w()-N)/1e3)),t=Math.round((i+n)/u.size*100),g.fire("progress",{name:u.name,loaded:i+n,total:u.size,percent:t,bytesPerSecond:I,global:{loaded:S+i+n,total:T},extra:f})},R.onload=function(){var e,n,a;R.upload.onprogress({loaded:c.size,total:c.size});try{e=r.parse(R.responseText)}catch(d){return a={code:-1,message:"Error response is not proper JSON\n\nResponse:\n"+R.responseText,data:{fileName:u.name,fileSize:u.size,maxSize:_,extensions:C.join(", ")},extra:f},D.push(a),g.fire("error",a),void m(o,l)}return e.error?(a={code:e.error.code,message:e.error.message,data:{fileName:u.name,fileSize:u.size,maxSize:_,extensions:C.join(", ")},extra:f},D.push(a),g.fire("error",a),void m(o,l)):void(!e.error&&i+y<u.size?p(t,e.result,i+y,o,l):(e.result&&(n=s.fromJSON(e.result),M.push(n),g.fire("FileUploaded",{file:n})),S+=u.size,e.result&&k.push(e.result),m(o,l)))},R.send(x(c))}function m(e,t){var n=t.shift();return v++,n&&n.userFile.error?void m(e,t):void(n?p(n,null,0,e,t):g.fire("FilesUploaded",{files:new l(M),bytesPerSecond:I,errors:E(D),extra:f}))}var g=this,v=-1,y,_,R,C,T=0,S=0,A,k=[],D=[],N,I=0,M=[];for(C=g.getExtensions(),N=w(),y=c.parse(g.config.get("upload.chunk_size")||"2mb"),_=c.parse(g.config.get("upload.maxsize")||"2mb"),f=f||{},A=0;A<n.length;A++)T+=n[A].size;D=t.map(t.filter(n,t.not(h)),function(e){return{code:u.INVALID_FILE_NAME,message:"Invalid file name",data:{fileName:e.name,fileSize:e.size,maxSize:_,extensions:C.join(", ")},extra:f}}),n=t.filter(n,h),i.request().then(function(e){var t,i=[];for(t=0;t<n.length;t++)i.push(n[t].name);d.confirmOverwrite(i,a).then(function(i){for(t=0;t<i.length;t++)i[t].userFile=n[t];m(e.token,i)})},function(e){g.fire("error",{message:"Error:"+e.message})})},disable:function(e){this._disabled=e,this.fileInput&&this.fileInput.disable&&this.fileInput.disable(e)},destroy:function(){this.fileInput&&(this.fileInput.destroy&&this.fileInput.destroy(),this.fileInput=null)}},n),_.hasNativeSupport=y,_}),i(At,[Ue,c,ue],function(e,t,n){return e.extend({Defaults:{text:"Create",icon:"create"},render:function(){function e(){return a.get("selectedFileSystem")&&a.get("selectedFileSystem").isLinked()}function i(){function e(){t.disabled(!a.get("file").get("canWrite"))}var t=this;a.on("change:file",e),e()}function r(e){var r,o=e.control.menu,l=a.get("config");o.append({text:"Folder",icon:"folder",onclick:function(){s.createDir()},onPostRender:i}),(r=l.get("createdir.templates"))&&t.each(r.split(","),function(e){e=e.split("="),o.append({text:n.basename(e[0]),icon:"folder",onclick:function(){s.createDir(e[1]||e[0])},onPostRender:i})}),(r=l.get("createdoc.templates"))&&(o.append({text:"-"}),t.each(r.split(","),function(e){e=e.split("="),o.append({text:n.basename(e[0]),icon:"file",onclick:function(){s.createDoc(e[1]||e[0])},onPostRender:i})}))}var o=this,a=this.data.get("viewModel"),s=this.data.get("actions");a.on("change:selectedFileSystem",function(){o.visible(!e())}),this.on("createMenu",r),a.get("config").on("change:general.disabled_tools",function(){o.disabled(this.isDisabled("create"))}),a.get("config").on("change:general.hidden_tools",function(){o.visible(!this.isHidden("create")&&!e())})}})}),i(kt,[Ve,c,yt],function(e,t,n){return e.extend({render:function(){function e(){function e(){t.disabled(!f.get("file").get("canWrite"))}var t=this;f.on("change:file",e),e()}function t(){}function i(){function e(){t.disabled(0===m.length||!f.get("file").get("canWrite"))}var t=this;m.on("add remove",e),f.on("change:file",e),e()}function r(){function e(){t.disabled(0===p.length||!p[0].get("canView"))}var t=this;p.on("add remove",e),e()}function o(){function e(){t.disabled(0===p.length||!p[0].get("canEdit"))}var t=this;p.on("add remove",e),e()}function a(){function e(){t.disabled(!f.get("file").get("canWrite"))}var t=this;f.on("change:file",e),e()}function s(){}function l(){var e,t=f.get("fileSystems");for(e=0;e<t.length;e++)if("/Favorites"==t[e].get("path"))return void this.show()}function u(){function e(){t.disabled(!f.get("file").get("canWrite"))}var t=this;f.on("change:file",e),e()}function c(){function e(){var e;p.forEach(function(t){e=e||!t.get("isDirectory")&&/.zip$/i.test(t.get("name"))}),t.disabled(!e)}var t=this;p.on("add remove",e),e()}function d(){function e(){t.disabled(!f.get("file").get("canWrite"))}var t=this;f.on("change:file",e),e()}var f,h,p,m;return h=this.data.get("actions"),f=this.data.get("viewModel"),p=f.get("selectedFiles"),m=f.get("clipboardFiles"),n.addMenuItems({"managemenu.cut":{text:"Cut",icon:"cut",shortcut:"Ctrl+X",onclick:h.cut,onPostRender:e},"managemenu.copy":{text:"Copy",icon:"copy",shortcut:"Ctrl+C",onclick:h.copy,onPostRender:t},"managemenu.paste":{text:"Paste",icon:"paste",shortcut:"Ctrl+V",onclick:h.paste,onPostRender:i},"managemenu.view":{text:"View",icon:"view",onclick:h.view,onPostRender:r},"managemenu.edit":{text:"Edit",icon:"edit",onclick:h.edit,onPostRender:o},"managemenu.rename":{text:"Rename",onclick:h.rename,onPostRender:a},"managemenu.download":{text:"Download",icon:"download",onclick:h.download,onPostRender:s},"managemenu.addfavorite":{text:"Add favorite",icon:"favorites",onclick:h.addFavorite,hidden:!0,onPostRender:l},"managemenu.zip":{text:"Zip",icon:"zip",onclick:h.zip,onPostRender:u},"managemenu.unzip":{text:"Unzip",onclick:h.unzip,onPostRender:c},"managemenu.remove":{text:"Remove",icon:"delete",shortcut:"Delete",onclick:h.deleteFiles,onPostRender:d}}),n.createMenu("managemenu",this.data.get("items")||"cut copy paste | view edit rename download addfavorite | zip unzip | remove")}})}),i(Dt,[Ue,kt],function(e,t){return e.extend({Defaults:{text:"Manage",icon:"manage",visible:!1},render:function(){function e(){return i.get("selectedFileSystem").isLinked()}var n=this,i,r,o,a;return i=this.data.get("viewModel"),r=this.data.get("actions"),o=i.get("userSettings"),a=i.get("config"),i.get("selectedFiles").on("add remove",function(){!this.length||a.isHidden("manage")||e()?n.hide():n.show()}),i.get("config").on("change:general.disabled_tools",function(){n.disabled(this.isDisabled("manage"))}),new t({data:{viewModel:i,actions:r,items:o.filelist_manage_menu}}).parent(this)}})}),i(Nt,[Ue,se,dt,St,Et,De],function(e,t,n,i,r,o){return e.extend({Defaults:{text:"Upload",icon:"upload"},render:function(){function e(){return u.get("selectedFileSystem")&&u.get("selectedFileSystem").isLinked()}function n(){function e(){t.disabled(!u.get("file").get("canWrite"))}var t=this;u.on("change:file",e),e()}function a(){function e(){a.disable(!u.get("file").get("canWrite"))}var t=this,a,s,l,d;l=u.get("userSettings"),n.call(this),i.hasNativeSupport()&&l.force_upload_dialog!==!0?(s=u.get("config"),a=new i(s,l),a.picker(t),e(),a.on("select",function(e){u.get("selectedFiles").reset(),d=(new Date).getTime(),u.set("lastActionTime",d),a.upload(e.files,u.get("file").get("path"))}),a.on("progress",function(e){var t=e.extra.notification;t||(t=new r({type:"progress"}),u.get("notifications").add(t),e.extra.notification=t),e.extra.notification.set("text",["Uploading file {0}",e.name]).set("percent",e.global.loaded/e.global.total*100),e.global.loaded==e.global.total&&e.extra.notification.set("text","Finished uploading.")}),a.on("FileUploaded",function(e){u.get("files").insert(e.file),u.get("lastActionTime")<=d&&u.get("selectedFiles").add(e.file)}),a.on("FilesUploaded",function(e){e.extra.notification&&e.extra.notification.set("percent",100),o.displayErrors(e.errors),l.onupload&&l.onupload({files:e.files})})):t.on("click",function(){c.uploadFiles()})}var s=this,l,u,c;return u=this.data.get("viewModel"),c=this.data.get("actions"),u.get("config").on("change:general.disabled_tools",function(){s.disabled(this.isDisabled("upload"))}),u.get("config").on("change:general.hidden_tools",function(){s.visible(!this.isHidden("upload")&&!e())}),u.on("change:selectedFileSystem",function(){s.visible(!e())}),l=[{text:"Local machine",icon:"upload",onPostRender:a}],t.getInfo()["dropbox.app_id"]&&l.push({text:"Dropbox",icon:"dropbox",onclick:c.dropboxChooser,onPostRender:n}),t.getInfo()["googledrive.client_id"]&&l.push({text:"Google Drive",icon:"cloud-download",onclick:c.googlePicker,onPostRender:n}),t.getInfo()["onedrive.client_id"]&&l.push({text:"OneDrive",icon:"cloud-download",onclick:c.oneDrivePicker,onPostRender:n}),l}})}),i(It,[Ue],function(e){return e.extend({Defaults:{text:"Sort"},render:function(){function e(){i.get("files").sort(this.data.get("column"),"asc")}function t(){this.active(this.data.get("column")===i.get("files").sortBy)}var n=this,i=n.data.get("viewModel");return[{text:"Name",data:{column:"name"},onclick:e,selectable:!0,onPostRender:t},{text:"Size",data:{column:"size"},onclick:e,selectable:!0,onPostRender:t},{text:"Type",data:{column:"extension"},onclick:e,selectable:!0,onPostRender:t},{text:"Modification date",data:{column:"lastModified"},onclick:e,selectable:!0,onPostRender:t}]},bindStates:function(){var e=this,t=e.data.get("viewModel").get("files");t.on("sort",function(){e.menu&&(e.menu.items().active(!1),e.menu.items().each(function(e){e.active(e.data.get("column")===t.sortBy)}))})}})}),i(Mt,[G,yt,se,At,Dt,Nt,It],function(e,t,n,i,r,o,a){return e.extend({Defaults:{layout:"flex",spacing:4,padding:4},render:function(){function e(){var e=this;c.on("change:view",function(t){e.active(t.value)}),e.active(e.data.get("view")===c.get("view"))}function s(){var e=this;c.get("clipboardFiles").on("add remove",function(){e.visible(c.get("clipboardFiles").length>0)}),c.on("change:file",function(){e.disabled(!c.get("file").get("canWrite"))})}function l(){var e=this;c.get("selectedFiles").on("add remove",function(){this.length&&c.get("selectedFileSystem").isLinked()?e.show():e.hide()})}var u,c,d,f,h;return u=this.data.get("actions"),c=this.data.get("viewModel"),d=c.get("userSettings"),t.addButtons({"main.create":function(){return new i({data:{viewModel:c,actions:u,userSettings:d}})},"main.upload":function(){return new o({data:{viewModel:c,actions:u,userSettings:d}})},"main.manage":function(){return new r({data:{viewModel:c,actions:u,userSettings:d}})}}),t.addButtons({"util.refresh":{type:"button",icon:"refresh",tooltip:"Refresh file list",onclick:function(){u.refresh(!0)}},"util.viewmode":{type:"buttongroup",items:[{type:"button",icon:"list",active:!0,tooltip:"List",data:{view:"files"},onclick:u.filesView,onPostRender:e},{type:"button",icon:"thumbs",tooltip:"Thumbnails",data:{view:"thumbs"},onclick:u.thumbsView,onPostRender:e}]},"util.sort":function(){return new a({data:{viewModel:c,actions:u}})},"util.filter":{type:"combobox",icon:"search",flex:1,maxWidth:200,placeholder:"Filter",value:c.get("filter"),onchange:function(){u.filter(this.value())},onPostRender:function(){var e=this;c.on("change:filter",function(t){e.value(t.value)}),c.get("config").on("change:general.disabled_tools",function(){e.disabled(this.isDisabled("filter"))}),c.get("config").on("change:general.hidden_tools",function(){e.visible(!this.isHidden("filter"))})}}}),f=t.createToolbar("main",d.filelist_main_toolbar||"create upload manage"),h=t.createToolbar("util",d.filelist_utils_toolbar||"refresh viewmode sort filter"),[].concat({type:"menubutton",text:"Manage",icon:"manage",visible:!1,menu:[{text:"Goto file",onclick:u.gotoLink},{text:"Remove link",onclick:u.deleteFiles}],onPostRender:l},f,[{type:"button",text:"Paste",icon:"paste",onclick:u.paste,visible:!1,onPostRender:s},{type:"spacer",flex:1}],h,{type:"button",icon:"logout",tooltip:"Logout",onclick:u.logout,hidden:!n.isStandalone()})}})}),i(Ft,[G],function(e){return e.extend({render:function(){function e(){var e=this,i=t.get("fileSystems");i.on("add",function(t){var n=t.items,i,r,o,a;for(i=0;i<n.length;i++)a=n[i].get("meta"),r=a["ui.icon_16x16"]||"folder",o=a["ui.icon_16x16_url"],e.add({text:n[i].get("name"),icon:r,iconUrl:o,data:{cid:n[i].cid}});e.renderNew()}),i.on("remove",function(t){function n(t){e.items().filter(function(e){return e.data.get("cid")===t.cid}).remove()}var i,r=t.items;for(i=0;i<r.length;i++)n(r[i])}),t.on("change:selectedFileSystem",function(t){e.items().selected(!1).filter(function(e){return t.value&&e.data.get("cid")===t.value.cid}).selected(!0)}),e.on("click",function(e){n.selectFileSystem(i.get(e.control.data.get("cid")))})}var t=this.data.get("viewModel"),n=this.data.get("actions");return[{type:"treeview",classes:"overflow-y",onPostRender:e}]}})}),i(Pt,[I,rt,T],function(e,t,n){return e.extend({Defaults:{minWidth:1,minHeight:1},init:function(e){var t=this,n,i,r,o,a;for(t._super(e),t.classes.add("filelist"),r=t.settings.columns,n=0;n<r.length;n++)if(o=r[n].width,"number"==typeof o&&(o>0&&1>o?o=100*o+"%":o+="px"),r[n].width=o,r[n].classes){for(a=r[n].classes.split(" "),i=0;i<a.length;i++)a[i]=t.classPrefix+a[i];r[n].classes=a.join(" ")}t.rows=t.data.get("rows"),t.selection=t.data.get("selection")},renderHeadHtml:function(){var e=this,t,n=[],i=e.settings.columns,r,o,a,s,l=e.classPrefix,u;for(n.push('<div class="'+l+'filelist-head" unselectable="true"><table id="'+e._id+'-head" class="'+l+'filelist-head"><tbody><tr id="'+e._id+'-headr">'),t=0;t<i.length;t++)r=i[t],o=r.width,u="",r.sorting!==!1&&"checkbox"!=r.type&&(a='<i class="'+l+'caret"></i>'),s="checkbox"==r.type?'<i class="'+l+"ico "+l+'i-checkbox" unselectable="on"></i>':e.encode(r.title)||"&nbsp;",r.classes&&(u=" "+r.classes),n.push('<td class="'+l+"filelist-head-item"+u+'"',o?' style="width: '+o+'"':"",'><div class="'+l+"txt "+l+'filelist-cell">',s,a,"</div></td>");return n.push("</tr></tbody></table></div>"),n.join("")},renderBodyHtml:function(){var e=this,t=[],n,i=e.classPrefix,r,o=e.settings.columns,a;for(t.push('<div id="'+e._id+'-body" class="'+i+'filelist-body"><table>'),t.push('<thead id="'+e._id+'-thead">'),r=0;r<o.length;r++)n=o[r].width,a="",o[r].classes&&(a=o[r].classes),t.push('<td class="'+a+'"',n?' style="width: '+o[r].width+'"':"",">",'<div class="'+i+"txt "+i+'filelist-cell">',r,"</div></td>");return t.push("</thead>"),t.push('<tbody id="'+e._id+'-tbody">'),t.push(e.renderRowsHtml(e.rows)),t.push("</tbody></table></div>"),t.join("")},renderRowsHtml:function(e){function t(e){return!!n.selection.get(e)}var n=this,i,r,o=n.settings.columns,a=[],s,l,u,c,d=n.classPrefix,f;if(!n.rows)return"";for(i=0;i<e.length;i++){for(s=e[i],a.push('<tr class="'+d+"filelist-row",t(s)?" "+d+"checked":"",'" data-id="'+s.id+'">'),r=0;r<o.length;r++)l=o[r],u=s.get(l.name),l.filter&&(u=l.filter(u,s)),"checkbox"==l.type?f=u===!1?"&nbsp;":'<i class="'+d+"ico "+d+'i-checkbox" unselectable="on"></i>':(c=l.icon?'<i class="'+d+"ico "+d+"i-"+l.icon(u,s)+'" unselectable="on"></i>':"",u=n.encode(""+u),f=c+'<span class="'+d+"txt "+d+'reset" title="'+u+'">'+u+"</span>"),a.push(l.classes?'<td class="'+l.classes+'">':"<td>"),a.push('<div class="'+d+"txt "+d+'filelist-cell">'+f+"</div></td>");a.push("</tr>")}return a.join("")},renderHtml:function(){var e=this;return'<div id="'+e._id+'" class="'+e.classes+'">'+e.renderHeadHtml()+e.renderBodyHtml()+"</div>"},bindStates:function(){function e(){p||d.rows.next()}function i(e,t){var n,i,r;if(i=d.rows.indexOf(e),r=d.rows.indexOf(t),-1!==i&&-1!==r){if(r>i)for(n=i;r>=n;n++)d.selection.add(d.rows[n]);else for(n=r;i>=n;n++)d.selection.add(d.rows[n]);d.focusRow=t}}function r(e){return d.settings.selectableFilter?d.settings.selectableFilter(e):!0}function o(e,t){var i,r,o,a,s;for(i=document.createElement("div"),i.innerHTML="<table><tbody>"+d.renderRowsHtml(e)+"</tbody></table>",o=i.firstChild.firstChild,r=d.getEl("tbody"),s=r.childNodes;a=o.lastChild;)s[t]?n(s[t]).before(a):r.appendChild(a)}function a(e){var t,n,i;for(t=0;t<e.length;t++)for(n=d.getEl("tbody").firstChild;n;n=n.nextSibling)n.getAttribute("data-id")===""+e[t].id&&(i=n,n=n.previousSibling||n.parentNode.firstChild,i.parentNode.removeChild(i));l()}function s(e,t){var i,r;for(i=0;i<e.length;i++)for(r=d.getEl("tbody").firstChild;r;r=r.nextSibling)r.getAttribute("data-id")===""+e[i].id&&n(r).toggleClass(d.classPrefix+"checked",t);n(d.getEl("thead").getElementsByTagName("i")[0]).toggleClass(d.classPrefix+"checked",t),l()}function l(){var e,t,i=d.classPrefix,o,a=d.selection.length,s=d.rows.filter(r).length;for(t=d.settings.columns,e=0;e<t.length;e++)"checkbox"==t[e].type&&(o=d.getEl("headr").childNodes[e].firstChild.firstChild,a&&a==s?(n(o).removeClass(i+"inter"),n(o).addClass(i+"checked")):a?(n(o).removeClass(i+"checked"),n(o).addClass(i+"inter")):(n(o).removeClass(i+"checked"),n(o).removeClass(i+"inter")))}function u(){var e,t,i=d.classPrefix,r,o=d.rows.sortOrder;for(t=d.settings.columns,e=0;e<t.length;e++)r=t[e].name===d.rows.sortBy,n(d.getEl("headr").childNodes[e]).find("."+i+"caret").toggleClass(i+"up",r&&"asc"==o).toggleClass(i+"down",r&&"desc"==o)}function c(){return d.getEl("body").scrollHeight>d.getEl().clientHeight}var d=this,f=d.$el.children().last(),h,p,m=400;d._super(),d.rows.on("add",function(t){d.visible()&&(o(t.items,t.index),t.items.complete||c()||e())}).on("remove",function(e){d.visible()&&a(e.items)}),d.selection.on("add",function(e){d.visible()&&s(e.items,!0)}).on("remove",function(e){d.visible()&&s(e.items,!1)}),d.on("click dblclick contextmenu",function(e){var t,n,o,a,s,l=e.target,u,c;"contextmenu"==e.type&&e.preventDefault();do if(1==l.nodeType){if(t=l.getAttribute("data-id"),c=c||"DIV"==l.tagName,"TD"==l.tagName){for(u=l.parentNode.firstChild,s=0;u&&u!=l;s++,u=u.nextSibling);a=d.settings.columns[s]}if(t){for(n=d.rows,s=0;s<n.length;s++)if(o=n[s],o.id===t){if("contextmenu"==e.type)return void(r(o)&&(d.selection.get(o)||(d.selection.splice(0),d.selection.add(o),d.focusRow=o),d.fire("contextRowSelect",e)));if("link"==a.type&&c){var f=d.fire("open",{action:e.type,value:o.get(a.name),row:o});if(f.isDefaultPrevented())return}if(r(o)){if(e.shiftKey&&d.selection.length&&d.focusRow)return void i(d.focusRow,o);"checkbox"!=a.type&&d.selection.splice(0),d.selection.get(o)?d.selection.remove(o):(d.selection.add(o),d.focusRow=o)}return}return}}while((l=l.parentNode)&&l!=d.getEl());a&&("checkbox"==a.type?d.selection.length>0?d.selection.splice(0):d.selection.add(d.rows.filter(r)):d.rows.sort(a.name,"desc"==d.rows.sortOrder?"asc":"desc"))}),d.rows.on("loading",function(e){var n;p=e.state,e.state?(n=d.rows.offset>0,h=new t(f[0],n),h.show(500)):h.hide()}),d.rows.on("sort",function(){u()}),u(),f.on("scroll",function(){f[0].scrollTop+f[0].clientHeight+m>=f[0].scrollHeight&&e()}),d.state.on("change:visible",function(e){e.value&&(n(d.getEl("tbody")).empty(),o(d.rows,0),l())})},repaint:function(){var e=this,t=e.layoutRect(),n,i;e._super(),n=e.getEl("body").style,n.width=t.innerW+"px",n.height=t.innerH-e.getEl("head").offsetHeight+"px",i=e.getEl("thead").offsetWidth,i&&(e.getEl("head").style.width=i+"px")}})}),i(Ot,[I,rt,T],function(e,t,n){return e.extend({Defaults:{minWidth:1,minHeight:1},init:function(e){var t=this;t._super(e),t.classes.add("thumbnailview"),t.rows=t.data.get("rows"),t.selection=t.data.get("selection")},renderBodyHtml:function(e){var t,n=[],i,r=this.classPrefix,o,a,s,l,u=this.settings,c,d,f,h,p;for(c=u.iconFilter,d=u.thumbFilter,f=u.selectableFilter,t=0;t<e.length;t++)i=e[t],o=i.get("name"),h=this.selection.get(i),a="",l="folder",d&&(a=d(o,i)),!a&&c&&(l=c(o,i)),s=a?'<img src="'+a+'">':'<i class="'+r+"ico "+r+"thumb "+r+"i-"+l+'"></i>',o=this.encode(o),p=!f||f(i)?'<i class="'+r+"ico "+r+'i-checkbox"></i>':"",n.push('<div class="'+r+"thumb"+(h?" "+r+"checked":"")+'" unselectable="on" data-id="'+e[t].id+'">',s,'<div class="'+r+'info" title="'+o+'">',p,o,"</div>","</div>");return n.join("")},renderHtml:function(){var e=this,t=e.classPrefix;return'<div id="'+e._id+'" class="'+e.classes+'"><div id="'+e._id+'-body" class="'+t+"container-body "+t+'flow-layout">'+e.renderBodyHtml(e.rows)+"</div></div>"},bindStates:function(){function e(){c||a.rows.next()}function i(e){var t=[];return l.children().each(function(n,i){for(n=0;n<e.length;n++)i.getAttribute("data-id")===""+e[n].id&&t.push(i)}),t}function r(e,t){var i=l.children()[t];i?n(i).before(a.renderBodyHtml(e)):l.append(a.renderBodyHtml(e))}function o(){return a.getEl("body").scrollHeight>a.getEl().clientHeight}var a=this,s=a.$el,l=s.children().last(),u,c,d=400,f=a.settings.selectableFilter;a.rows.on("add",function(t){a.visible()&&(r(t.items,t.index),t.items.complete||o()||e())}).on("remove",function(e){a.visible()&&n(i(e.items)).remove()}),a.selection.on("add remove",function(e){function t(t){a.visible()&&n(i([t])).toggleClass(a.classPrefix+"checked","add"==e.type)}var r;for(r=0;r<e.items.length;r++)t(e.items[r])}),a.on("click dblclick contextmenu",function(e){var t,i;if("contextmenu"==e.type&&e.preventDefault(),t=n(e.target).closest("[data-id]").attr("data-id")){if(i=a.rows.get(t),"contextmenu"==e.type)return a.selection.get(i)||(a.selection.splice(0),a.selection.add(i)),void a.fire("contextRowSelect",e);if(f(i)&&n(e.target).closest("."+a.classPrefix+"info").length)a.selection.get(i)?a.selection.remove(i):a.selection.add(i);else{var r=a.fire("open",{action:e.type,value:i.get("name"),row:i});if(r.isDefaultPrevented())return;a.selection.splice(0),a.selection.add(i)}}}),a.rows.on("loading",function(e){var n;c=e.state,a.visible()&&(e.state?(n=a.rows.offset>0,u=new t(l[0],n),u.show(500)):u.hide())}),a.state.on("change:visible",function(e){e.value&&(l.empty(),r(a.rows,0))}),s.on("scroll",function(){s[0].scrollTop+s[0].clientHeight+d>=s[0].scrollHeight&&e()})}})}),i(Ht,[G,Ve,Pt,Ot,kt,c,ft,ct,ue,De,St,Et,C],function(e,t,n,i,r,o,a,s,l,u,c,d,f){function h(e){var t,n,i;for(t in e)if(n=t.split(" "),n.length>1){for(i=0;i<n.length;i++)e[n[i]]=e[t];delete e[t]}return e}function p(e,t){return t.get("isFile")?"number"!=typeof e?"-":a.format(e):""}function m(e,t){return t.get("isFile")?l.extname(t.get("name")).toLowerCase().substr(1):"dir"}function g(e){return e&&0!==e.getTime()?s.dateTime(e):"-"}function v(e,t){return t.get("isFile")?t.get("isParent")?"parent":y[l.extname(e).substr(1)]||"file":"folder"}var y=h({txt:"file-text",doc:"file-word",pdf:"file-pdf",xls:"file-excel","html htm xml php aspx":"file-xml",zip:"file-zip","jpeg jpg gif png":"file-image"});return e.extend({Defaults:{layout:"flex",direction:"column",align:"stretch"},render:function(){function e(){return C.get("file").get("canWrite")&&!A.isDisabled("upload")}function o(e){T.list(e.value.path)}function a(e){var t,n=e.row;if(n.get("isFile")){if(t=S.filelist_insert_event||"click",!f.desktop||t!=e.action)return;T.insert(n)}else T.list(n.get("meta").link||n.get("path"));e.preventDefault()}function s(e){R||(R=new t({items:[{text:"Goto file",onclick:T.gotoLink},{text:"Remove link",onclick:T.deleteFiles}],oncancel:function(){E.hide()}}),R.renderTo(document.body)),R.show().moveTo(e.pageX,e.pageY).getEl().focus()}function h(e){var t=C.get("selectedFileSystem");return e.preventDefault(),t.get("meta").linked?void s(e):void(A.isDisabled("manage")||A.isHidden("manage")||0!==C.get("selectedFiles").length&&(E||(E=new r({data:{viewModel:C,actions:T,items:S.filelist_context_menu},oncancel:function(){E.hide()}}),E.renderTo(document.body)),E.show().moveTo(e.pageX,e.pageY).getEl().focus()))}function y(e,t){var n,i=t.get("meta").thumb_url;return!i&&t.get("canPreview")&&(n=t.get("meta").link||t.get("path"),i=f.apiPageUrl+"?action=streamfile&stream=true&path="+encodeURIComponent(n)+"&thumb=true&u="+t.get("size")),i}function x(e,t){return".."!==t.get("name")}function b(e){return".."!==e.get("name")}function w(){C.set("lastActionTime",(new Date).getTime())}var _=this,E,R,C,T,S,A;return C=_.data.get("viewModel"),T=_.data.get("actions"),S=C.get("userSettings"),A=C.get("config"),C.on("change:path",function(e){var t=e.value,n=[],i;if(i=C.get("fileSystems").findByPath(t)){for(;t&&l.isChildOf(t,i.get("path"));)n.push({name:l.basename(t),path:t}),t=l.dirname(t);n.reverse(),n[0].name=i.get("name"),_.find("path")[0].row(n)}}),C.on("change:view",function(e){var t=_.find("#list"),n=_.find("#thumbs");"thumbs"==e.value?(t.hide(),n.show()):(t.show(),n.hide())}),_.on("remove",function(){E&&E.remove(),R&&R.remove()}),_.on("PostRender",function(){var t,n,i,r;t=C.get("config"),n=C.get("userSettings"),i=new c(t,n),i.dropzone(this),i.on("drop",function(t){return t.files?(C.get("selectedFiles").reset(),r=(new Date).getTime(),C.set("lastActionTime",r),void(t.files.length&&e()&&i.upload(t.files,C.get("path")))):void t.preventDefault()}),i.on("progress",function(e){var t=e.extra.notification;t||(t=new d({type:"progress"}),C.get("notifications").add(t),e.extra.notification=t),e.extra.notification.set("text",["Uploading file {0}",e.name]).set("percent",e.global.loaded/e.global.total*100),e.global.loaded==e.global.total&&e.extra.notification.set("text","Finished uploading.")}),i.on("FileUploaded",function(e){C.get("files").insert(e.file),C.get("lastActionTime")<=r&&C.get("selectedFiles").add(e.file)}),i.on("FilesUploaded",function(e){e.extra.notification&&e.extra.notification.set("percent",100),u.displayErrors(e.errors),n.onupload&&n.onupload({files:e.files})})}),[{type:"panel",flex:1,align:"stretch",border:"0 0 0 0",layout:"flex",direction:"column",autoResize:!1,items:[{type:"path",delimiter:"/",onselect:o},new n({name:"list",border:"1 0 0 0",flex:1,onopen:a,hidden:"files"!=C.get("view"),onContextRowSelect:h,selectableFilter:b,columns:[{type:"checkbox",classes:"checkbox-column",filter:x},{title:"Filename",name:"name",icon:v,type:"link"},{title:"Size",name:"size",width:60,filter:p},{title:"Type",name:"extension",width:70,filter:m},{title:"Modification date",name:"lastModified",width:200,filter:g}],onClick:w,data:{rows:C.get("files"),selection:C.get("selectedFiles")}}),new i({name:"thumbs",border:"1 0 0 0",flex:1,onopen:a,iconFilter:v,thumbFilter:y,selectableFilter:b,hidden:"thumbs"!=C.get("view"),onContextRowSelect:h,onClick:w,data:{rows:C.get("files"),selection:C.get("selectedFiles")}})]}]}})}),i(Lt,[O,De,yt,ue,c,Me,xt],function(e,t,n,i,r,o,a){var s=e.extend({Defaults:{layout:"border"},render:function(){function e(e){T.find("imagecanvas")[0].zoom(e.value)}function s(){T.find("#zoom").value(S.zoom())}function l(e){return function(){if(S.cancel(),T.find("form:visible slider").each(function(e){e.reset()}),s(),"crop"==e&&S.crop(),"redeye"==e&&S.redeye(20),("grayscale"==e||"invert"==e||"sharpen"==e||"emboss"==e)&&S.filter(e).apply(),"fliprotate"==e&&S.startTransation(),"resize"==e&&(k=S.size().w,D=S.size().h,T.find("#rw").value(S.size().w),T.find("#rh").value(S.size().h)),C=e,T.find("panel form").hide(),T.find("#"+e)[0].show(),"save"==e){R=i.extname(E.path);var t=i.basename(E.path,R);T.find("#saveas").value(t),T.find("#saveas")[0].focus()}}}function u(e,n){S.saveAs(e,function(e){var t=o.fromJSON(e);E.onsave&&E.onsave({file:t}),_.fire("CloseView")},function(n){switch(n.args=[e],n.code){case a.FILE_EXISTS:n.message='The "{0}" file/directory already exists.'}t.displayError(n)},n)}function c(){u(T.find("#saveas").value()+R)}function d(){u(i.basename(E.path,R),"overwrite")}function f(){E.show_save_as?l("save")():d()}function h(e){var t=e.control;T.find("#undo,#revert").disabled(!t.canUndo()),T.find("#redo").disabled(!t.canRedo()),E.show_save_as||T.find("#savebtn").disabled(!t.canUndo())}function p(){var e=T.find("treeview")[0];e.find("*").each(function(e){e.selected(!1)}),"resize"==C&&(y(),S.resize(T.find("#rw").value(),T.find("#rh").value())),S.apply(),l("main")(),A=null}function m(){var e=T.find("treeview")[0];e.find("*").each(function(e){e.selected(!1)}),this.parent().find("slider").exec("reset"),l("main")(),A=null}function g(e){return function(){var t=this;t.on("dragstart",function(){A=S.filter(e)}),t.on("drag",function(e){A.live(e.value)}),t.on("dragend",function(e){A.apply(e.value)})}}function v(){var e=this.find("slider");e.each(function(t){t.on("dragstart",function(){A=A||S.filter("colorize")}),t.on("drag",function(){A.live(e[0].value(),e[1].value(),e[2].value())}),t.on("dragend",function(){A.apply(e[0].value(),e[1].value(),e[2].value())})})}function y(){var e,t,n,i;e=T.find("#rw")[0],t=T.find("#rh")[0],n=e.value(),i=t.value(),T.find("#constrain")[0].checked()&&k&&D&&n&&i&&(n!=k?(i=Math.round(n/k*i),t.value(i)):(n=Math.round(i/D*n),e.value(n))),k=n,D=i}function x(){S.crop(parseInt(T.find("#x")[0].value(),10),parseInt(T.find("#y")[0].value(),10),parseInt(T.find("#w")[0].value(),10),parseInt(T.find("#h")[0].value(),10))}function b(e,t){var i="undefined"!=typeof E[e]?E[e]:t;return n.createToolbar("editimage",i)}function w(){var e=this.parent().find("treeview")[0].items();this.hasCanvasSupport()?this.hasWebGlSupport()||e.eq(-1)[0].find("#triangleblur,#hue,#sharpen,#emboss").hide():(e.eq(-1).hide(),this.parent().find("#undo,#redo").hide(),this.parent().find("#undo")[0].parent()._layout.applyClasses(this.parent().find("#undo")[0].parent().items()))}var _=this,E,R,C,T,S,A;E=_.data.get("settings")||{};var k,D;n.addButtons({"editimage.save":{name:"savebtn",text:"Save",icon:"save",onclick:f,disabled:!E.show_save_as
},"editimage.revert":{name:"revert",text:"Revert",disabled:!0,onclick:function(){S.revert()}},"editimage.undo":{name:"undo",text:"Undo",icon:"undo",disabled:!0,onclick:function(){S.undo()}},"editimage.redo":{name:"redo",text:"Redo",icon:"redo",disabled:!0,onclick:function(){S.redo()}},"editimage.zoomfit":{text:"Zoom fit",type:"button",onclick:function(){e({value:"fit"}),s()}},"editimage.zoom":{type:"slider",name:"zoom",label:"Zoom",ondrag:e,minValue:0,maxValue:7,previewFilter:function(e){return Math.round(100*e)+"%"}}});var N=[{name:"main",hidden:!1,items:[].concat({type:"buttongroup",items:b("editimage_main_toolbar","save revert undo redo")},{type:"spacer",flex:1},b("editimage_zoom_toolbar","zoomfit zoom"))},{name:"save",hidden:!0,onsubmit:c,items:[{name:"saveas",label:"Save as",type:"textbox",minWidth:350},{type:"button",text:"Save",subtype:"primary",onclick:c},{type:"button",text:"Cancel",onclick:m}]},{name:"redeye",hidden:!0,items:[{label:"Radius",type:"slider",onDrag:function(e){S.redeye(e.value)}},{type:"button",text:"Apply",subtype:"primary",onclick:p},{type:"button",text:"Reset",onclick:m}]},{name:"crop",hidden:!0,items:[{label:"X",name:"x",type:"textbox",size:4,value:"0",onchange:x},{label:"Y",name:"y",type:"textbox",size:4,value:"0",onchange:x},{label:"W",name:"w",type:"textbox",size:4,value:"0",onchange:x},{label:"H",name:"h",type:"textbox",size:4,value:"0",onchange:x},{type:"button",text:"Apply",subtype:"primary",onclick:"submit"},{type:"button",text:"Reset",onclick:m}],onsubmit:p},{name:"resize",hidden:!0,items:[{label:"W",name:"rw",type:"textbox",size:4,onchange:y},{label:"H",name:"rh",type:"textbox",size:4,onchange:y},{name:"constrain",checked:!0,text:"Constrain proportions",type:"checkbox"},{type:"button",text:"Apply",subtype:"primary",onclick:"submit"},{type:"button",text:"Reset",onclick:m}],onsubmit:p},{name:"fliprotate",hidden:!0,items:[{type:"button",icon:"flip-h",tooltip:"Flip H",onclick:function(){S.flip("h")}},{type:"button",icon:"flip-v",tooltip:"Flip V",onclick:function(){S.flip("v")}},{type:"button",icon:"rotate-left",tooltip:"Rotate left",onclick:function(){S.rotate(-90)}},{type:"button",icon:"rotate-right",tooltip:"Rotate right",onclick:function(){S.rotate(90)}},{type:"button",text:"Apply",subtype:"primary",onclick:p},{type:"button",text:"Reset",onclick:m}]}],I=[{name:"brightness",title:"Brightness",min:-1,max:1,value:0},{name:"contrast",title:"Contrast",min:-1,max:1,value:0},{name:"exposure",title:"Exposure",min:0,max:2,value:2},{name:"gamma",title:"Gamma",min:0,max:2,value:1},{name:"hue",title:"Hue",min:-1,max:1,value:0},{name:"saturate",title:"Saturate",min:-1,max:1,value:0},{name:"sepia",title:"Sepia",min:0,max:1,value:0},{name:"vibrance",title:"Vibrance",min:-1,max:1,value:0},{name:"triangleblur",title:"TriangleBlur",min:0,max:200,value:0},{name:"colorize",title:"Colorize",min:0,max:2,value:1},{name:"grayscale",title:"Grayscale"},{name:"invert",title:"Invert"},{name:"sharpen",title:"Sharpen"},{name:"emboss",title:"Emboss"}],M=r.makeMap(r.deepSplit(E.editimage_actions_toolbar||"resize crop fliprotate brightness contrast exposure gamma hue saturate sepia vibrance triangleblur colorize grayscale invert sharpen emboss"));r.each(I,function(e){N.push("colorize"==e.name?{name:"colorize",hidden:!0,defaults:{minValue:e.min,maxValue:e.max,value:e.value},onPostRender:v,items:[{type:"slider",label:"Red",name:"r"},{type:"slider",label:"Green",name:"g"},{type:"slider",label:"Blue",name:"b"},{type:"button",text:"Apply",subtype:"primary",onclick:p},{type:"button",text:"Reset",onclick:m}]}:"undefined"==typeof e.value?{name:e.name,hidden:!0,items:[{text:e.title,type:"label",strong:!0},{type:"button",text:"Apply",subtype:"primary",onclick:p},{type:"button",text:"Reset",onclick:m}]}:{name:e.name,items:[{type:"slider",label:e.title,minValue:e.min,maxValue:e.max,value:e.value,onPostRender:g(e.name)},{type:"button",text:"Apply",subtype:"primary",onclick:p},{type:"button",text:"Reset",onclick:m}]})});var F=[];r.each(I,function(e){M[e.name.toLowerCase()]&&F.push({name:e.name,text:e.title,onclick:l(e.name)})});var P=[],O=[{text:"Resize",name:"resize",onclick:l("resize")},{text:"Crop",name:"crop",onclick:l("crop")},{text:"Flip/Rotate",name:"fliprotate",onclick:l("fliprotate")}];return r.each(O,function(e){M[e.name.toLowerCase()]&&P.push(e)}),_.on("postRender",function(){T=this,T.find("imagecanvas")[0].loadFromPath(E.path,function(e){this.zoom("fit"),s(),this.hasCanvasSupport()&&this._imageEditor.loadFromImage(e)}),S=T.find("imagecanvas")[0],S.on("cropchange",function(e){T.find("#x").value(e.rect.x),T.find("#y").value(e.rect.y),T.find("#w").value(e.rect.w),T.find("#h").value(e.rect.h)})}),[{region:"north",type:"panel",layout:"flex",padding:4,border:"0 0 1 0",defaults:{type:"form",direction:"row",spacing:4,labelGap:3,padding:3,align:"center",hidden:!0},items:N},{region:"west",type:"panel",width:200,border:"0 1 0 0",items:[{type:"treeview",items:[{text:"Alter",fixed:!0,items:P,hidden:!P.length},{text:"Filters",fixed:!0,items:F,hidden:!F.length}]}]},{region:"center",type:"imagecanvas",onchange:h,onPostRender:w,layout:"fit",canEdit:".gif"!=i.extname(E.path).toLowerCase()}]}});return s.renderAsDialog=function(e){t.renderViewAsDialog(s,{title:"Edit image",width:800,height:600,layout:"fit",buttons:[{text:"Close",onclick:"close"}],fullscreen:"auto"},e)},s}),i(zt,[V,De,Me],function(e,t,n){var i=e.extend({render:function(){var e=this,i=e.data.get("settings");return e.on("postRender",function(){n.getContents(i.path).then(function(t){e.find("#content").value(t)})}),e.on("submit",function(){var r=e.find("#content").value();n.putContents(i.path,r).then(function(t){i.onsave&&i.onsave({file:t,content:r}),e.fire("CloseView")},function(e){t.displayError(e)})}),{type:"textbox",name:"content",multiline:!0,spellcheck:!1,minWidth:500,minHeight:400,flex:1}}});return i.renderAsDialog=function(e){t.renderViewAsDialog(i,{title:"Edit text",buttons:[{text:"Save",subtype:"primary",onclick:"submit"},{text:"Close",onclick:"close"}],fullscreen:"auto",layout:"flex",align:"stretch"},e)},i}),i(Bt,[V,De,Me,xt,ue],function(e,t,n,i,r){var o=e.extend({render:function(){function e(){o.find("#name")[0].focus()}var o=this,a=this.data.get("settings");return this.on("submit",function(s){var l=r.join(a.path,s.data.name);return r.isValidFileName(s.data.name)?void n.createDirectory(l,a.template).then(function(e){a.oncreate&&a.oncreate({file:e}),o.fire("CloseView")},function(n){switch(n.args=[s.data.name],n.code){case i.FILE_EXISTS:n.message='The "{0}" file/directory already exists.'}t.displayError(n,e)}):void t.displayError("Please specify a valid file name.",e)}),[{label:"Name",name:"name",type:"textbox",minWidth:300}]}});return o.renderAsDialog=function(e){t.renderViewAsDialog(o,{title:"Create new folder",buttons:[{text:"Create",subtype:"primary",onclick:"submit"},{text:"Cancel",onclick:"close"}]},e)},o}),i(Wt,[V,De,Me,ue,xt],function(e,t,n,i,r){function o(e,t){var n,i;if(e)for(e=e.split(/\s*,\s*/),n=0;n<e.length;n++)i=e[n].split(/\s*=\s*/),t(i[0],i[1]||i[0])}var a=e.extend({render:function(){function e(){a.find("#name")[0].focus()}var a=this,s=this.data.get("settings"),l=[];return o(s.fields,function(e,t){l.push({label:e,name:"field_"+t,type:"textbox",minWidth:300})}),this.on("submit",function(o){var l,u={},c=o.data,d=i.join(s.path,c.name);if(!i.isValidFileName(o.data.name))return void t.displayError("Please specify a valid file name.",e);for(l in c)0===l.indexOf("field_")&&(u[l.substr(6)]=c[l]);n.createDocument(d,s.template,u).then(function(e){s.oncreate&&s.oncreate({file:e}),a.fire("CloseView")},function(n){switch(n.args=[c.name],n.code){case r.FILE_EXISTS:n.message='The "{0}" file/directory already exists.'}t.displayError(n,e)})}),[{label:"Name",name:"name",type:"textbox",minWidth:300}].concat(l)}});return a.renderAsDialog=function(e){t.renderViewAsDialog(a,{title:"Create new document",buttons:[{text:"Create",subtype:"primary",onclick:"submit"},{text:"Cancel",onclick:"close"}]},e)},a}),i(Ut,[V,De,Me,xt,ue],function(e,t,n,i,r){var o=e.extend({render:function(){function e(){o.find("#newname")[0].focus()}var o=this,a,s,l="",u;return a=o.data.get("settings"),s=a.from,s&&s.get("isFile")?(l=r.extname(s.get("name")),u=r.basename(s.get("name"),l)):u=s.get("name"),o.on("submit",function(u){var c=u.data.newname,d,f;return r.isValidFileName(c)?(d=s.get("path"),f=r.join(r.dirname(s.get("path")),c+l),void n.moveTo(d,f).then(function(e){a.onrename&&a.onrename({file:e}),o.fire("CloseView")},function(n){switch(n.args=[c],n.code){case i.FILE_EXISTS:n.message='The "{0}" file/directory already exists.'}t.displayError(n,e)})):void t.displayError("Please specify a valid file name.",e)}),[{label:"Filename",name:"newname",type:"textbox",minWidth:300,value:u}]}});return o.renderAsDialog=function(e){t.renderViewAsDialog(o,{title:"Rename",buttons:[{text:"Ok",subtype:"primary",onclick:"submit"},{text:"Close",onclick:"close"}]},e)},o}),i(jt,[V,De,Me,xt,ue],function(e,t,n,i,r){var o=e.extend({render:function(){function e(){n.find("#name")[0].focus()}var n=this,o,a,s;return o=n.data.get("settings"),a=o.to,s=o.selectedFiles,n.on("submit",function(l){var u=l.data.name;return r.isValidFileName(l.data.name)?void s.zip(r.join(a,u)).then(function(e){o.onzip&&o.onzip({file:e}),n.fire("CloseView")},function(n){switch(n.args=[u],n.code){case i.FILE_EXISTS:n.message='The "{0}" file/directory already exists.'}t.displayError(n,e)}):void t.displayError("Please specify a valid file name.",e)}),[{label:"Filename",name:"name",type:"textbox",minWidth:300}]}});return o.renderAsDialog=function(e){t.renderViewAsDialog(o,{title:"Zip",buttons:[{text:"Ok",subtype:"primary",onclick:"submit"},{text:"Close",onclick:"close"}]},e)},o}),i(Vt,[O,De,C],function(e,t,n){var i=e.extend({render:function(){var e=this.data.get("settings"),t;return t=n.apiPageUrl+"?action=streamfile&path="+encodeURIComponent(e.path),e.unique&&(t+="&u="+e.unique),[{type:"iframe",url:t}]}});return i.renderAsDialog=function(e){t.renderViewAsDialog(i,{title:"View",minWidth:800,minHeight:600,layout:"fit",buttons:[{text:"Close",onclick:"close"}],fullscreen:"auto"},e)},i}),i(qt,[O,De,C,ue,c,Oe],function(e,t,n,i,r,o){var a=e.extend({Defaults:{layout:"border",defaults:{type:"panel",layout:"fit"},minWidth:800,minHeight:600},render:function(){var e,t=[],r=[],o=0,a,s,l,u,c;if(s=this.data.get("settings").path,u=this.data.get("settings").files,u.length>1){for(e=0;e<u.length;e++)l=u[e],c=l.get("lastModified").getTime(),l.get("path")===s&&(o=e),t.push({url:n.apiPageUrl+"?action=streamfile&path="+encodeURIComponent(l.get("path"))+"&u="+c}),r.push({type:"thumb",text:i.basename(s[e]),url:n.apiPageUrl+"?action=streamfile&path="+encodeURIComponent(l.get("path"))+"&thumb=true&u="+c});a={region:"south",height:120,layout:"flex",pack:"center",align:"center",autoScroll:!0,padding:10,spacing:10,onclick:function(e){this.parent().items().eq(0)[0].index(this.items().indexOf(e.control))},items:r,onPostRender:function(){this.items()[o].active(!0)}}}else o=0,t.push({url:n.apiPageUrl+"?action=streamfile&path="+encodeURIComponent(u[0].get("path"))+"&u="+u[0].get("lastModified").getTime()});return[{region:"center",type:"carousel",data:t,selectedIndex:o,onPostRender:function(){var e=this;e.on("change",function(t){var n;u.length>1&&(n=e.parent().items().eq(-1)[0].items(),n.eq(t.index)[0].active(!0).scrollIntoView("center"),n.eq(t.lastIndex).active(!1))})}},a]}});return a.renderAsDialog=function(e){function n(e){return/^(jpe?g|gif|png)$/i.test(i.extname(e).substr(1))}var s,l,u;for(l=e.path,u=e.paths||[],-1===r.inArray(l,u)&&u.unshift(l),s=0;s<u.length;s++)n(u[s])||(u.splice(s,1),s--);o.getFiles(u).then(function(n){t.renderViewAsDialog(a,{title:"View",minWidth:800,minHeight:600,layout:"fit",fullscreen:"auto",buttons:[{text:"Close",onclick:"close"}]},r.extend({files:n},e))})},a}),i(Xt,[V,De,C,St,re,ue,c,se,dt,ft],function(e,t,n,i,r,o,a,s,l,u){var c=e.extend({Defaults:{minWidth:600,padding:10,spacing:10},render:function(){function e(e){function t(e){for(var t=o.extname(e).substr(1).toLowerCase(),n=0;n<c.length;n++)if("*"==c[n]||c[n]==t)return!0;return!1}var n,i=r.find("#dragmsg"),a=r.find("#files")[0],s=0,l;for(r.parents().filter("window").each(function(e){e.statusbar.find("#upload").disabled(!1)}),i.hide(),a.show(),n=0;n<e.length;n++)l="",t(e[n].name)||(l="Invalid extension"),a.append({type:"container",layout:"flex",spacing:10,align:"stretch",classes:"upload-row",border:"0 0 1 0",padding:"7 15 7 15",items:[{type:"label",flex:1,maxWidth:350,minHeight:25,text:{raw:e[n].name}},{type:"spacer",flex:1},l?{type:"label",text:"Error",tooltip:l,maxWidth:150,classes:"upload-error error"}:{type:"progress",minWidth:150}]}),f.push(l?{error:l,size:0}:e[n]);for(n=0;n<f.length;n++)s+=f[n].size;r.find("#filecount")[0].text(["Files: {0}",f.length+" ("+u.format(s)+")"]).parent().reflow()}function n(){function n(){var e=d.items()[l];return e.scrollIntoView(),l++,e}var r=this,o,l=0,c,d=r.find("#files")[0];o=new i(s,a),o.picker(r.find("#addfiles")[0]),o.dropzone(r),o.on("select drop",function(t){e(t.files)}),o.on("error",function(){var e=n();e.items().filter("progress").value(100),e.items().filter("label")[0].classes.add("error").add("upload-error")}),o.on("progress",function(e){var t=d.items()[l];t.items().filter("progress").value(Math.round(e.loaded/e.total*100)),r.find("#filecount").text(["Uploading file: {0}/{1} at {2}/s",l+1,c,u.format(e.bytesPerSecond)])}),o.on("FileUploaded",n),o.on("FilesUploaded",function(e){var n=e.errors;t.displayErrors(n),r.find("#filecount").text(["Uploaded {0} files(s) at {1}/s",c,u.format(e.bytesPerSecond)]),a.onupload&&a.onupload({files:e.files}),!n.length&&a.upload_auto_close&&r.fire("CloseView")}),r.on("submit",function(e){return e.preventDefault(),a.path?(r.find("#addfiles").disabled(!0),o.disable(!0),r.parents().filter("window").each(function(e){e.statusbar.find("#upload").disabled(!0)}),c=f.length,void o.upload(f,a.path)):void t.displayErrors("You need to specify an upload path.")}),r.on("CloseView",function(){o.destroy()})}var r=this,a,s,c,d,f=[];return a=this.data.get("settings"),s=a.config,d=s.get("upload.maxsize").replace(/\s+/,""),d=d.replace(/([0-9]+)/g,"$1 "),c=l.merge([s.get("filesystem.extensions"),s.get("upload.extensions"),a.extensions]),this.on("postRender",n),[{type:"label",text:["Valid extensions: {0}",c.join(", ")],maxWidth:600,multiline:!0},{type:"label",text:["Max size: {0}",d]},i.hasNativeSupport()?{type:"label",name:"dragmsg",text:"Drag files here",classes:"upload-drop-zone",minHeight:200,maxHeight:200,border:1}:{type:"label",name:"dragmsg",text:"Select files to upload",style:"text-align: center",minHeight:200,maxHeight:200,border:1},{type:"panel",name:"files",layout:"flex",direction:"column",align:"stretch",autoScroll:!0,minHeight:200,maxHeight:200,border:1,hidden:!0},{type:"container",layout:"flex",align:"stretch",items:[{type:"label",name:"filecount",text:" ",flex:1},{type:"button",name:"addfiles",text:"Add files",icon:"upload"}]}]}});return c.renderAsDialog=function(e){t.renderViewAsDialog(c,{title:"Upload",buttons:[{text:"Upload",name:"upload",subtype:"primary",disabled:!0,onclick:"submit"},{text:"Close",onclick:"close"}]},e)},c}),i(Yt,[mt,se,ae],function(e,t,n){return{open:function(){return new n(function(n,i){e.load({js:["//www.google.com/jsapi","//apis.google.com/js/client.js"]},function(){google.load("picker","1",{callback:function(){function e(e){var t,r;return e.error?void i(e.error):(t=e.title.replace(/[\/\\\:\*\?><\|\"]/g,"_"),r=e.downloadUrl,!r&&e.exportLinks["application/pdf"]&&(r=e.exportLinks["application/pdf"],t+=".pdf"),r?void n({files:[{name:t,url:r+"&access_token="+gapi.auth.getToken().access_token}]}):void i(new Error("Could not find downloadUrl property.")))}var r;r=t.getInfo()["googledrive.client_id"],gapi.client.load("drive","v2"),gapi.auth.authorize({client_id:r,scope:"https://www.googleapis.com/auth/drive https://www.googleapis.com/auth/drive.file"},function(){var t,n,i=new google.picker.PickerBuilder;for(i.setAppId(r).setOAuthToken(gapi.auth.getToken().access_token).addView(google.picker.ViewId.DOCS).setCallback(function(t){var n,i,r;if(t.docs)for(n=0;n<t.docs.length;n++)i=t.docs[n],r=gapi.client.drive.files.get({fileId:i.id}),r.execute(e)}).build().setVisible(!0),t=document.getElementsByTagName("div"),n=0;n<t.length;n++)/modal\-dialog\-bg|picker\-dialog/.test(t[n].className)&&(t[n].style.zIndex=1e5)})}})})})}}}),i($t,[mt,se,ae],function(e,t,n){return{open:function(){return new n(function(n){e.loadScript({src:"//www.dropbox.com/static/api/1/dropbox.js",id:"dropboxjs","data-app-key":t.getInfo()["dropbox.app_id"]},function(){Dropbox.choose({linkType:"direct",success:function(e){n({files:[{name:e[0].name,url:e[0].link}]})}})})})}}}),i(Gt,[mt,se,ae,C],function(e,t,n,i){return{open:function(){return new n(function(n,r){e.load({js:["//js.live.net/v5.0/wl.js"]},function(){WL.init({client_id:t.getInfo()["onedrive.client_id"],redirect_uri:i.apiPageUrl+"?action=onedrive"}),WL.login({scope:"wl.skydrive wl.signin"}).then(function(){WL.fileDialog({mode:"open",select:"single"}).then(function(e){var t,i,r;t=WL.getSession().access_token,i=e.data.files,r="https://apis.live.net/v5.0/"+i[0].id+"/content?download=1&access_token="+t+"&x_http_live_library=Web%2Fchrome_5.5",n({files:[{name:i[0].name,url:r}]})},function(e){r(e.message)})},function(){r(new Error("Failed to authenticate."))})})})}}}),i(Jt,[ut,ue,vt,le,c,Se,Lt,zt,Bt,Wt,Ut,jt,Vt,qt,Xt,De,Yt,$t,Gt,Me,Oe,Et],function(e,t,n,i,r,o,a,s,l,u,c,d,f,h,p,m,g,v,y,x,b,w){function _(e,t){return t&&!/^https?:\/\//.test(t)&&0!==t.indexOf("//")&&(t=n.parseURI(e.document_base_url).toAbsolute(n.parseURI(t).getURI(!0))),0===t.indexOf("//")&&(t=document.location.protocol+t),t}function E(e){return/\.(jpe?g|gif|png)$/i.test(t.extname(e))}return{createActions:function(n,r){function R(e){var r=e.files[0];i.exec("importFromUrl",{url:r.url,path:t.join(n.get("path"),r.name)}).then(function(){A.refresh()},C)}function C(e){m.displayError(e)}function T(){n.set("lastActionTime",(new Date).getTime())}var S,A,k,D,N,I,M;k=n.get("files"),D=n.get("selectedFiles"),N=n.get("clipboardFiles"),I=n.get("userSettings"),r.url&&(M=_(r,r.url)),A=e.createActions(["list","selectFileSystem","edit","createDir","createDoc","rename","zip","unzip","view","deleteFiles","download","cut","copy","paste","refresh","filesView","thumbsView","filter","insert","sort","uploadFiles","logout","gotoLink","addFavorite","googlePicker","dropboxChooser","oneDrivePicker","autoSelectFiles"]);for(S in A)A[S].listen(T);return A.list.listen(function(e,i){function o(e){var i,r;e.file&&(i=t.dirname(e.file.get("path"))),i&&(r=n.get("fileSystems").findByPath(i),r&&e.unshift({name:"..",path:i,isFile:!1}))}var a,s=n.get("files");i=i||{},D.reset(),e&&n.set("path",e),a={path:n.get("path"),filter:n.get("filter"),extensions:r.extensions,include_file_pattern:r.include_file_pattern,exclude_file_pattern:r.exclude_file_pattern,include_directory_pattern:r.include_directory_pattern,exclude_directory_pattern:r.exclude_directory_pattern,force:i.force},M&&(a.url=M,M=null),s.reset().fetch(a).then(function(){var e,t=n.get("config");o(s);for(e in s.config)t.set(e,s.config[e]);n.set("file",s.file),s.file&&n.set("path",s.file.get("path")),s.urlFile&&D.add(s.urlFile),i.selectFiles&&D.add(i.selectFiles)},C)}),A.selectFileSystem.listen(function(e){e&&(n.set("selectedFileSystem",e),A.list(e.get("path")))}),A.edit.listen(function(){var e=D.toPathArray()[0];E(e)?a.renderAsDialog({path:e,show_save_as:!0,onsave:function(e){r.onsave&&r.onsave(e),k.insert(e.file),A.autoSelectFiles(e.file)}}):s.renderAsDialog({path:e,onsave:function(e){r.onsave&&r.onsave(e),k.insert(e.file),A.autoSelectFiles(e.file)}})}),A.createDir.listen(function(e){l.renderAsDialog({path:n.get("path"),template:e,oncreate:function(e){r.oncreate&&r.oncreate(e),k.insert(e.file),A.autoSelectFiles(e.file)}})}),A.createDoc.listen(function(e){u.renderAsDialog({path:n.get("path"),template:e,fields:n.get("config").get("createdoc.fields"),oncreate:function(e){r.oncreate&&r.oncreate(e),k.insert(e.file),A.autoSelectFiles(e.file)}})}),A.rename.listen(function(){c.renderAsDialog({from:D[0],onrename:function(e){r.onrename&&r.onrename({from:D[0],to:e.file}),A.refresh()}})}),A.zip.listen(function(){d.renderAsDialog({selectedFiles:D,to:n.get("path"),onzip:function(e){r.onzip&&r.onzip({from:D,to:e.file}),k.insert(e.file),A.autoSelectFiles(e.file)}})}),A.unzip.listen(function(){D.unzip(n.get("path")).then(function(){A.refresh()},function(){A.refresh()})}),A.view.listen(function(){var e=D.toPathArray()[0];E(e)?h.renderAsDialog({path:e,paths:D.toPathArray()}):f.renderAsDialog({path:e})}),A.deleteFiles.listen(function(){D.length>0&&o.confirm("Are you sure you want to delete the selected files?",function(e){e&&D.destroy().then(function(){r.ondelete&&r.ondelete({files:D}),A.refresh()},C)})}),A.download.listen(function(){document.location.href=D.getDownloadUrl()}),A.cut.listen(function(){n.set("clipboardAction","cut"),N.reset().add(D)}),A.copy.listen(function(){n.set("clipboardAction","copy"),N.reset().add(D)}),A.paste.listen(function(){function e(e){return x.moveTo(e.from.get("path"),e.to.get("path"),e.type).then(function(t){k.remove(e.from.get("path")),k.insert(t),A.autoSelectFiles(t)},C)}function i(e){return x.copyTo(e.from.get("path"),e.to.get("path"),e.type).then(function(e){k.insert(e),A.autoSelectFiles(e)},C)}function r(){function t(){var s=c.shift(),l;s?(r||(r=new w({type:"progress"}),n.get("notifications").add(r)),l=s.from.get("name"),r.set("percent",(o-(c.length+1))/o*100),"cut"==a?(r.set("text","Moving file: "+l),e(s).then(t)):(r.set("text","Copying file: "+l),i(s).then(t))):("cut"==a?r.set("text","Finished moving files."):r.set("text","Finished copying files."),r.set("percent",100))}var r,o=c.length;t()}function o(){function e(e,t,n){"cancel"!=e&&("skip"!=e&&c.push({from:t,to:n,type:e}),0===l.length?c.length>0&&r():o())}var t,n;return t=l.shift(),n=u.shift(),s?void e(s,t,n):void(n.get("exists")?m.renderConfirmOverwriteDialog(['The "{0}" file/directory already exists. Are you sure you want to overwrite this file?',n.get("name")],function(i,r){r&&(s=i),e(i,t,n)}):e("default",t,n))}var a,s,l,u,c=[];a=n.get("clipboardAction"),a&&(l=new b,u=new b,N.forEach(function(e){l.push(e),u.push({path:t.join(n.get("path"),t.basename(e.get("path")))})}),n.set("clipboardAction",null),N.reset(),u.populateMeta({create:!0}).then(function(){o()}))}),A.refresh.listen(function(e){A.list(n.get("path"),{force:e})}),A.filesView.listen(function(){n.set("view","files")}),A.thumbsView.listen(function(){n.set("view","thumbs")}),A.filter.listen(function(e){n.set("filter",e),A.list()}),A.insert.listen(function(e){function t(e){r.oninsert&&r.oninsert(e)}var n=new b;e&&D.reset().add(e),D.length&&(I.multiple!==!1||1===D.length)&&(D.forEach(function(e){var t=e.get("link");t&&n.add(t)}),n.length?n.populateMeta().then(t,C):D.populateMeta().then(t,C))}),A.sort.listen(function(e,t){k.sort(e,t)}),A.uploadFiles.listen(function(){p.renderAsDialog({path:n.get("path"),config:n.get("config"),onupload:function(e){r.onupload&&r.onupload(e);for(var t=0;t<e.files.length;t++)k.insert(e.files[t]);A.autoSelectFiles(e.files)}})}),A.logout.listen(function(){i.exec("logout",{}).then(function(){},function(){})}),A.gotoLink.listen(function(){var e=D[0],n,i;e&&(n=e.get("link").get("path"),i=e.get("isFile")?t.dirname(n):n,A.list(i,{select:[e.get("link")]}))}),A.addFavorite.listen(function(){i.exec("favorites.add",{paths:D.toPathArray()}).then(function(){},C)}),A.googlePicker.listen(function(){g.open(n.get("path")).then(R,C)}),A.dropboxChooser.listen(function(){v.open(n.get("path")).then(R,C)}),A.oneDrivePicker.listen(function(){y.open(n.get("path")).then(R,C)}),A.autoSelectFiles.listen(function(e){D.reset(),D.add(e)}),A}}}),i(Kt,[O,Xe,vt,yt,gt,De,Mt,Ft,Ht,x,Me,Oe,wt,_t,Rt,Ie,Jt],function(e,t,n,i,r,o,a,s,l,u,c,d,f,h,p,m,g){function v(e,t){var n=new r(e);n.add("ctrl+x",t.cut),n.add("ctrl+c",t.copy),n.add("ctrl+v",t.paste),n.add("delete",t.deleteFiles)}function y(e,n){e.on("PostRender",function(){var i=new t(e.getEl("body")),r;window.notifier=i,r=n.get("notifications"),r.on("add",function(e){function t(e){var t;t="progress"==e.get("type")?i.progress(e.get("text")):i.info(e.get("text")),e.on("change:text",function(e){t.message(e.value)}),e.on("change:percent",function(e){t.percent(e.value)}),e.on("destroy",function(){t.close()}),t.message(e.get("text")),t.percent(e.get("percent")),t.on("close",function(){r.remove(e)})}var n=e.items,o;for(o=0;o<n.length;o++)t(n[o])})})}var x,b=e.extend({Defaults:{layout:"border",defaults:{type:"panel",layout:"fit"}},render:function(){var e=this,t,n,i=this.data.get("settings")||{};return e.on("CloseView",function(){t.off(),t.get("files").off(),t.get("selectedFiles").off(),t.get("selectedFileSystem").off(),t.get("fileSystems").off(),t.get("clipboardFiles").off(),t.get("config").off()}),this.viewModel=t=new u({path:"",file:null,filter:i.filter||"",files:new d,selectedFiles:new d,selectedFileSystem:new f,fileSystems:new h,clipboardFiles:new d,notifications:new p,view:i.view||"files",userSettings:i,config:new m,lastActionTime:0}),t.get("files").sortBy=i.sort_by||"name",t.get("files").sortOrder=i.sort_order||"asc",window.viewModel=t,this.actions=n=g.createActions(t,i),v(this,this.actions),y(this,t),t.get("files").on("sort",function(){n.list()}),t.get("files").on("remove",function(e){t.get("selectedFiles").remove(e.items)}),t.on("change:path",function(e){i.remember_last_path&&(x=e.value),t.set("selectedFileSystem",t.get("fileSystems").findByPath(e.value))}),n.insert.listen(function(){var n=t.get("selectedFiles").length;(n>0||i.multiple!==!1||1===n)&&e.fire("CloseView")}),n.logout.listen(function(){e.fire("CloseView")}),this.on("OpenView",function(){t.get("fileSystems").fetch({rootpath:i.rootpath}).then(function(){i.path?t.set("path",i.path):i.remember_last_path&&x?t.set("path",x):t.set("path",t.get("fileSystems")[0].get("path")),n.list()},function(e){o.displayError(e)})}),[new a({region:"north",border:"0 0 1 0",data:{viewModel:t,actions:n}}),new s({region:"west",width:190,border:"0 1 0 0",hidden:i.leftpanel===!1,data:{viewModel:t,actions:n}}),new l({region:"center",data:{viewModel:t,actions:n}})]}});return b.renderAsDialog=function(e){function t(e){return e.parent().parent().items()[0]}function n(){t(this).actions.insert()}function r(){function n(){i.disabled(e.multiple===!1?1!==r.get("selectedFiles").length:!r.get("selectedFiles").length)}var i=this,r=t(this).viewModel;r.get("selectedFiles").on("add remove",n),n()}i.addButtons({"insert.insert":{text:"Insert",subtype:"primary",onclick:n,onPostRender:r},"insert.close":{text:"Close",onclick:"close"}}),o.renderViewAsDialog(b,{title:e.title||"MoxieManager",width:e.width||800,height:e.height||500,layout:"fit",buttons:i.createToolbar("insert",e.filelist_insert_toolbar,"insert close"),fullscreen:"auto"},e)},b}),i(Zt,[V,De,F,le,C,Se],function(e,t,n,i,r,o){var a=e.extend({render:function(){function e(){switch(r.find("#basicauth_details,#sessionauth_details").hide(),r.find("#authenticator").value()){case"BasicAuthenticator":r.find("#basicauth_details")[0].visible(!0);break;case"SessionAuthenticator":r.find("#sessionauth_details")[0].visible(!0)}r.reflow()}function t(e){e.preventDefault(),i.exec("install",e.data).then(function(e){e===!0?(a.oninstall(),r.fire("CloseView")):n.create({type:"window",title:"Couldn't write config file",padding:10,spacing:5,direction:"column",items:[{type:"label",text:"You need to manually paste this into your config file located in the root of moxiemanager."},{type:"textbox",multiline:!0,value:e,minWidth:700,minHeight:500,spellcheck:!1,onpostrender:function(e){e.control.getEl().select()},onclick:function(e){e.target.select()}}],buttons:[{text:"Close",onclick:"close"}]}).renderTo(document.body).reflow()},function(e){o.alert(e.message)})}var r=this,a=r.data.get("settings");return r.on("submit",t),[{type:"label",text:"This will configure the basic settings needed for the MoxieManager."},{label:"License key",name:"license",type:"textbox",placeholder:"xxxx-xxxx-xxxx-xxxx-xxxx-xxxx-xxxx-xxxx",tooltip:"Paste you license key you got from the www.moxiemanager.com website",minWidth:250},{label:"Authenticator",name:"authenticator",onselect:e,type:"listbox",values:[{text:"Basic authenticator",value:"BasicAuthenticator",selected:!0},{text:"Session authenticator",value:"SessionAuthenticator"},{text:"Joomla authenticator",value:"JoomlaAuthenticator"},{text:"Drupal authenticator",value:"DrupalAuthenticator"}]},{type:"fieldset",name:"basicauth_details",title:"Basic authenticator",items:[{type:"label",maxWidth:400,multiline:!0,text:"Enter the username/password you want to use to login to the MoxieManager"},{label:"User name",name:"username",type:"textbox"},{label:"Password",name:"password",type:"textbox",subtype:"password"}]},{type:"fieldset",hidden:!0,name:"sessionauth_details",title:"Session authenticator",items:[{type:"label",maxWidth:400,multiline:!0,text:"Enter the session key to authorize the user by."},{label:"Session name",name:"logged_in_key",type:"textbox",value:"isLoggedIn",tooltip:"Session name to check"}]}]}});return a.renderAsDialog=function(e){return"api.ashx"==r.apiPageName?void o.alert("You need to follow the installation instructions on the www.moxiemanager.com website.",function(){document.location.href="http://www.moxiemanager.com/documentation/index.php/Installation_net"}):void t.renderViewAsDialog(a,{title:"Installation",buttons:[{text:"Install",subtype:"primary",onclick:"submit"},{text:"Cancel",onclick:"close"}]},e)},a}),i(Qt,[V,De,ke,le],function(e,t,n,i){var r=e.extend({render:function(){var e=this,t=e.data.get("settings")||{};return e.on("submit",function(n){var r=n.data;i.exec("login",{username:r.username,password:r.password,persistent:r.persistent}).then(function(n){n?(e.fire("CloseView"),t.onlogin&&t.onlogin()):e.find("#msg").eq(0).show(!0)})}),[{label:"Username",name:"username",type:"textbox",minWidth:250},{label:"Password",name:"password",type:"textbox",subtype:"password",minWidth:250},{label:" ",name:"persistent",type:"checkbox",checked:!0,text:"Keep me logged in"},{type:"label",name:"msg",text:n.translate("Wrong username/password"),hidden:!0}]}});return r.renderAsDialog=function(e){t.renderViewAsDialog(r,{title:"Login",buttons:[{text:"Login",subtype:"primary",onclick:"submit"},{text:"Cancel",onclick:"close"}]},e)},r}),i(en,[mt,C,ke],function(e,t,n){function i(){function e(e){var t="moxman-loader-scale 0.75s "+e+" infinite cubic-bezier(.2, .68, .18, 1.08);";return"-webkit-animation: "+t+"animation: "+t}var t=document.createElement("div"),n=document.createElement("div"),i=document.createElement("style"),r,o,a;t.id="moxman-modal-block",t.className="moxman-reset moxman-fade",n.className="moxman-reset moxman-loader-throbber",i=document.createElement("style"),i.type="text/css";var s="keyframes moxman-loader-scale {0% {-webkit-transform: scale(1);transform: scale(1);opacity: 1;}45% {-webkit-transform: scale(0.1);transform: scale(0.1);opacity: 0.7;}80% {-webkit-transform: scale(1);transform: scale(1);opacity: 1;}}",l=".moxman-reset {margin: 0; padding: 0; border: 0; outline: 0;vertical-align: top; background: transparent;font-size: @font-size; position: static;width: auto; height: auto;white-space: nowrap; cursor: inherit;-webkit-tap-highlight-color: transparent;line-height: normal; font-weight: normal;text-align: left; float: none;-moz-box-sizing: content-box;-webkit-box-sizing: content-box;box-sizing: content-box;direction: ltr;max-width: none;}.moxman-fade {opacity: 0;filter: alpha(opacity=30);transition: opacity 0.15s linear;}.moxman-in {opacity: 0.3;}#moxman-modal-block {position: fixed;left: 0; top: 0;width: 100%; height: 100%;background: #000;}@-webkit-"+s+"@"+s+".moxman-loader-throbber {position: fixed;top: 50%; left: 50%;width: 60px; height: 60px;margin-top: -30px;margin-left: -30px;}.moxman-loader-throbber > div:nth-child(1) {"+e("-0.24s")+"}.moxman-loader-throbber > div:nth-child(2) {"+e("-0.12s")+"}.moxman-loader-throbber > div:nth-child(3) {"+e("-0s")+"}.moxman-loader-throbber > .moxman-loader-throbber-dot {background-color: #000;width: 15px;height: 15px;border-radius: 100%;margin: 2px;-webkit-animation-fill-mode: both;animation-fill-mode: both;display: inline-block;}";
for(i.styleSheet?i.styleSheet.cssText=l:i.appendChild(document.createTextNode(l)),o=document.getElementsByTagName("head")[0],o.firstChild?o.insertBefore(i,o.firstChild):o.appendChild(i),t.style.zIndex=16777215,n.style.zIndex=16777215,document.body.appendChild(t),document.body.appendChild(n),a=0;3>a;a++)r=document.createElement("div"),r.className="moxman-reset moxman-loader-throbber-dot",n.appendChild(r);return setTimeout(function(){t.className+=" moxman-in"},0),{hide:function(){n.parentNode.removeChild(n),i.parentNode.removeChild(i)}}}function r(n){return function(r){function o(e){return s&&(s=s.replace(/^[?& ]+/,""),s&&(e+=(-1===e.indexOf("?")?"?":"&")+s)),e}var a,s=t.cacheSuffix,l;if(r=r||{},!t.baseUrl)for(var u=document.getElementsByTagName("script"),c=0;c<u.length;c++){var d=u[c].src;if(/(^|\/)moxman\./.test(d)){s=t.cacheSuffix||d.split("?")[1],t.baseUrl=d.substring(0,d.lastIndexOf("/"))+"/..",t.apiPageUrl=t.baseUrl+"/"+t.apiPageName;break}}if(a=r.language||t.language,moxman.ui)moxman.Dialogs[n](r);else{if(l)return;l=i(),e.load({js:[o(t.baseUrl+"/js/moxman.api.min.js"),t.apiPageUrl+"?action=language"+(a?"&code="+a:"")],css:[o(t.baseUrl+"/skins/"+(r.skin||"lightgray")+"/skin"+(t.ie7?".ie7":"")+".min.css")]},function(){l.hide(),moxman.ui.FloatPanel.zIndex=r.zIndex||moxman.ui.FloatPanel.zIndex,moxman.Dialogs.loaded(t),moxman.Dialogs[n](r)})}}}var o=this||window;if("toJSON"in Object.prototype)return void alert("MoxieManager detected an old prototype.js version that breaks compatibility.");var a={browse:r("browse"),upload:r("upload"),edit:r("edit"),zip:r("zip"),createDir:r("createDir"),createDoc:r("createDoc"),view:r("view"),rename:r("rename")};o.moxman=o.moxman||{};for(var s in a)o.moxman[s]=a[s];return o.moxman.addI18n=n.add,a}),i(tn,[Me,bt,c,se,Kt,Bt,Wt,Lt,zt,Xt,Vt,qt,Qt,Zt,De,k,K,ke,C],function(e,t,n,i,r,o,a,s,l,u,c,d,f,h,p,m,g,v,y){function x(e){return/\.(jpe?g|png|gif)$/i.test(e)}function b(e){return function(t){function n(){e(t)}t=t||{},i.request({cache:!1}).then(function(i){return i.installed?void(i.loggedin?n():i.standalone?f.renderAsDialog({onlogin:n}):i.loginurl?document.location.href=i.loginurl:p.displayError("Access denied by authenticator(s).")):void h.renderAsDialog({oninstall:function(){b(e)(t)}})},function(e){p.displayError(e.message)})}}function w(e,t,i){e.renderAsDialog(n.extend({},t,i))}function _(e,n){return t.update(e,n)}function E(e){var t;return e&&n.each(e.split(/[, ]/),function(e){var n;return(n=document.getElementById(e))?(t=n.value,!1):void 0}),t}function R(e,t){e&&n.each(e.split(/[, ]/),function(e){var n;if(n=document.getElementById(e))if(n.value=t,"fireEvent"in n)n.fireEvent("onchange");else{var i=document.createEvent("HTMLEvents");i.initEvent("change",!1,!0),n.dispatchEvent(i)}})}var C=n.curry(function(e,t,n,i){var r,o={};t="on"+t,r=n[t],r&&(o[e]=_(n,i[e].toJSON()),r(o))}),T=C("files"),S=C("file"),A=T("upload"),k=S("save"),D=S("create"),N=T("delete");return m.translate=v.translate,{loaded:function(e){y.baseUrl=e.baseUrl,y.apiPageUrl=e.baseUrl+"/"+e.apiPageName},browse:b(function(e){e.url=e.url||E(e.fields),w(r,e,{oninsert:function(t){e.oninsert?e.oninsert({files:_(e,t.toJSON()),focusedFile:_(e,t[0].toJSON())}):R(e.fields,_(e,t[0].toJSON()).url)},onupload:A(e),onsave:k(e),oncreate:D(e),ondelete:N(e)})}),createDir:b(function(e){w(o,e,{oncreate:D(e)})}),createDoc:b(function(t){e.getConfig(t.path).then(function(e){w(a,n.extend({fields:e.get("createdoc.fields")},t,{oncreate:D(t)}))})}),edit:b(function(e){x(e.path)?w(s,e,{onsave:k(e)}):w(l,e,{onsave:k(e)})}),upload:b(function(t){e.getConfig(t.path).then(function(e){w(u,n.extend({config:e},t),{onupload:A(t)})})}),view:b(function(e){x(e.path)?w(d,e):w(c,e)})}}),i(nn,[Ee,ge,de,ce,Ct,ye,fe,Re],function(e,t,n,i,r,o,a,s){function l(n){var a=this,l;"string"==typeof n&&(n={drop_zone:n}),l={accept:[{title:e.translate("All Files"),extensions:"*"}],required_caps:{drag_and_drop:!0}},n="object"==typeof n?i.extend({},l,n):l,n.container=t.get(n.drop_zone)||document.body,"static"===t.getStyle(n.container,"position")&&(n.container.style.position="relative"),"string"==typeof n.accept&&(n.accept=s.mimes2extList(n.accept)),o.call(a),i.extend(a,{uid:i.guid("uid_"),ruid:null,files:null,init:function(){a.convertEventPropsToHandlers(u),a.bind("RuntimeInit",function(e,t){a.ruid=t.uid,a.bind("Drop",function(){var e=t.exec.call(a,"FileDrop","getFiles");a.files=[],i.each(e,function(e){a.files.push(new r(a.ruid,e))})},999),t.exec.call(a,"FileDrop","init",n),a.dispatchEvent("ready")}),a.connectRuntime(n)},destroy:function(){var e=this.getRuntime();e&&(e.exec.call(this,"FileDrop","destroy"),this.disconnectRuntime()),this.files=null}})}var u=["ready","dragenter","dragleave","drop","error"];return l.prototype=a.instance,l}),i(rn,[ce,de,ve,me],function(e,t,n,i){function r(t){var r=this,s=n.capTest,l=n.capTrue,u=e.extend({access_binary:s(window.FileReader||window.File&&window.File.getAsDataURL),access_image_binary:function(){return r.can("access_binary")&&!!a.Image},display_media:s(i.can("create_canvas")||i.can("use_data_uri_over32kb")),do_cors:s(window.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest),drag_and_drop:s(function(){var e=document.createElement("div");return("draggable"in e||"ondragstart"in e&&"ondrop"in e)&&("IE"!==i.browser||i.version>9)}()),filter_by_extension:s(function(){return"Chrome"===i.browser&&i.version>=28||"IE"===i.browser&&i.version>=10}()),return_response_headers:l,return_response_type:function(e){return"json"===e&&window.JSON?!0:i.can("return_response_type",e)},return_status_code:l,report_upload_progress:s(window.XMLHttpRequest&&(new XMLHttpRequest).upload),resize_image:function(){return r.can("access_binary")&&i.can("create_canvas")},select_file:function(){return i.can("use_fileinput")&&window.File},select_folder:function(){return r.can("select_file")&&"Chrome"===i.browser&&i.version>=21},select_multiple:function(){return!(!r.can("select_file")||"Safari"===i.browser&&"Windows"===i.os||"iOS"===i.os&&i.verComp(i.osVersion,"7.0.4","<"))},send_binary_string:s(window.XMLHttpRequest&&((new XMLHttpRequest).sendAsBinary||window.Uint8Array&&window.ArrayBuffer)),send_custom_headers:s(window.XMLHttpRequest),send_multipart:function(){return!!(window.XMLHttpRequest&&(new XMLHttpRequest).upload&&window.FormData)||r.can("send_binary_string")},slice_blob:s(window.File&&(File.prototype.mozSlice||File.prototype.webkitSlice||File.prototype.slice)),stream_upload:function(){return r.can("slice_blob")&&r.can("send_multipart")},summon_file_dialog:s(function(){return"Firefox"===i.browser&&i.version>=4||"Opera"===i.browser&&i.version>=12||"IE"===i.browser&&i.version>=10||!!~e.inArray(i.browser,["Chrome","Safari"])}()),upload_filesize:l},arguments[2]);n.call(this,t,arguments[1]||o,u),e.extend(this,{init:function(){this.trigger("Init")},destroy:function(e){return function(){e.call(r),e=r=null}}(this.destroy)}),e.extend(this.getShim(),a)}var o="html5",a={};return n.addConstructor(o,r),a}),i(on,[rn,be],function(e,t){function n(){function e(e,t,n){var i;if(!window.File.prototype.slice)return(i=window.File.prototype.webkitSlice||window.File.prototype.mozSlice)?i.call(e,t,n):null;try{return e.slice(),e.slice(t,n)}catch(r){return e.slice(t,n-t)}}this.slice=function(){return new t(this.getRuntime().uid,e.apply(this,arguments))}}return e.Blob=n}),i(an,[ce],function(e){function t(){this.returnValue=!1}function n(){this.cancelBubble=!0}var i={},r="moxie_"+e.guid(),o=function(o,a,s,l){var u,c;a=a.toLowerCase(),o.addEventListener?(u=s,o.addEventListener(a,u,!1)):o.attachEvent&&(u=function(){var e=window.event;e.target||(e.target=e.srcElement),e.preventDefault=t,e.stopPropagation=n,s(e)},o.attachEvent("on"+a,u)),o[r]||(o[r]=e.guid()),i.hasOwnProperty(o[r])||(i[o[r]]={}),c=i[o[r]],c.hasOwnProperty(a)||(c[a]=[]),c[a].push({func:u,orig:s,key:l})},a=function(t,n,o){var a,s;if(n=n.toLowerCase(),t[r]&&i[t[r]]&&i[t[r]][n]){a=i[t[r]][n];for(var l=a.length-1;l>=0&&(a[l].orig!==o&&a[l].key!==o||(t.removeEventListener?t.removeEventListener(n,a[l].func,!1):t.detachEvent&&t.detachEvent("on"+n,a[l].func),a[l].orig=null,a[l].func=null,a.splice(l,1),o===s));l--);if(a.length||delete i[t[r]][n],e.isEmptyObj(i[t[r]])){delete i[t[r]];try{delete t[r]}catch(u){t[r]=s}}}},s=function(t,n){t&&t[r]&&e.each(i[t[r]],function(e,i){a(t,i,n)})};return{addEvent:o,removeEvent:a,removeAllEvents:s}}),i(sn,[rn,ce,ge,an,Re],function(e,t,n,i,r){function o(){function e(e){for(var n=[],i=0;i<e.length;i++)[].push.apply(n,e[i].extensions.split(/\s*,\s*/));return-1===t.inArray("*",n)?n:[]}function o(e){var n=r.getFileExtension(e.name);return!n||!d.length||-1!==t.inArray(n,d)}function a(e,n){var i=[];t.each(e,function(e){var t=e.webkitGetAsEntry();if(t)if(t.isFile){var n=e.getAsFile();o(n)&&c.push(n)}else i.push(t)}),i.length?s(i,n):n()}function s(e,n){var i=[];t.each(e,function(e){i.push(function(t){l(e,t)})}),t.inSeries(i,function(){n()})}function l(e,t){e.isFile?e.file(function(e){o(e)&&c.push(e),t()},function(){t()}):e.isDirectory?u(e,t):t()}function u(e,t){function n(e){r.readEntries(function(t){t.length?([].push.apply(i,t),n(e)):e()},e)}var i=[],r=e.createReader();n(function(){s(i,t)})}var c=[],d=[],f;t.extend(this,{init:function(n){var r=this,s;f=n,d=e(f.accept),s=f.container,i.addEvent(s,"dragover",function(e){e.preventDefault(),e.stopPropagation(),e.dataTransfer.dropEffect="copy"},r.uid),i.addEvent(s,"drop",function(e){e.preventDefault(),e.stopPropagation(),c=[],e.dataTransfer.items&&e.dataTransfer.items[0].webkitGetAsEntry?a(e.dataTransfer.items,function(){r.trigger("drop")}):(t.each(e.dataTransfer.files,function(e){o(e)&&c.push(e)}),r.trigger("drop"))},r.uid),i.addEvent(s,"dragenter",function(e){e.preventDefault(),e.stopPropagation(),r.trigger("dragenter")},r.uid),i.addEvent(s,"dragleave",function(e){e.preventDefault(),e.stopPropagation(),r.trigger("dragleave")},r.uid)},getFiles:function(){return c},destroy:function(){i.removeAllEvents(f&&n.get(f.container),this.uid),c=d=f=null}})}return e.FileDrop=o}),i(ln,[rn,ce,ge,an,Re,me],function(e,t,n,i,r,o){function a(){var e=[],a;t.extend(this,{init:function(s){var l=this,u=l.getRuntime(),c,d,f,h,p,m;a=s,e=[],f=a.accept.mimes||r.extList2mimes(a.accept,u.can("filter_by_extension")),d=u.getShimContainer(),d.innerHTML='<input id="'+u.uid+'" type="file" style="font-size:999px;opacity:0;"'+(a.multiple&&u.can("select_multiple")?"multiple":"")+(a.directory&&u.can("select_folder")?"webkitdirectory directory":"")+(f?' accept="'+f.join(",")+'"':"")+" />",c=n.get(u.uid),t.extend(c.style,{position:"absolute",top:0,left:0,width:"100%",height:"100%"}),h=n.get(a.browse_button),u.can("summon_file_dialog")&&("static"===n.getStyle(h,"position")&&(h.style.position="relative"),p=parseInt(n.getStyle(h,"z-index"),10)||1,h.style.zIndex=p,d.style.zIndex=p-1,i.addEvent(h,"click",function(e){var t=n.get(u.uid);t&&!t.disabled&&t.click(),e.preventDefault()},l.uid)),m=u.can("summon_file_dialog")?h:d,i.addEvent(m,"mouseover",function(){l.trigger("mouseenter")},l.uid),i.addEvent(m,"mouseout",function(){l.trigger("mouseleave")},l.uid),i.addEvent(m,"mousedown",function(){l.trigger("mousedown")},l.uid),i.addEvent(n.get(a.container),"mouseup",function(){l.trigger("mouseup")},l.uid),c.onchange=function g(){if(e=[],a.directory?t.each(this.files,function(t){"."!==t.name&&e.push(t)}):e=[].slice.call(this.files),"IE"!==o.browser)this.value="";else{var n=this.cloneNode(!0);this.parentNode.replaceChild(n,this),n.onchange=g}l.trigger("change")},l.trigger({type:"ready",async:!0}),d=null},getFiles:function(){return e},disable:function(e){var t=this.getRuntime(),i;(i=n.get(t.uid))&&(i.disabled=!!e)},destroy:function(){var t=this.getRuntime(),r=t.getShim(),o=t.getShimContainer();i.removeAllEvents(o,this.uid),i.removeAllEvents(a&&n.get(a.container),this.uid),i.removeAllEvents(a&&n.get(a.browse_button),this.uid),o&&(o.innerHTML=""),r.removeInstance(this.uid),e=a=o=r=null}})}return e.FileInput=a}),i(un,[rn,ce,Re,pe,Ct,be,_e,de,me],function(e,t,n,i,r,o,a,s,l){function u(){function e(e,t){var n=this,i,r;i=t.getBlob().getSource(),r=new window.FileReader,r.onload=function(){t.append(t.getBlobName(),new o(null,{type:i.type,data:r.result})),f.send.call(n,e,t)},r.readAsBinaryString(i)}function u(){return!window.XMLHttpRequest||"IE"===l.browser&&l.version<8?function(){for(var e=["Msxml2.XMLHTTP.6.0","Microsoft.XMLHTTP"],t=0;t<e.length;t++)try{return new ActiveXObject(e[t])}catch(n){}}():new window.XMLHttpRequest}function c(e){var t=e.responseXML,n=e.responseText;return"IE"===l.browser&&n&&t&&!t.documentElement&&/[^\/]+\/[^\+]+\+xml/.test(e.getResponseHeader("Content-Type"))&&(t=new window.ActiveXObject("Microsoft.XMLDOM"),t.async=!1,t.validateOnParse=!1,t.loadXML(n)),t&&("IE"===l.browser&&0!==t.parseError||!t.documentElement||"parsererror"===t.documentElement.tagName)?null:t}function d(e){var t="----moxieboundary"+(new Date).getTime(),n="--",i="\r\n",r="",a=this.getRuntime();if(!a.can("send_binary_string"))throw new s.RuntimeError(s.RuntimeError.NOT_SUPPORTED_ERR);return h.setRequestHeader("Content-Type","multipart/form-data; boundary="+t),e.each(function(e,a){r+=e instanceof o?n+t+i+'Content-Disposition: form-data; name="'+a+'"; filename="'+unescape(encodeURIComponent(e.name||"blob"))+'"'+i+"Content-Type: "+(e.type||"application/octet-stream")+i+i+e.getSource()+i:n+t+i+'Content-Disposition: form-data; name="'+a+'"'+i+i+unescape(encodeURIComponent(e))+i}),r+=n+t+n+i}var f=this,h,p;t.extend(this,{send:function(n,r){var s=this,c="Mozilla"===l.browser&&l.version>=4&&l.version<7,f="Android Browser"===l.browser,m=!1;if(p=n.url.replace(/^.+?\/([\w\-\.]+)$/,"$1").toLowerCase(),h=u(),h.open(n.method,n.url,n.async,n.user,n.password),r instanceof o)r.isDetached()&&(m=!0),r=r.getSource();else if(r instanceof a){if(r.hasBlob())if(r.getBlob().isDetached())r=d.call(s,r),m=!0;else if((c||f)&&"blob"===t.typeOf(r.getBlob().getSource())&&window.FileReader)return void e.call(s,n,r);if(r instanceof a){var g=new window.FormData;r.each(function(e,t){e instanceof o?g.append(t,e.getSource()):g.append(t,e)}),r=g}}h.upload?(n.withCredentials&&(h.withCredentials=!0),h.addEventListener("load",function(e){s.trigger(e)}),h.addEventListener("error",function(e){s.trigger(e)}),h.addEventListener("progress",function(e){s.trigger(e)}),h.upload.addEventListener("progress",function(e){s.trigger({type:"UploadProgress",loaded:e.loaded,total:e.total})})):h.onreadystatechange=function v(){switch(h.readyState){case 1:break;case 2:break;case 3:var e,t;try{i.hasSameOrigin(n.url)&&(e=h.getResponseHeader("Content-Length")||0),h.responseText&&(t=h.responseText.length)}catch(r){e=t=0}s.trigger({type:"progress",lengthComputable:!!e,total:parseInt(e,10),loaded:t});break;case 4:h.onreadystatechange=function(){},s.trigger(0===h.status?"error":"load")}},t.isEmptyObj(n.headers)||t.each(n.headers,function(e,t){h.setRequestHeader(t,e)}),""!==n.responseType&&"responseType"in h&&("json"!==n.responseType||l.can("return_response_type","json")?h.responseType=n.responseType:h.responseType="text"),m?h.sendAsBinary?h.sendAsBinary(r):!function(){for(var e=new Uint8Array(r.length),t=0;t<r.length;t++)e[t]=255&r.charCodeAt(t);h.send(e.buffer)}():h.send(r),s.trigger("loadstart")},getStatus:function(){try{if(h)return h.status}catch(e){}return 0},getResponse:function(e){var t=this.getRuntime();try{switch(e){case"blob":var i=new r(t.uid,h.response),o=h.getResponseHeader("Content-Disposition");if(o){var a=o.match(/filename=([\'\"'])([^\1]+)\1/);a&&(p=a[2])}return i.name=p,i.type||(i.type=n.getFileMime(p)),i;case"json":return l.can("return_response_type","json")?h.response:200===h.status&&window.JSON?JSON.parse(h.responseText):null;case"document":return c(h);default:return""!==h.responseText?h.responseText:null}}catch(s){return null}},getAllResponseHeaders:function(){try{return h.getAllResponseHeaders()}catch(e){}return""},abort:function(){h&&h.abort()},destroy:function(){f=p=null}})}return e.XMLHttpRequest=u}),i(cn,[ce,me,ge,de,ve],function(e,t,n,i,r){function o(){var e;try{e=navigator.plugins["Shockwave Flash"],e=e.description}catch(t){try{e=new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version")}catch(n){e="0.0"}}return e=e.match(/\d+/g),parseFloat(e[0]+"."+e[1])}function a(a){var u=this,c;a=e.extend({swf_url:t.swf_url},a),r.call(this,a,s,{access_binary:function(e){return e&&"browser"===u.mode},access_image_binary:function(e){return e&&"browser"===u.mode},display_media:r.capTrue,do_cors:r.capTrue,drag_and_drop:!1,report_upload_progress:function(){return"client"===u.mode},resize_image:r.capTrue,return_response_headers:!1,return_response_type:function(t){return"json"===t&&window.JSON?!0:!e.arrayDiff(t,["","text","document"])||"browser"===u.mode},return_status_code:function(t){return"browser"===u.mode||!e.arrayDiff(t,[200,404])},select_file:r.capTrue,select_multiple:r.capTrue,send_binary_string:function(e){return e&&"browser"===u.mode},send_browser_cookies:function(e){return e&&"browser"===u.mode},send_custom_headers:function(e){return e&&"browser"===u.mode},send_multipart:r.capTrue,slice_blob:r.capTrue,stream_upload:function(e){return e&&"browser"===u.mode},summon_file_dialog:!1,upload_filesize:function(t){return e.parseSizeStr(t)<=2097152||"client"===u.mode},use_http_method:function(t){return!e.arrayDiff(t,["GET","POST"])}},{access_binary:function(e){return e?"browser":"client"},access_image_binary:function(e){return e?"browser":"client"},report_upload_progress:function(e){return e?"browser":"client"},return_response_type:function(t){return e.arrayDiff(t,["","text","json","document"])?"browser":["client","browser"]},return_status_code:function(t){return e.arrayDiff(t,[200,404])?"browser":["client","browser"]},send_binary_string:function(e){return e?"browser":"client"},send_browser_cookies:function(e){return e?"browser":"client"},send_custom_headers:function(e){return e?"browser":"client"},stream_upload:function(e){return e?"client":"browser"},upload_filesize:function(t){return e.parseSizeStr(t)>=2097152?"client":"browser"}},"client"),o()<10&&(this.mode=!1),e.extend(this,{getShim:function(){return n.get(this.uid)},shimExec:function(e,t){var n=[].slice.call(arguments,2);return u.getShim().exec(this.uid,e,t,n)},init:function(){var n,r,o;o=this.getShimContainer(),e.extend(o.style,{position:"absolute",top:"-8px",left:"-8px",width:"9px",height:"9px",overflow:"hidden"}),n='<object id="'+this.uid+'" type="application/x-shockwave-flash" data="'+a.swf_url+'" ',"IE"===t.browser&&(n+='classid="clsid:d27cdb6e-ae6d-11cf-96b8-************" '),n+='width="100%" height="100%" style="outline:0"><param name="movie" value="'+a.swf_url+'" /><param name="flashvars" value="uid='+escape(this.uid)+"&target="+t.global_event_dispatcher+'" /><param name="wmode" value="transparent" /><param name="allowscriptaccess" value="always" /></object>',"IE"===t.browser?(r=document.createElement("div"),o.appendChild(r),r.outerHTML=n,r=o=null):o.innerHTML=n,c=setTimeout(function(){u&&!u.initialized&&u.trigger("Error",new i.RuntimeError(i.RuntimeError.NOT_INIT_ERR))},5e3)},destroy:function(e){return function(){e.call(u),clearTimeout(c),a=c=e=u=null}}(this.destroy)},l)}var s="flash",l={};return r.addConstructor(s,a),l}),i(dn,[cn,be],function(e,t){var n={slice:function(e,n,i,r){var o=this.getRuntime();return 0>n?n=Math.max(e.size+n,0):n>0&&(n=Math.min(n,e.size)),0>i?i=Math.max(e.size+i,0):i>0&&(i=Math.min(i,e.size)),e=o.shimExec.call(this,"Blob","slice",n,i,r||""),e&&(e=new t(o.uid,e)),e}};return e.Blob=n}),i(fn,[cn],function(e){var t={init:function(e){this.getRuntime().shimExec.call(this,"FileInput","init",{name:e.name,accept:e.accept,multiple:e.multiple}),this.trigger("ready")}};return e.FileInput=t}),i(hn,[ce,he,ye,fe],function(e,t,n,i){function r(){function i(){c=d=0,u=this.result=null}function o(t,n){var i=this;l=n,i.bind("TransportingProgress",function(t){d=t.loaded,c>d&&-1===e.inArray(i.state,[r.IDLE,r.DONE])&&a.call(i)},999),i.bind("TransportingComplete",function(){d=c,i.state=r.DONE,u=null,i.result=l.exec.call(i,"Transporter","getAsBlob",t||"")},999),i.state=r.BUSY,i.trigger("TransportingStarted"),a.call(i)}function a(){var e=this,n,i=c-d;f>i&&(f=i),n=t.btoa(u.substr(d,f)),l.exec.call(e,"Transporter","receive",n,c)}var s,l,u,c,d,f;n.call(this),e.extend(this,{uid:e.guid("uid_"),state:r.IDLE,result:null,transport:function(t,n,r){var a=this;if(r=e.extend({chunk_size:204798},r),(s=r.chunk_size%3)&&(r.chunk_size+=3-s),f=r.chunk_size,i.call(this),u=t,c=t.length,"string"===e.typeOf(r)||r.ruid)o.call(a,n,this.connectRuntime(r));else{var l=function(e,t){a.unbind("RuntimeInit",l),o.call(a,n,t)};this.bind("RuntimeInit",l),this.connectRuntime(r)}},abort:function(){var e=this;e.state=r.IDLE,l&&(l.exec.call(e,"Transporter","clear"),e.trigger("TransportingAborted")),i.call(e)},destroy:function(){this.unbindAll(),l=null,this.disconnectRuntime(),i.call(this)}})}return r.IDLE=0,r.BUSY=1,r.DONE=2,r.prototype=i.instance,r}),i(pn,[cn,ce,be,Ct,we,_e,hn],function(e,t,n,i,r,o,a){var s={send:function(e,i){function r(){e.transport=c.mode,c.shimExec.call(u,"XMLHttpRequest","send",e,i)}function s(e,t){c.shimExec.call(u,"XMLHttpRequest","appendBlob",e,t.uid),i=null,r()}function l(e,t){var n=new a;n.bind("TransportingComplete",function(){t(this.result)}),n.transport(e.getSource(),e.type,{ruid:c.uid})}var u=this,c=u.getRuntime();if(t.isEmptyObj(e.headers)||t.each(e.headers,function(e,t){c.shimExec.call(u,"XMLHttpRequest","setRequestHeader",t,e.toString())}),i instanceof o){var d;if(i.each(function(e,t){e instanceof n?d=t:c.shimExec.call(u,"XMLHttpRequest","append",t,e)}),i.hasBlob()){var f=i.getBlob();f.isDetached()?l(f,function(e){f.destroy(),s(d,e)}):s(d,f)}else i=null,r()}else i instanceof n?i.isDetached()?l(i,function(e){i.destroy(),i=e.uid,r()}):(i=i.uid,r()):r()},getResponse:function(e){var n,o,a=this.getRuntime();if(o=a.shimExec.call(this,"XMLHttpRequest","getResponseAsBlob")){if(o=new i(a.uid,o),"blob"===e)return o;try{if(n=new r,~t.inArray(e,["","text"]))return n.readAsText(o);if("json"===e&&window.JSON)return JSON.parse(n.readAsText(o))}finally{o.destroy()}}return null},abort:function(e){var t=this.getRuntime();t.shimExec.call(this,"XMLHttpRequest","abort"),this.dispatchEvent("readystatechange"),this.dispatchEvent("abort")}};return e.XMLHttpRequest=s}),i(mn,[cn,he],function(e,t){function n(e,n){switch(n){case"readAsText":return t.atob(e,"utf8");case"readAsBinaryString":return t.atob(e);case"readAsDataURL":return e}return null}var i={read:function(e,t){var i,r=this.getRuntime();return(i=r.shimExec.call(this,"FileReaderSync","readAsBase64",t.uid))?("readAsDataURL"===e&&(i="data:"+(t.type||"")+";base64,"+i),n(i,e,t.type)):null}};return e.FileReaderSync=i}),a([l,u,c,d,f,h,p,m,g,v,y,x,b,w,_,E,R,C,T,S,A,k,D,N,I,M,F,P,O,H,L,z,B,W,U,j,V,q,X,Y,$,G,J,K,Z,Q,ee,te,ne,ie,re,oe,ae,se,le,ue,ce,de,fe,he,pe,me,ge,ve,ye,xe,be,we,_e,Ee,Re,Ce,Te,Se,Ae,ke,De,Ne,Ie,Me,Fe,Pe,Oe,He,Le,ze,Be,We,Ue,je,Ve,qe,Xe,Ye,$e,Ge,Je,Ke,Ze,Qe,et,tt,nt,it,rt,ot,at,st,lt,ut,ct,dt,ft,ht,pt,mt,gt,vt,yt,xt,bt,wt,_t,Et,Rt,Ct,Tt,St,At,kt,Dt,Nt,It,Mt,Ft,Pt,Ot,Ht,Lt,zt,Bt,Wt,Ut,jt,Vt,qt,Xt,Yt,$t,Gt,Jt,Kt,Zt,Qt,en,tn,nn,rn,on,an,sn,ln,un,cn,dn,fn,hn,pn,mn])}(this);