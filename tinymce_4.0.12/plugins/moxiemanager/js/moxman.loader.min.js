// 2.0.5 (2015-06-30)
(function(exports,undefined){"use strict";var modules={};function require(ids,callback){var module,defs=[];for(var i=0;i<ids.length;++i){module=modules[ids[i]]||resolve(ids[i]);if(!module){throw"module definition dependecy not found: "+ids[i]}defs.push(module)}callback.apply(null,defs)}function define(id,dependencies,definition){if(typeof id!=="string"){throw"invalid module definition, module id must be defined and be a string"}if(dependencies===undefined){throw"invalid module definition, dependencies must be specified"}if(definition===undefined){throw"invalid module definition, definition function must be specified"}require(dependencies,function(){modules[id]=definition.apply(null,arguments)})}function defined(id){return!!modules[id]}function resolve(id){var target=exports;var fragments=id.split(/[.\/]/);for(var fi=0;fi<fragments.length;++fi){if(!target[fragments[fi]]){return}target=target[fragments[fi]]}return target}function expose(ids){for(var i=0;i<ids.length;i++){var target=exports;var id=ids[i];var fragments=id.split(/[.\/]/);for(var fi=0;fi<fragments.length-1;++fi){if(target[fragments[fi]]===undefined){target[fragments[fi]]={}}target=target[fragments[fi]]}target[fragments[fragments.length-1]]=modules[id]}}var __moxman_util_Loader="moxman/util/Loader",__moxman_Env="moxman/Env",__moxman_util_I18n="moxman/util/I18n",__moxman_Loader="moxman/Loader";define(__moxman_util_Loader,[],function(){"use strict";var idCount=0,loadedUrls={};function noop(){}function appendToHead(node){document.getElementsByTagName("head")[0].appendChild(node)}var Loader={maxLoadTime:5,load:function(urls,loadedCallback,errorCallback){var cssFiles=urls.css||[],jsFiles=urls.js||[];function loadNextScript(){if(jsFiles.length){Loader.loadScript(jsFiles.shift(),loadNextScript,errorCallback)}else{loadNextCss()}}function loadNextCss(){if(cssFiles.length){Loader.loadCss(cssFiles.shift(),loadNextCss,errorCallback)}else{loadedCallback()}}loadNextScript()},loadScript:function(url,loadedCallback,errorCallback){var key,script;function done(){loadedUrls[url]=true;loadedCallback()}if(loadedUrls[url]){loadedCallback();return}loadedCallback=loadedCallback||noop;errorCallback=errorCallback||noop;script=document.createElement("script");script.type="text/javascript";if(typeof url=="object"){for(key in url){script.setAttribute(key,url[key])}}else{script.src=url}if("onload"in script){script.onload=done;script.onerror=errorCallback}else{script.onreadystatechange=function(){var state=script.readyState;if(state=="complete"||state=="loaded"){done()}};script.onerror=errorCallback}appendToHead(script)},loadCss:function(url,loadedCallback,errorCallback){var doc=document,link,style,startTime;function done(){loadedUrls[url]=true;loadedCallback()}if(loadedUrls[url]){loadedCallback();return}loadedCallback=loadedCallback||noop;errorCallback=errorCallback||noop;function isOldWebKit(){var webKitChunks=navigator.userAgent.match(/WebKit\/(\d*)/);return!!(webKitChunks&&webKitChunks[1]<536)}function waitForWebKitLinkLoaded(){var styleSheets=doc.styleSheets,file,i=styleSheets.length,owner;while(i--){file=styleSheets[i];owner=file.ownerNode?file.ownerNode:file.owningElement;if(owner&&owner.id===link.id){done();return}}if((new Date).getTime()-startTime<Loader.maxLoadTime*1e3){window.setTimeout(waitForWebKitLinkLoaded,0)}else{errorCallback()}}function waitForGeckoLinkLoaded(){try{var cssRules=style.sheet.cssRules;done();return cssRules}catch(ex){}if((new Date).getTime()-startTime<Loader.maxLoadTime*1e3){window.setTimeout(waitForGeckoLinkLoaded,0)}else{errorCallback()}}link=doc.createElement("link");link.rel="stylesheet";link.type="text/css";link.href=url;link.id="u"+idCount++;startTime=(new Date).getTime();if("onload"in link&&!isOldWebKit()){link.onload=done;link.onerror=errorCallback}else{if(navigator.userAgent.indexOf("Firefox")>0){style=doc.createElement("style");style.textContent='@import "'+url+'"';waitForGeckoLinkLoaded();appendToHead(style);return}else{waitForWebKitLinkLoaded()}}appendToHead(link)}};return Loader});define(__moxman_Env,[],function(){var nav=navigator,userAgent=nav.userAgent,webkit,ie,mac,android,iDevice,phone,tablet;function matchMediaQuery(query){return"matchMedia"in window?matchMedia(query).matches:false}webkit=/WebKit/.test(userAgent);ie=!webkit&&/MSIE/gi.test(userAgent)&&/Explorer/gi.test(nav.appName);ie=ie&&/MSIE (\w+)\./.exec(userAgent)[1];mac=userAgent.indexOf("Mac")!=-1;android=/Android/.test(userAgent);iDevice=/(iPad|iPhone)/.test(userAgent);phone=matchMediaQuery("only screen and (max-device-width: 480px)")&&(android||iDevice);tablet=matchMediaQuery("only screen and (min-width: 800px)")&&(android||iDevice);return{apiPageName:"api.php",ie:ie,ie7:!("createRange"in document)&&"all"in document&&!window.opera&&!document.documentMode,mac:mac,phone:phone,tablet:tablet,desktop:!phone&&!tablet}});define(__moxman_util_I18n,[],function(){"use strict";function resolve(id){var target=window;var fragments=id.split(/\//);for(var fi=0;fi<fragments.length;++fi){if(!target[fragments[fi]]){return}target=target[fragments[fi]]}return target}var I18n=resolve("moxman/util/I18n");if(I18n){return I18n}var data={};return{add:function(code,items){for(var name in items){data[name]=items[name]}},translate:function(text,defaultText){if(typeof text=="undefined"){return text}if(typeof text!="string"&&"raw"in text){return text.raw}if(text.push){var values=text.slice(1);text=(data[text[0]]||text[0]).replace(/\{([^\}]+)\}/g,function(match1,match2){return values[match2]})}return data[text]||defaultText||text},data:data}});define(__moxman_Loader,[__moxman_util_Loader,__moxman_Env,__moxman_util_I18n],function(ResourceLoader,Env,I18n){var exports=this||window;function createThrobber(){var blocker=document.createElement("div"),throbber=document.createElement("div"),styleElm=document.createElement("style"),dot,head,i;blocker.id="moxman-modal-block";blocker.className="moxman-reset moxman-fade";throbber.className="moxman-reset moxman-loader-throbber";styleElm=document.createElement("style");styleElm.type="text/css";function getAnim(sec){var line="moxman-loader-scale 0.75s "+sec+" infinite cubic-bezier(.2, .68, .18, 1.08);";return"-webkit-animation: "+line+"animation: "+line}var animKeyFramesCss="keyframes moxman-loader-scale {"+"0% {"+"-webkit-transform: scale(1);"+"transform: scale(1);"+"opacity: 1;"+"}"+"45% {"+"-webkit-transform: scale(0.1);"+"transform: scale(0.1);"+"opacity: 0.7;"+"}"+"80% {"+"-webkit-transform: scale(1);"+"transform: scale(1);"+"opacity: 1;"+"}"+"}";var css=".moxman-reset {"+"margin: 0; padding: 0; border: 0; outline: 0;"+"vertical-align: top; background: transparent;"+"font-size: @font-size; position: static;"+"width: auto; height: auto;"+"white-space: nowrap; cursor: inherit;"+"-webkit-tap-highlight-color: transparent;"+"line-height: normal; font-weight: normal;"+"text-align: left; float: none;"+"-moz-box-sizing: content-box;"+"-webkit-box-sizing: content-box;"+"box-sizing: content-box;"+"direction: ltr;"+"max-width: none;"+"}"+".moxman-fade {"+"opacity: 0;"+"filter: alpha(opacity=30);"+"transition: opacity 0.15s linear;"+"}"+".moxman-in {"+"opacity: 0.3;"+"}"+"#moxman-modal-block {"+"position: fixed;"+"left: 0; top: 0;"+"width: 100%; height: 100%;"+"background: #000;"+"}"+"@-webkit-"+animKeyFramesCss+"@"+animKeyFramesCss+".moxman-loader-throbber {"+"position: fixed;"+"top: 50%; left: 50%;"+"width: 60px; height: 60px;"+"margin-top: -30px;"+"margin-left: -30px;"+"}"+".moxman-loader-throbber > div:nth-child(1) {"+getAnim("-0.24s")+"}"+".moxman-loader-throbber > div:nth-child(2) {"+getAnim("-0.12s")+"}"+".moxman-loader-throbber > div:nth-child(3) {"+getAnim("-0s")+"}"+".moxman-loader-throbber > .moxman-loader-throbber-dot {"+"background-color: #000;"+"width: 15px;"+"height: 15px;"+"border-radius: 100%;"+"margin: 2px;"+"-webkit-animation-fill-mode: both;"+"animation-fill-mode: both;"+"display: inline-block;"+"}";if(styleElm.styleSheet){styleElm.styleSheet.cssText=css}else{styleElm.appendChild(document.createTextNode(css))}head=document.getElementsByTagName("head")[0];if(head.firstChild){head.insertBefore(styleElm,head.firstChild)}else{head.appendChild(styleElm)}blocker.style.zIndex=16777215;throbber.style.zIndex=16777215;document.body.appendChild(blocker);document.body.appendChild(throbber);for(i=0;i<3;i++){dot=document.createElement("div");dot.className="moxman-reset moxman-loader-throbber-dot";throbber.appendChild(dot)}setTimeout(function(){blocker.className+=" moxman-in"},0);return{hide:function(){throbber.parentNode.removeChild(throbber);styleElm.parentNode.removeChild(styleElm)}}}if("toJSON"in Object.prototype){alert("MoxieManager detected an old prototype.js version that breaks compatibility.");return}function loadAndRender(view){return function(settings){var langCode,cacheSuffix=Env.cacheSuffix,throbber;function appendCacheSuffix(url){if(cacheSuffix){cacheSuffix=cacheSuffix.replace(/^[?& ]+/,"");if(cacheSuffix){url+=(url.indexOf("?")===-1?"?":"&")+cacheSuffix}}return url}settings=settings||{};if(!Env.baseUrl){var scripts=document.getElementsByTagName("script");for(var i=0;i<scripts.length;i++){var src=scripts[i].src;if(/(^|\/)moxman\./.test(src)){cacheSuffix=Env.cacheSuffix||src.split("?")[1];Env.baseUrl=src.substring(0,src.lastIndexOf("/"))+"/..";Env.apiPageUrl=Env.baseUrl+"/"+Env.apiPageName;break}}}langCode=settings.language||Env.language;if(!moxman.ui){if(throbber){return}throbber=createThrobber();ResourceLoader.load({js:[appendCacheSuffix(Env.baseUrl+"/js/moxman.api.min.js"),Env.apiPageUrl+"?action=language"+(langCode?"&code="+langCode:"")],css:[appendCacheSuffix(Env.baseUrl+"/skins/"+(settings.skin||"lightgray")+"/skin"+(Env.ie7?".ie7":"")+".min.css")]},function(){throbber.hide();moxman.ui.FloatPanel.zIndex=settings.zIndex||moxman.ui.FloatPanel.zIndex;moxman.Dialogs.loaded(Env);moxman.Dialogs[view](settings)})}else{moxman.Dialogs[view](settings)}}}var Loader={browse:loadAndRender("browse"),upload:loadAndRender("upload"),edit:loadAndRender("edit"),zip:loadAndRender("zip"),createDir:loadAndRender("createDir"),createDoc:loadAndRender("createDoc"),view:loadAndRender("view"),rename:loadAndRender("rename")};exports.moxman=exports.moxman||{};for(var name in Loader){exports.moxman[name]=Loader[name]}exports.moxman.addI18n=I18n.add;return Loader});expose([__moxman_util_Loader,__moxman_Env,__moxman_util_I18n,__moxman_Loader])})(this);