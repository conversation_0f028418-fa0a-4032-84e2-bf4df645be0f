<?php
/**
 * api.php
 *
 * Copyright 2003-2014, Moxiecode Systems AB, All rights reserved.
 */

// cb 2023-12-28
require_once(dirname(__DIR__,3) . '/inc/KD_sessions.inc.php');
session_set_save_handler("sessionOpen",
			"sessionClose",
			"sessionRead",
			"sessionWrite",
			"sessionDestroy",
			"sessionGC");
session_start();


try {
	require_once('./classes/MOXMAN.php');

	define("MOXMAN_API_FILE", __FILE__);

	$context = MOXMAN_Http_Context::getCurrent();
	$pluginManager = MOXMAN::getPluginManager();

	foreach ($pluginManager->getAll() as $plugin) {
		if ($plugin instanceof MOXMAN_Http_IHandler) {
			$plugin->processRequest($context);
		}
	}
} catch (Exception $e) {
	MOXMAN_Exception::printException($e);
}
?>