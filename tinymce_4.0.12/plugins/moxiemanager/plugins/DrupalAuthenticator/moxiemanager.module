<?php

/**
 * Implementation of hook_wysiwyg_plugin().
 */
function moxiemanager_wysiwyg_plugin($editor, $version) {
	switch ($editor) {
		case 'tinymce':
			return array(
				'moxiemanager' => array(
					'path' => drupal_get_path('module', 'moxiemanager') .'/../..',
					'filename' => 'editor_plugin.js',
					'buttons' => array('insertfile' => t('Insert file')),
					'extensions' => array('moxiemanager' => t('moxiemanager')),
					'url' => 'http://www.moxiemanager.com',
					'load' => TRUE
				)
			);
		break;
	}
}
?>