/**
 * Skill Progress Circle Animation Controller
 * Triggers animations when the section scrolls into view
 */

document.addEventListener('DOMContentLoaded', function() {
    // Create intersection observer to detect when section is in view
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Section is in view, start animations
                const progressCircles = entry.target.querySelectorAll('.skill-progress');
                progressCircles.forEach(circle => {
                    circle.classList.add('animate');
                });

                // Optional: Stop observing after animation starts (one-time trigger)
                observer.unobserve(entry.target);
            }
        });
    }, {
        // Trigger when 30% of the section is visible
        threshold: 0.3,
        // Start observing 100px before the section enters viewport
        rootMargin: '0px 0px -100px 0px'
    });

    // Find and observe the impact skills section
    const skillsSection = document.querySelector('.section.impact_skills, [class*="impact_skills"]');
    if (skillsSection) {
        observer.observe(skillsSection);
    }

    // Fallback: Also look for any section containing skill-progress elements
    const sectionsWithSkillProgress = document.querySelectorAll('section:has(.skill-progress)');
    sectionsWithSkillProgress.forEach(section => {
        observer.observe(section);
    });

    // Additional fallback for older browsers that don't support :has()
    if (sectionsWithSkillProgress.length === 0) {
        const allSections = document.querySelectorAll('section');
        allSections.forEach(section => {
            if (section.querySelector('.skill-progress')) {
                observer.observe(section);
            }
        });
    }

    // Impact Education Progress Bar Animation
    // Create separate observer for impact education section
    const progressBarObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Section is in view, start progress bar animations
                console.log('Impact education section in view, starting animation');

                // Find progress bars and log their attributes for debugging
                const progressBars = entry.target.querySelectorAll('.progress-bar');
                console.log(`Found ${progressBars.length} progress bars`);

                // Log progress bar details for debugging
                progressBars.forEach((bar, index) => {
                    const ariaValue = bar.getAttribute('aria-valuenow');
                    const styleWidth = bar.style.width;
                    console.log(`Progress bar ${index + 1}: aria-valuenow="${ariaValue}", style.width="${styleWidth}"`);
                });

                // Add animation class to trigger CSS animations
                entry.target.classList.add('animate-progress');
                console.log('Added animate-progress class to section');

                // Force immediate style update for debugging
                setTimeout(() => {
                    progressBars.forEach((bar, index) => {
                        const computedStyle = window.getComputedStyle(bar);
                        console.log(`Progress bar ${index + 1} computed width after animation: ${computedStyle.width}`);
                    });
                }, 100);

                // Optional: Stop observing after animation starts (one-time trigger)
                progressBarObserver.unobserve(entry.target);
            }
        });
    }, {
        // Trigger when 30% of the section is visible
        threshold: 0.3,
        // Start observing 50px before the section enters viewport
        rootMargin: '0px 0px -50px 0px'
    });

    // Find the impact education section by ID (most reliable method)
    const impactEducationSection = document.getElementById('impact_education');

    if (impactEducationSection) {
        console.log('Found impact education section by ID');

        // Check that it has the expected progress bars
        const progressBars = impactEducationSection.querySelectorAll('.progress-bar');
        console.log(`Section has ${progressBars.length} progress bars`);

        if (progressBars.length >= 2) {
            progressBarObserver.observe(impactEducationSection);
            console.log('Started observing impact education section');
        } else {
            console.log('Section found but does not have expected progress bars');
        }
    } else {
        console.log('Impact education section not found by ID, trying fallback methods');

        // Fallback: Look for sections containing the specific pattern
        const allSections = document.querySelectorAll('section');
        let foundSection = false;

        allSections.forEach(section => {
            const hasGraduationCap = section.querySelector('.fa-graduation-cap');
            const progressBars = section.querySelectorAll('.progress-bar');
            const hasMultipleProgressBars = progressBars.length >= 2;

            if (hasGraduationCap && hasMultipleProgressBars) {
                foundSection = true;
                progressBarObserver.observe(section);
                console.log('Found impact education section by content pattern (fallback)');
            }
        });

        if (!foundSection) {
            console.log('No impact education sections found');
        }
    }

    // Impact Participation Progress Bar Animation
    // Create separate observer for impact participation section
    const participationProgressBarObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Section is in view, start progress bar animation
                console.log('Impact participation section in view, starting animation');

                // Find progress bars and log their attributes for debugging
                const progressBars = entry.target.querySelectorAll('.progress-bar');
                console.log(`Found ${progressBars.length} progress bars in participation section`);

                // Log progress bar details for debugging
                progressBars.forEach((bar, index) => {
                    const ariaValue = bar.getAttribute('aria-valuenow');
                    const styleWidth = bar.style.width;
                    console.log(`Participation progress bar ${index + 1}: aria-valuenow="${ariaValue}", style.width="${styleWidth}"`);
                });

                // Add animation class to trigger CSS animations
                entry.target.classList.add('animate-progress');
                console.log('Added animate-progress class to participation section');

                // Force immediate style update for debugging
                setTimeout(() => {
                    progressBars.forEach((bar, index) => {
                        const computedStyle = window.getComputedStyle(bar);
                        console.log(`Participation progress bar ${index + 1} computed width after animation: ${computedStyle.width}`);
                    });
                }, 100);

                // Optional: Stop observing after animation starts (one-time trigger)
                participationProgressBarObserver.unobserve(entry.target);
            }
        });
    }, {
        // Trigger when 30% of the section is visible
        threshold: 0.3,
        // Start observing 50px before the section enters viewport
        rootMargin: '0px 0px -50px 0px'
    });

    // Find the impact participation section by ID (most reliable method)
    const impactParticipationSection = document.getElementById('impact_participation');

    if (impactParticipationSection) {
        console.log('Found impact participation section by ID');

        // Check that it has the expected progress bar
        const progressBars = impactParticipationSection.querySelectorAll('.progress-bar');
        console.log(`Participation section has ${progressBars.length} progress bars`);

        if (progressBars.length >= 1) {
            participationProgressBarObserver.observe(impactParticipationSection);
            console.log('Started observing impact participation section');
        } else {
            console.log('Participation section found but does not have expected progress bars');
        }
    } else {
        console.log('Impact participation section not found by ID, trying fallback methods');

        // Fallback: Look for sections containing the specific pattern
        const allSections = document.querySelectorAll('section');
        let foundParticipationSection = false;

        allSections.forEach(section => {
            const hasProgressContainer = section.querySelector('.progress-container');
            const progressBars = section.querySelectorAll('.progress-bar');
            const hasParticipationContent = section.querySelector('.participant-demographics');

            if (hasProgressContainer && progressBars.length >= 1 && hasParticipationContent) {
                foundParticipationSection = true;
                participationProgressBarObserver.observe(section);
                console.log('Found impact participation section by content pattern (fallback)');
            }
        });

        if (!foundParticipationSection) {
            console.log('No impact participation sections found');
        }
    }

    // Impact Participation Chart Images Animation
    // Create separate observer for participant demographics section
    const participationChartsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Participant demographics section is in view, start chart animations
                console.log('Participant demographics section in view, starting chart animations');

                // Find chart images and log for debugging
                const chartImages = entry.target.querySelectorAll('.stats-chart-1 img.chart-img, .stats-chart-2 img.chart-img');
                console.log(`Found ${chartImages.length} chart images in participant demographics`);

                // Add animation class to trigger CSS animations
                entry.target.classList.add('animate-charts');
                console.log('Added animate-charts class to participant demographics section');

                // Optional: Stop observing after animation starts (one-time trigger)
                participationChartsObserver.unobserve(entry.target);
            }
        });
    }, {
        // Trigger when 30% of the section is visible
        threshold: 0.3,
        // Start observing 50px before the section enters viewport
        rootMargin: '0px 0px -50px 0px'
    });

    // Find the participant demographics section
    const participantDemographicsSection = document.querySelector('#impact_participation .participant-demographics');

    if (participantDemographicsSection) {
        console.log('Found participant demographics section');

        // Check that it has the expected chart images
        const chartImages = participantDemographicsSection.querySelectorAll('.stats-chart-1 img.chart-img, .stats-chart-2 img.chart-img');
        console.log(`Demographics section has ${chartImages.length} chart images`);

        if (chartImages.length >= 2) {
            participationChartsObserver.observe(participantDemographicsSection);
            console.log('Started observing participant demographics section for chart animations');
        } else {
            console.log('Demographics section found but does not have expected chart images');
        }
    } else {
        console.log('Participant demographics section not found, trying fallback methods');

        // Fallback: Look for sections containing the specific pattern
        const allDemographicsSections = document.querySelectorAll('.participant-demographics');
        let foundDemographicsSection = false;

        allDemographicsSections.forEach(section => {
            const chartImages = section.querySelectorAll('.stats-chart-1 img.chart-img, .stats-chart-2 img.chart-img');

            if (chartImages.length >= 2) {
                foundDemographicsSection = true;
                participationChartsObserver.observe(section);
                console.log('Found participant demographics section by class pattern (fallback)');
            }
        });

        if (!foundDemographicsSection) {
            console.log('No participant demographics sections found');
        }
    }
});