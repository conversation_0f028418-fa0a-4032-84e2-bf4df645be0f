/**
 * Skill Progress Circle Animation Controller
 * Triggers animations when the section scrolls into view
 */

document.addEventListener('DOMContentLoaded', function() {
    // Create intersection observer to detect when section is in view
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Section is in view, start animations
                const progressCircles = entry.target.querySelectorAll('.skill-progress');
                progressCircles.forEach(circle => {
                    circle.classList.add('animate');
                });

                // Optional: Stop observing after animation starts (one-time trigger)
                observer.unobserve(entry.target);
            }
        });
    }, {
        // Trigger when 30% of the section is visible
        threshold: 0.3,
        // Start observing 100px before the section enters viewport
        rootMargin: '0px 0px -100px 0px'
    });

    // Find and observe the impact skills section
    const skillsSection = document.querySelector('.section.impact_skills, [class*="impact_skills"]');
    if (skillsSection) {
        observer.observe(skillsSection);
    }

    // Fallback: Also look for any section containing skill-progress elements
    const sectionsWithSkillProgress = document.querySelectorAll('section:has(.skill-progress)');
    sectionsWithSkillProgress.forEach(section => {
        observer.observe(section);
    });

    // Additional fallback for older browsers that don't support :has()
    if (sectionsWithSkillProgress.length === 0) {
        const allSections = document.querySelectorAll('section');
        allSections.forEach(section => {
            if (section.querySelector('.skill-progress')) {
                observer.observe(section);
            }
        });
    }

    // Impact Education Progress Bar Animation
    // Create separate observer for impact education section
    const progressBarObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Section is in view, start progress bar animations
                console.log('Impact education section in view, starting animation');

                // Find progress bars and log their attributes for debugging
                const progressBars = entry.target.querySelectorAll('.progress-bar');
                console.log(`Found ${progressBars.length} progress bars`);

                // Log progress bar details for debugging
                progressBars.forEach((bar, index) => {
                    const ariaValue = bar.getAttribute('aria-valuenow');
                    const styleWidth = bar.style.width;
                    console.log(`Progress bar ${index + 1}: aria-valuenow="${ariaValue}", style.width="${styleWidth}"`);
                });

                // Add animation class to trigger CSS animations
                entry.target.classList.add('animate-progress');
                console.log('Added animate-progress class to section');

                // Force immediate style update for debugging
                setTimeout(() => {
                    progressBars.forEach((bar, index) => {
                        const computedStyle = window.getComputedStyle(bar);
                        console.log(`Progress bar ${index + 1} computed width after animation: ${computedStyle.width}`);
                    });
                }, 100);

                // Optional: Stop observing after animation starts (one-time trigger)
                progressBarObserver.unobserve(entry.target);
            }
        });
    }, {
        // Trigger when 30% of the section is visible
        threshold: 0.3,
        // Start observing 50px before the section enters viewport
        rootMargin: '0px 0px -50px 0px'
    });

    // Find the impact education section by ID (most reliable method)
    const impactEducationSection = document.getElementById('impact_education');

    if (impactEducationSection) {
        console.log('Found impact education section by ID');

        // Check that it has the expected progress bars
        const progressBars = impactEducationSection.querySelectorAll('.progress-bar');
        console.log(`Section has ${progressBars.length} progress bars`);

        if (progressBars.length >= 2) {
            progressBarObserver.observe(impactEducationSection);
            console.log('Started observing impact education section');
        } else {
            console.log('Section found but does not have expected progress bars');
        }
    } else {
        console.log('Impact education section not found by ID, trying fallback methods');

        // Fallback: Look for sections containing the specific pattern
        const allSections = document.querySelectorAll('section');
        let foundSection = false;

        allSections.forEach(section => {
            const hasGraduationCap = section.querySelector('.fa-graduation-cap');
            const progressBars = section.querySelectorAll('.progress-bar');
            const hasMultipleProgressBars = progressBars.length >= 2;

            if (hasGraduationCap && hasMultipleProgressBars) {
                foundSection = true;
                progressBarObserver.observe(section);
                console.log('Found impact education section by content pattern (fallback)');
            }
        });

        if (!foundSection) {
            console.log('No impact education sections found');
        }
    }
});