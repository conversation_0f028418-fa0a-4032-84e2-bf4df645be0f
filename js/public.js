$(document).ready(function(){
    
    // show or hide clearer icon on page load
    $('.hasclear').each(function(){
      var t = $(this);
      if(t.val() != ''){
        t.next('span').show();
      } else {
        t.next('span').hide();
      }
    });
    
    // clear icon for form fields
    $('.hasclear').keyup(function () {
        var t = $(this);
        t.next('span').toggle(Bo<PERSON>an(t.val()));
    });
    
    $('.clearer').click(function () {
        $(this).prev('input').val('').focus();
        // ajax to clear session var
        $.ajax({
          url : "/ajax_clear_search.html",
          type : 'GET',
          data : {"clear_search":"1"}
        });
        $(this).hide();
    });
    
    // scroll to target ID for jump links
    var offsetSize = $("#site_header").outerHeight(true) + 10;
    if( window.location.hash && $(window.location.hash).length ) {
	    $("html, body").animate({scrollTop:$(window.location.hash).offset().top - offsetSize }, 300);
	}

});
