<?php
/**
 * File: section.articles_listing.inc.php
 * 
 * @see "README" in this folder for instructions on usage. ;)
 *    
 */
if(!class_exists('articles_listing')) {

    /**
     * PageSection class.
     */
    class articles_listing
        extends AbstractPageSection 
        implements PageSectionInterface
        {

        /**
         * @var Page $obj
         */
        
        /////////////////////////////////////////////////
        /** 
         * Section HTML Template
         * This required HTML code will be used to render the final section content.
         * You can use tokens (see above) and you can run loops or any other PHP tricks you like.
         *
         * @returns string
         */
        public function section_html_template() {

            $node_html = '';
            $node_array = $this->get_node_array();

            foreach($node_array as $i => $node) {
                $node_html .= '
                
                <h2 class="h4 text-primary-800">{'.$i.':headline}</h2>
                <div class="col-12 col-md-9">
                {'.$i.':node_text}
                </div>
                {button_html}
                ';
            }

            $section_html = '

            <section 
                id="'.htmlspecialchars($this->section_row['css_id'],ENT_COMPAT).'" 
                class="section '.htmlspecialchars($this->section_type_row['css_class'],ENT_COMPAT).' '.htmlspecialchars($this->section_row['css_class'],ENT_COMPAT).'"
                >
                <div class="container-xl">
                    ' . $this->obj->show_list('list_C_articles.',array('view_where'=>"item_active=1 AND category!='news'")) . '
                </div>                
            </section>
            ';
            return $section_html;
        }
            
        /////////////////////////////////////////////////
        /** 
         * Image HTML Template
         * This optional HTML code will be used to wrap any present images
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:image_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function image_html_template() { 
            $image_html = '
            <img src="/images/pages/{image}" alt="{alt_text}" class="img-fluid">
            ';
            return $image_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button HTML Template
         * This optional HTML code will be used to wrap any present buton text/URL
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:button_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function button_html_template() {
            $button_html = '
            <a href="{url}" {external} class="btn btn-blue {css_class}">{text}</a>
            ';
            return $button_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button Wrapper HTML Template
         *
         * @returns string
         */
        public function button_wrapper_html_template() {
            $button_html = '
            <p class="mt-auto d-flex flex-row flex-wrap gap-3 justify-content-center justify-content-md-start aign-items-center">{button_html}</p>
            ';
            return $button_html;
        }
        
    } // end class
    
} // end if
