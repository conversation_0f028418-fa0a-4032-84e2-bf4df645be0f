<?php
/**
 * File: section.impact_skills.inc.php
 * 
 * @see "README" in this folder for instructions on usage. ;)
 *    
 */
if(!class_exists('impact_skills')) {

    /**
     * PageSection class.
     */
    class impact_skills
        extends AbstractPageSection 
        implements PageSectionInterface
        {

        /**
         * @var Page $obj
         */
        
        /////////////////////////////////////////////////
        /** 
         * Section HTML Template
         * This required HTML code will be used to render the final section content.
         * You can use tokens (see above) and you can run loops or any other PHP tricks you like.
         *
         * @returns string
         */
        public function section_html_template() {

            $node_html = '';
            $node_array = $this->get_node_array();

            foreach($node_array as $i => $node) {
                $node_html .= '
                
                <h2 class="">{'.$i.':headline}</h2>
                <div class="col-12 col-md-9">
                {'.$i.':node_text}
                </div>
                {button_html}
                ';
            }

            $background_image = '';
            if ($this->section_row['attachment_filename']) {
                $background_image = ' style="background-image: url(/images/pages/'.htmlspecialchars($this->section_row['attachment_filename'],ENT_COMPAT).');"';
            }

            $section_headline = '';
            if ($this->section_row['section_title']) {
                $section_headline = '
                <h2 class="mb-5 text-center">' . htmlspecialchars($this->section_row['section_title'],ENT_COMPAT) . '</h2>
                ';
            }

            $section_html = '
            <section 
                id="'.htmlspecialchars($this->section_row['css_id'],ENT_COMPAT).'" 
                class="section pt-0
                    '.htmlspecialchars($this->section_type_row['css_class'],ENT_COMPAT).' 
                    '.htmlspecialchars($this->section_row['css_class'],ENT_COMPAT).'
                " 
                ' . $background_image . '
                >
                <div class="container-xl section-content">
                    <div class="divider section-spacing bg-red mt-0"></div>
                    ' . $section_headline . '
                    <div class="skills-container d-flex flex-wrap flex-row justify-content-center align-items-stretch">';

            // Define the percentage values and titles for each progress circle
            $percentages = [90, 95, 93, 94, 95];
            $titles = [
                'Communication',
                'Leadership',
                'Conflict Resolution',
                'Problem Solving',
                'Time Management'
            ];

            // Generate progress circles dynamically
            foreach($percentages as $index => $percentage) {
                // Get the corresponding title
                $title = $titles[$index];

                // Calculate rotation values for CSS custom properties
                $rightRotation = min(180, $percentage * 3.6);
                $leftRotation = max(0, ($percentage - 50) * 3.6);
                $leftAnimationState = $percentage > 50 ? 'running' : 'paused';

                // Calculate staggered animation delay (0.25 second between each circle)
                $animationDelay = $index * 0.25; // 0s, 0.25s, 0.5s, 0.75s, 1s
                $leftAnimationDelay = $animationDelay + 1.62; // Add right animation duration

                // Generate inline style with calculated values
                $style = "--percentage: {$percentage}; --right-rotation: {$rightRotation}deg; --left-rotation: {$leftRotation}deg; --left-animation-state: {$leftAnimationState}; --animation-delay: {$animationDelay}s; --left-animation-delay: {$leftAnimationDelay}s;";

                $section_html .= '
                        <div class="skill-item d-flex flex-column">
                            <h3 class="h4 text-center mb-4">' . htmlspecialchars($title, ENT_COMPAT) . '</h3>
                            <div class="skill-progress mt-auto" style="' . $style . '">
                                <span class="skill-progress-left">
                                  <span class="skill-progress-bar"></span>
                                </span>
                                <span class="skill-progress-right">
                                    <span class="skill-progress-bar"></span>
                                </span>
                                <div class="skill-progress-value fs-24 fw-semibold">' . $percentage . '%</div>
                            </div>
                        </div>';
            }

            $section_html .= '
                    </div>
                </div>
            </section>

            ';
            return $section_html;
        }
            
        /////////////////////////////////////////////////
        /** 
         * Image HTML Template
         * This optional HTML code will be used to wrap any present images
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:image_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function image_html_template() { 
            $image_html = '
            <img src="/images/pages/{image}" alt="{alt_text}" class="img-fluid">
            ';
            return $image_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button HTML Template
         * This optional HTML code will be used to wrap any present buton text/URL
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:button_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function button_html_template() {
            $button_html = '
            <a href="{url}" {external} class="btn btn-blue {css_class}">{text}</a>
            ';
            return $button_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button Wrapper HTML Template
         *
         * @returns string
         */
        public function button_wrapper_html_template() {
            $button_html = '
            <p class="mt-auto d-flex flex-row flex-wrap gap-3 justify-content-center justify-content-md-start aign-items-center">{button_html}</p>
            ';
            return $button_html;
        }
        
    } // end class
    
} // end if

