<?php
/**
 * File: section_type.section_text_col_image_col_padding.inc.php
 * 
 * @see "README" in this folder for instructions on usage. ;)
 *    
 */
if(!class_exists('section_text_col_image_col_padding')) {

    /**
     * PageSection class.
     */
    class section_text_col_image_col_padding
        extends AbstractPageSection 
        implements PageSectionInterface
        {

        /**
         * @var Page $obj
         */
        
        /////////////////////////////////////////////////
        /** 
         * Section HTML Template
         * This required HTML code will be used to render the final section content.
         * You can use tokens (see above) and you can run loops or any other PHP tricks you like.
         *
         * @returns string
         */
        public function section_html_template() {

            $node_html = '';
            $node_array = $this->get_node_array();

            $flex_order_image = '';
            $flex_order_text = '';
            if($this->section_row['option_text_layout'] == 'text-left-image-right') {
                $flex_order_image = 'order-md-2';
                $flex_order_text = 'order-md-1';
            }
            elseif ($this->section_row['option_text_layout'] == 'text-right-image-left') {
                $flex_order_image = 'order-md-1';
                $flex_order_text = 'order-md-2';
            }
            // DEFAULT -- text-right-image-left
            else {
                $flex_order_image = 'order-md-1';
                $flex_order_text = 'order-md-2';
            }

            foreach($node_array as $i => $node) {
                $node_headline = '';
                if( $node['headline'] ) {
                    $node_headline = '<h2>'.$node['headline'].'</h2>';
                }

                $node_html .= '
                <div class="section-row row flex-column flex-md-row justify-content-start justify-content-md-between align-items-start align-items-md-center">
                    <div class="col-12 col-md-6 ' . $flex_order_image . ' text-center section-image mb-4 mb-md-0">
                        {'.$i.':image_html}
                    </div>
                    <div class="col-12 col-md-6 col-xl-5 ' . $flex_order_text . ' section-content">
                        '.$node_headline.'
                        {'.$i.':node_text}
                        {'.$i.':button_html}
                    </div>
                </div>
                ';
            }

            $background_image = '';
            if ($this->section_row['attachment_filename']) {
                $background_image = ' style="background-image: url(/images/pages/'.htmlspecialchars($this->section_row['attachment_filename'],ENT_COMPAT).');"';
            }
            
            $section_html = '

            <section 
                id="'.htmlspecialchars($this->section_row['css_id'],ENT_COMPAT).'" 
                class="section 
                    '.htmlspecialchars($this->section_type_row['css_class'],ENT_COMPAT).' 
                    '.htmlspecialchars($this->section_row['css_class'],ENT_COMPAT).'
                    '.htmlspecialchars($this->section_row['option_background_color'],ENT_COMPAT).'
                    '.htmlspecialchars($this->section_row['option_text_layout'],ENT_COMPAT).'
                " 
                ' . $background_image . '
                >
                <div class="container-xl">
                    ' . $node_html . '
                </div>
            </section>

            ';
            return $section_html;
        }
            
        /////////////////////////////////////////////////
        /** 
         * Image HTML Template
         * This optional HTML code will be used to wrap any present images
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:image_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function image_html_template() { 
            $image_html = '
            <img src="/images/pages/{image}" alt="{alt_text}" class="img-fluid">
            ';
            return $image_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button HTML Template
         * This optional HTML code will be used to wrap any present buton text/URL
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:button_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function button_html_template() {
            $btn_color = '';
            if($this->section_row['option_background_color'] == 'text-bg-white') {
                $btn_color = 'btn-blue';
            }
            elseif ($this->section_row['option_background_color'] == 'text-bg-blue') {
                $btn_color = 'btn-white';
            }
            elseif ($this->section_row['option_background_color'] == 'text-bg-gray-900') {
                $btn_color = 'btn-outline-white';
            }
            // DEFAULT -- white bg, dark text, blue buttons
            else {
                $btn_color = 'btn-outline-blue';
            }
            $button_html = '
            <a href="{url}" {external} class="btn ' . $btn_color . ' {css_class}">{text}</a>
            ';
            return $button_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button Wrapper HTML Template
         *
         * @returns string
         */
        public function button_wrapper_html_template() {
            $button_html = '
            <p class="d-flex flex-row flex-wrap gap-3 justify-content-start align-items-center mt-5">{button_html}</p>
            ';
            return $button_html;
        }
        
    } // end class
    
} // end if



