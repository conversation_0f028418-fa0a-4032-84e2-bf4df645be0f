<?php
/**
 * File: section_type.section_hero_image.inc.php
 * 
 * @see "README" in this folder for instructions on usage. ;)
 *    
 */
if(!class_exists('section_hero_image')) {

    /**
     * PageSection class.
     */
    class section_hero_image
        extends AbstractPageSection 
        implements PageSectionInterface
        {

        /**
         * @var Page $obj
         */
        
        /////////////////////////////////////////////////
        /** 
         * Section HTML Template
         * This required HTML code will be used to render the final section content.
         * You can use tokens (see above) and you can run loops or any other PHP tricks you like.
         *
         * @returns string
         */
        public function section_html_template() {

            $node_html = '';
            $node_array = $this->get_node_array();
            
            foreach($node_array as $i => $node) {
                $node_headline = '';
                if( $node['headline'] ) {
                    $node_headline = '<h1>'.$node['headline'].'</h1>';
                }

                $node_html .= '
                <div class="section-row row flex-grow-1 flex-column flex-md-row ' . $flex_justify . ' align-items-center">
                    <div class="col-12 d-block d-md-none mb-4 p-0 section-image">
                        <img src="/images/pages/'.htmlspecialchars($this->section_row['attachment_filename'],ENT_COMPAT).'" alt="" class="img-fluid">
                    </div>
                    <div class="col-12 col-md-7 section-content text-center ' . $text_md_align . ' py-3 py-md-0">
                        '.$node_headline.'
                        {'.$i.':node_text}
                        {'.$i.':button_html}
                    </div>
                </div>
                ';
            }

            $background_image = '';
            if ($this->section_row['attachment_filename']) {
                $background_image = ' style="background-image: url(/images/pages/'.htmlspecialchars($this->section_row['attachment_filename'],ENT_COMPAT).');"';
            }
            
            $section_html = '

            <section 
                id="'.htmlspecialchars($this->section_row['css_id'],ENT_COMPAT).'" 
                class="section section-hero hero-image p-0 
                    '.htmlspecialchars($this->section_type_row['css_class'],ENT_COMPAT).'
                    '.htmlspecialchars($this->section_row['css_class'],ENT_COMPAT).'
                " 
                ' . $background_image . '
            >
            </section>

            ';
            return $section_html;
        }
            
        /////////////////////////////////////////////////
        /** 
         * Image HTML Template
         * This optional HTML code will be used to wrap any present images
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:image_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function image_html_template() { 
            $image_html = '
            <img src="/images/pages/{image}" alt="{alt_text}" class="img-fluid">
            ';
            return $image_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button HTML Template
         * This optional HTML code will be used to wrap any present buton text/URL
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:button_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function button_html_template() {
            $btn_color = '';
            if($this->section_row['option_background_color'] == 'text-bg-white') {
                $btn_color = 'btn-blue';
            }
            elseif ($this->section_row['option_background_color'] == 'text-bg-blue') {
                $btn_color = 'btn-white';
            }
            $button_html = '
            <a href="{url}" {external} class="btn ' . $btn_color . ' {css_class}">{text}</a>
            ';
            return $button_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button Wrapper HTML Template
         *
         * @returns string
         */
        public function button_wrapper_html_template() {
            $btn_md_align = '';
            if($this->section_row['option_text_layout'] == 'hero-text-left') {
                $btn_md_align = 'justify-content-md-start';
            }
            elseif ($this->section_row['option_text_layout'] == 'hero-text-right') {
                $btn_md_align = 'justify-content-md-end';
            }
            $button_html = '
            <p class="d-flex flex-row flex-wrap gap-3 justify-content-center ' . $btn_md_align . ' aign-items-center mt-5">{button_html}</p>
            ';
            return $button_html;
        }
        
    } // end class
    
} // end if


