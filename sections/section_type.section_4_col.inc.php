<?php
/**
 * File: section_type.section_4_col.inc.php
 *
 * @see "README" in this folder for instructions on usage. ;)
 *
 */
if(!class_exists('section_4_col')) {

    /**
     * PageSection class.
     */
    class section_4_col
        extends AbstractPageSection
        implements PageSectionInterface
    {

        /**
         * @var Page $obj
         */

        /////////////////////////////////////////////////
        /**
         * Section HTML Template
         * This required HTML code will be used to render the final section content.
         * You can use tokens (see above) and you can run loops or any other PHP tricks you like.
         *
         * @returns string
         */
        public function section_html_template() {

            $node_html = '';
            $node_array = $this->get_node_array();

            foreach($node_array as $i => $node) {
                $node_headline = '';
                if ($node['headline']) {
                    $node_headline = '<h3>'.$node['headline'].'</h3>';
                }

                $node_image = '';
                if ($node['attachment_filename']) {
                    $node_image = '
                    <div class="w-100 mb-3">
                        <img src="/images/pages/'.$node['attachment_filename'].'" alt="'.$node['image_alt_text'].'" class="img-fluid w-100">
                    </div>
                    ';
                }

                $node_html .= '
                <div class="col-12 col-md-6 col-lg-3 mb-4 mb-lg-0">
                    <div class="h-100 d-flex flex-column justify-content-start align-items-center text-center">
                        '.$node_image.'
                        '.$node_headline.'
                        <div class="mb-auto">
                            {'.$i.':node_text}
                        </div>
                        {'.$i.':button_html}
                    </div>
                </div>
                ';
            }

            $background_image = '';
            if ($this->section_row['attachment_filename']) {
                $background_image = ' style="background-image: url(/images/pages/'.htmlspecialchars($this->section_row['attachment_filename'],ENT_COMPAT).');"';
            }

            $section_headline = '';
            if ($this->section_row['section_title']) {
                $section_headline = '
                <h2 class="mb-5 text-center">' . htmlspecialchars($this->section_row['section_title'],ENT_COMPAT) . '</h2>
                ';
            }

            $section_html = '

            <section 
                id="'.htmlspecialchars($this->section_row['css_id'],ENT_COMPAT).'" 
                class="section section-2-col 
                    '.htmlspecialchars($this->section_type_row['css_class'],ENT_COMPAT).' 
                    '.htmlspecialchars($this->section_row['css_class'],ENT_COMPAT).'
                    '.htmlspecialchars($this->section_row['option_background_color'],ENT_COMPAT).'
                " 
                ' . $background_image . '
                >
                <div class="container-xl section-content">
                    ' . $section_headline . '
                    <div class="row justify-content-center align-items-stretch">
                        ' . $node_html . '
                    </div>
                </div>
            </section>

            ';
            return $section_html;
        }

        /////////////////////////////////////////////////
        /**
         * Image HTML Template
         * This optional HTML code will be used to wrap any present images
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:image_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function image_html_template() {
            $image_html = '
            <img src="/images/pages/{image}" alt="{alt_text}" class="img-fluid">
            ';
            return $image_html;
        }

        /////////////////////////////////////////////////
        /**
         * Button HTML Template
         * This optional HTML code will be used to wrap any present buton text/URL
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:button_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function button_html_template() {
            $btn_color = '';
            if($this->section_row['option_background_color'] == 'text-bg-white') {
                $btn_color = 'btn-blue';
            }
            elseif ($this->section_row['option_background_color'] == 'text-bg-blue') {
                $btn_color = 'btn-white';
            }
            elseif ($this->section_row['option_background_color'] == 'text-bg-gray-900') {
                $btn_color = 'btn-outline-white';
            }
            // DEFAULT -- white bg, dark text, blue buttons
            else {
                $btn_color = 'btn-outline-blue';
            }
            $button_html = '
            <a href="{url}" {external} class="btn btn-lg ' . $btn_color . ' ml-sm-4 {css_class}">{text}</a>
            ';
            return $button_html;
        }

        /////////////////////////////////////////////////
        /**
         * Button Wrapper HTML Template
         *
         * @returns string
         */
        public function button_wrapper_html_template() {
            $button_html = '
            <p class="mt-auto d-flex flex-row flex-wrap gap-3 justify-content-center justify-content-md-start aign-items-center">{button_html}</p>
            ';
            return $button_html;
        }

    } // end class

} // end if
