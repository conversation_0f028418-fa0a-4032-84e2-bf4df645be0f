<?php
/**
 * File: section.news_newsletters.inc.php
 * 
 * @see "README" in this folder for instructions on usage. ;)
 *    
 */
if(!class_exists('news_newsletters')) {

    /**
     * PageSection class.
     */
    class news_newsletters
        extends AbstractPageSection 
        implements PageSectionInterface
        {

        /**
         * @var Page $obj
         */
        
        /////////////////////////////////////////////////
        /** 
         * Section HTML Template
         * This required HTML code will be used to render the final section content.
         * You can use tokens (see above) and you can run loops or any other PHP tricks you like.
         *
         * @returns string
         */
        public function section_html_template() {

            $node_html = '';
            $node_array = $this->get_node_array();

            foreach($node_array as $i => $node) {
                $node_html .= '
                
                <h2 class="h4 text-primary-800">{'.$i.':headline}</h2>
                <div class="col-12 col-md-9">
                {'.$i.':node_text}
                </div>
                {button_html}
                ';
            }

            $background_image = '';
            if ($this->section_row['attachment_filename']) {
                $background_image = ' style="background-image: url(/images/pages/'.htmlspecialchars($this->section_row['attachment_filename'],ENT_COMPAT).');"';
            }

            $section_html = '

            <section 
                id="'.htmlspecialchars($this->section_row['css_id'],ENT_COMPAT).'" 
                class="section section-2-col text-bg-blue 
                    '.htmlspecialchars($this->section_row['css_class'],ENT_COMPAT).'
                " 
                >
                <style>
                #news_newsletters .campaign {
                    margin-bottom: 1rem;
                }
                </style>
                <div class="container-xl">
                    <h2 class="text-center">Email Blasts</h2>
                    <div class="row justify-content-center align-items-stretch mt-5">
                        <div class="col-12 col-md-4 mb-4 mb-md-0">
                            <div class="h-100 d-flex flex-column justify-content-start align-items-start">
                                <h3 class="mb-4"><em>FIRST</em> LEGO League</h3>
                                <div class="fs-16">
                                    <script language="javascript" src="https://firstintexas.us12.list-manage.com/generate-js/?u=1ee0286e50bfdbb894f1420c8&show=10&fid=23149" type="text/javascript" ></script>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-4 mb-4 mb-md-0">
                            <div class="h-100 d-flex flex-column justify-content-start align-items-start">
                                <h3 class="mb-4"><em>FIRST</em> Tech Challenge</h3>
                                <div class="fs-16">
                                    <script language="javascript" src="https://firstintexas.us12.list-manage.com/generate-js/?u=1ee0286e50bfdbb894f1420c8&show=10&fid=23150" type="text/javascript" ></script>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-4 mb-4 mb-md-0">
                            <div class="h-100 d-flex flex-column justify-content-start align-items-start">
                                <h3 class="mb-4"><em>FIRST</em> Robotics Competition</h3>
                                <div class="fs-16">
                                    <script language="javascript" src="https://firstintexas.us12.list-manage.com/generate-js/?u=1ee0286e50bfdbb894f1420c8&show=10&fid=23151" type="text/javascript" ></script>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>                
            </section>

            ';
            return $section_html;
        }
            
        /////////////////////////////////////////////////
        /** 
         * Image HTML Template
         * This optional HTML code will be used to wrap any present images
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:image_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function image_html_template() { 
            $image_html = '
            <img src="/images/pages/{image}" alt="{alt_text}" class="img-fluid">
            ';
            return $image_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button HTML Template
         * This optional HTML code will be used to wrap any present buton text/URL
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:button_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function button_html_template() {
            $button_html = '
            <a href="{url}" {external} class="btn btn-blue {css_class}">{text}</a>
            ';
            return $button_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button Wrapper HTML Template
         *
         * @returns string
         */
        public function button_wrapper_html_template() {
            $button_html = '
            <p class="mt-auto d-flex flex-row flex-wrap gap-3 justify-content-center justify-content-md-start aign-items-center">{button_html}</p>
            ';
            return $button_html;
        }
        
    } // end class
    
} // end if
