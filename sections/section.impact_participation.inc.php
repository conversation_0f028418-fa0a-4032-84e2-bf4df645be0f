<?php
/**
 * File: section.impact_participation.inc.php
 * 
 * @see "README" in this folder for instructions on usage. ;)
 *    
 */
if(!class_exists('impact_participation')) {

    /**
     * PageSection class.
     */
    class impact_participation
        extends AbstractPageSection 
        implements PageSectionInterface
        {

        /**
         * @var Page $obj
         */
        
        /////////////////////////////////////////////////
        /** 
         * Section HTML Template
         * This required HTML code will be used to render the final section content.
         * You can use tokens (see above) and you can run loops or any other PHP tricks you like.
         *
         * @returns string
         */
        public function section_html_template() {

            $node_html = '';
            $node_array = $this->get_node_array();

            foreach($node_array as $i => $node) {
                $node_html .= '
                
                <h2 class="h4 text-primary-800">{'.$i.':headline}</h2>
                <div class="col-12 col-md-9">
                {'.$i.':node_text}
                </div>
                {button_html}
                ';
            }

            $background_image = '';
            if ($this->section_row['attachment_filename']) {
                $background_image = ' style="background-image: url(/images/pages/'.htmlspecialchars($this->section_row['attachment_filename'],ENT_COMPAT).');"';
            }

            $section_headline = '';
            if ($this->section_row['section_title']) {
                $section_headline = '
                <h2 class="participation-headline d-flex flex-row flex-wrap gap-4 justify-content-center align-items-center">
                    <div class="headline-image ratio ratio-1x1 bg-red rounded-circle overflow-hidden">
                        <div class="p-3">
                            <img src="/images/texas_white.png" class="img-fluid" alt="Texas White Shape">
                        </div>
                    </div>
                    <span class="headline-text text-red">
                        ' . $this->section_row['section_title'] . '
                    </span>
                </h2>
                ';
            }

            $section_html = '

            <section 
                id="'.htmlspecialchars($this->section_row['css_id'],ENT_COMPAT).'" 
                class="section pt-0
                    '.htmlspecialchars($this->section_type_row['css_class'],ENT_COMPAT).' 
                    '.htmlspecialchars($this->section_row['css_class'],ENT_COMPAT).'
                " 
                ' . $background_image . '
                >
                <div class="container-xl section-content">
                
                    <div class="divider section-spacing bg-cyan mt-0"></div>
                    ' . $section_headline . '
                    
                    <div class="participation-stats d-flex flex-row flex-wrap gap-5 justify-content-center align-items-stretch mt-5">
                        <div class="stats-item text-center">
                            <div class="title fs-24 fw-semibold text-uppercase">Students</div>
                            <div class="number fs-24">~31,325</div>
                        </div>
                        <div class="stats-item text-center">
                            <div class="title fs-24 fw-semibold text-uppercase">Teams</div>
                            <div class="number fs-24">3,349</div>
                        </div>
                        <div class="stats-item text-center">
                            <div class="title fs-24 fw-semibold text-uppercase">Supporters</div>
                            <div class="number fs-24">~16,000</div>
                        </div>
                        <div class="stats-item text-center">
                            <div class="title fs-24 fw-semibold text-uppercase">Volunteer Hours</div>
                            <div class="number fs-24">~500,000</div>
                        </div>
                    </div>
                    
                    <div class="row section-spacing mb-0">
                        <div class="participant-demographics col-12 col-lg-9">
                            <h3 class="mb-5 fs-24 fw-bold text-uppercase">Participant Demographics</h3>
                            <div class="row">
                                <div class="stats-chart-1 col-12 col-sm-6">
                                    <img src="/images/<EMAIL>" class="img-fluid chart-img d-block mx-auto mb-3"  alt="pie chart illustrating racial demographic statistics">
                                    <div class="stats-legend-1 d-flex flex-row flex-wrap justify-content-between fw-semibold">
                                        <div class="w-45 text-blue">
                                            <i class="fa-solid fa-square"></i> Hispanic 37%
                                        </div>
                                        <div class="w-45 text-red">
                                            <i class="fa-solid fa-square"></i> White 31%
                                        </div>
                                        <div class="w-45 text-green">
                                            <i class="fa-solid fa-square"></i> Asian 22%
                                        </div>
                                        <div class="w-45 text-orange">
                                            <i class="fa-solid fa-square"></i> Black 5%
                                        </div>
                                        <div class="w-45 text-gray-400">
                                            <i class="fa-solid fa-square"></i> Multiracial 4%
                                        </div>
                                        <div class="w-45 text-yellow">
                                            <i class="fa-solid fa-square"></i> Other 1%
                                        </div>
                                    </div>
                                </div>
                                <div class="stats-chart-2 col-12 col-sm-6 mt-5 mt-sm-0">
                                    <img src="/images/<EMAIL>" class="img-fluid chart-img d-block mx-auto mb-3"  alt="pie chart illustrating gender demographic statistics">
                                    <div class="stats-legend-2 d-flex flex-row flex-wrap justify-content-between fw-semibold">
                                        <div class="w-45 text-red text-start">
                                            Female 34%
                                        </div>
                                        <div class="w-45 text-blue text-end">
                                            Male 65%
                                        </div>
                                    </div>
                                    <div class="text-center mt-2">
                                        <span class="text-red fw-semibold">Women</span> are only <span class="text-red fw-semibold">26.5%</span> of the<br>
                                        <span class="text-blue fw-semibold">STEM Workforce in Texas</span> 
                                    </div>
                                    <div class="progress-container mt-3">
                                        <div class="progress rounded-pill" style="height: 60px;">
                                            <div class="progress-bar text-bg-primary" role="progressbar" style="width: 35%;" aria-valuenow="35" aria-valuemin="0" aria-valuemax="100">
                                                <div class="px-4 py-2">
                                                    <div class="progress-label-number">35%</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-center mt-1">Free/Reduce Lunch</div>
                                    </div>
                                    <div class="text-center">
                                        <span class="fs-32 fw-semibold text-red">7%</span> students with Disabilities, including in our <span class="text-blue fw-semibold text-nowrap"><em>FIRST</em> Access</span> program specifically for students with differing needs & abilities.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="school-participation col-12 col-lg-3 text-center mt-5 mt-lg-0">
                            <h3 class="mb-4 fs-24 fw-bold text-uppercase">School Participation</h3>
                            <p><span class="fs-32 fw-semibold text-red">Over 300</span><br>
                            School Districts & School Systems</p>
                            <p class="mt-3"><span class="fs-32 fw-semibold text-red">Over 1,150</span><br>
                            participating Schools & Youth Organizations</p>
                            <p class="mt-3"><span class="fs-32 fw-semibold text-red">49%</span><br>
                            of teams come from<br><span class="fw-semibold text-blue">Title 1 Schools</span></p>
                            <div class="d-flex flex-row gap-3 flex-nowrap justify-content-center align-items-center">
                                <div class="official-text flex-shrink-1 fs-14">
                                    Officially<br>
                                    Sanctioned<br>
                                    Program
                                </div>
                                <div class="official-img">
                                    <img src="/images/<EMAIL>" class="img-fluid" alt="UIL logo">
                                </div>
                            </div>
                        </div>
                    </div>
                
                </div>
            </section>

            ';
            return $section_html;
        }
            
        /////////////////////////////////////////////////
        /** 
         * Image HTML Template
         * This optional HTML code will be used to wrap any present images
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:image_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function image_html_template() { 
            $image_html = '
            <img src="/images/pages/{image}" alt="{alt_text}" class="img-fluid">
            ';
            return $image_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button HTML Template
         * This optional HTML code will be used to wrap any present buton text/URL
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:button_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function button_html_template() {
            $button_html = '
            <a href="{url}" {external} class="btn btn-blue {css_class}">{text}</a>
            ';
            return $button_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button Wrapper HTML Template
         *
         * @returns string
         */
        public function button_wrapper_html_template() {
            $button_html = '
            <p class="mt-auto d-flex flex-row flex-wrap gap-3 justify-content-center justify-content-md-start aign-items-center">{button_html}</p>
            ';
            return $button_html;
        }
        
    } // end class
    
} // end if

