<?php
/**
 * File: section_type.video_youtube_narrow.inc.php
 * 
 * @see "README" in this folder for instructions on usage. ;)
 *    
 */
if(!class_exists('video_youtube_narrow')) {

    /**
     * PageSection class.
     */
    class video_youtube_narrow
        extends AbstractPageSection 
        implements PageSectionInterface
        {

        /**
         * @var Page $obj
         */
        
        /////////////////////////////////////////////////
        /** 
         * Section HTML Template
         * This required HTML code will be used to render the final section content.
         * You can use tokens (see above) and you can run loops or any other PHP tricks you like.
         *
         * @returns string
         */
        public function section_html_template() {

            $section_html = '

            <section 
                id="'.htmlspecialchars($this->section_row['css_id'],ENT_COMPAT).'" 
                class="section video-narrow
                    '.htmlspecialchars($this->section_type_row['css_class'],ENT_COMPAT).' 
                    '.htmlspecialchars($this->section_row['css_class'],ENT_COMPAT).'
                    '.htmlspecialchars($this->section_row['option_background_color'],ENT_COMPAT).'
                " 
                >
                <div class="container-xl">
                    <div class="ratio ratio-16x9">
                        <iframe src="https://www.youtube.com/embed/' . $this->section_row['section_title'] . '" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                    </div>
                </div>
            </section>

            ';
            return $section_html;
        }
            
        /////////////////////////////////////////////////
        /** 
         * Image HTML Template
         * This optional HTML code will be used to wrap any present images
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:image_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function image_html_template() { 
            $image_html = '
        <img src="/images/pages/{image}" alt="{alt_text}" class="img-responsive">
            ';
            return $image_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button HTML Template
         * This optional HTML code will be used to wrap any present buton text/URL
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:button_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function button_html_template() {
            $btn_color = '';
            if ($this->section_row['option_background_color'] == 'text-bg-white') {
                $btn_color = 'btn-outline-blue';
            }
            elseif ($this->section_row['option_background_color'] == 'text-bg-blue') {
                $btn_color = 'btn-outline-white';
            }
            elseif ($this->section_row['option_background_color'] == 'text-bg-red') {
                $btn_color = 'btn-outline-white';
            }
            elseif ($this->section_row['option_background_color'] == 'text-bg-gray-900') {
                $btn_color = 'btn-outline-white';
            }
            // DEFAULT -- white bg, dark text, blue buttons
            else {
                $btn_color = 'btn-outline-blue';
            }
            $button_html = '
            <a href="{url}" {external} class="btn btn-lg ' . $btn_color . ' ml-sm-4 {css_class}">{text}</a>
            ';
            return $button_html;
        }

        /////////////////////////////////////////////////
        /**
         * Button Wrapper HTML Template
         *
         * @returns string
         */
        public function button_wrapper_html_template() {
            $btn_flex_justify = '';
            if($this->section_row['option_text_layout'] == 'text-left') {
                $btn_flex_justify = 'justify-content-start';
            }
            elseif ($this->section_row['option_text_layout'] == 'text-center') {
                $btn_flex_justify = 'justify-content-center';
            }
            elseif ($this->section_row['option_text_layout'] == 'text-right') {
                $btn_flex_justify = 'justify-content-end';
            }
            // DEFAULT -- center
            else {
                $btn_flex_justify = 'justify-content-center';
            }
            $button_html = '
            <p class="d-flex flex-row flex-wrap gap-3 ' . $btn_flex_justify . ' aign-items-center">{button_html}</p>
            ';
            return $button_html;
        }


    } // end class
    
} // end if
