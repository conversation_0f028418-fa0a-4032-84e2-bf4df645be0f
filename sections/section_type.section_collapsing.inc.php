<?php
/**
 * File: section_type.section_collapsing.inc.php
 * 
 * @see "README" in this folder for instructions on usage. ;)
 *    
 */
if(!class_exists('section_collapsing')) {

    /**
     * PageSection class.
     */
    class section_collapsing
        extends AbstractPageSection 
        implements PageSectionInterface
        {

        /**
         * @var Page $obj
         */
        
        /////////////////////////////////////////////////
        /** 
         * Section HTML Template
         * This required HTML code will be used to render the final section content.
         * You can use tokens (see above) and you can run loops or any other PHP tricks you like.
         *
         * @returns string
         */
        public function section_html_template() {

            $node_html = '';
            $node_array = $this->get_node_array();
            foreach($node_array as $i => $node) {
                $active_class = '';
                $aria_state = '';
                $button_state = '';
                if ($i === array_key_first($node_array)) {
                    $active_class = 'show';
                    $aria_state = 'true';
                }
                else {
                    $active_class = '';
                    $aria_state = 'false';
                    $button_state = 'collapsed';
                }
                $node_headline = '';
                if( $node['headline'] ) {
                    $node_headline = '
                    <h3 class="accordion-header">
                        <button 
                            class="accordion-button '. $button_state . '" 
                            type="button" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#item_' . $node['item_id'] . '" 
                            aria-controls="item_' . $node['item_id'] . '"
                            aria-expanded="' . $aria_state . '" 
                        >
                            '.$node['headline'].'
                        </button>
                    </h3>
                    ';
                }
                $node_html .= '
                <div class="accordion-item">
                    ' . $node_headline . '
                    <div 
                        id="item_' . $node['item_id'] . '" 
                        class="accordion-collapse collapse ' . $active_class . '" 
                        data-bs-parent="#section_' . $this->section_row['item_id'] . '"
                    >
                        <div class="accordion-body">
                            {'.$i.':node_text}
                            {'.$i.':button_html}
                        </div>
                    </div>
                </div>
                ';
            }

            $background_image = '';
            if ($this->section_row['attachment_filename']) {
                $background_image = ' style="background-image: url(/images/pages/'.htmlspecialchars($this->section_row['attachment_filename'],ENT_COMPAT).');"';
            }

            $section_html = '

            <section 
                id="'.htmlspecialchars($this->section_row['css_id'],ENT_COMPAT).'" 
                class="section section-collapsing '.htmlspecialchars($this->section_type_row['css_class'],ENT_COMPAT).' '.htmlspecialchars($this->section_row['css_class'],ENT_COMPAT).'" 
                ' . $background_image . '
                >
                <div class="container-xl section-content">
                    <div class="container-max-xl">
                        <div class="accordion" id="section_' . $this->section_row['item_id'] . '">
                            ' . $node_html . '
                        </div>
                    </div>
                </div>
            </section>

            ';
            return $section_html;
        }
            
        /////////////////////////////////////////////////
        /** 
         * Image HTML Template
         * This optional HTML code will be used to wrap any present images
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:image_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function image_html_template() { 
            $image_html = '
            <img src="/images/pages/{image}" alt="{alt_text}" class="img-fluid">
            ';
            return $image_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button HTML Template
         * This optional HTML code will be used to wrap any present buton text/URL
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:button_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function button_html_template() {
            $button_html = '
            <a href="{url}" {external} class="btn btn-blue {css_class}">{text}</a>
            ';
            return $button_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button Wrapper HTML Template
         *
         * @returns string
         */
        public function button_wrapper_html_template() {
            $button_html = '
            <p class="d-flex flex-row flex-wrap gap-3 justify-content-center justify-content-md-start aign-items-center">{button_html}</p>
            ';
            return $button_html;
        }
        
    } // end class
    
} // end if
