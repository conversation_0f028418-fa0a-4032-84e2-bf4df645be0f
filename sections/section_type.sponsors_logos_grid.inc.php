<?php
/**
 * File: section_type.sponsors_logos_grid.inc.php
 * 
 * @see "README" in this folder for instructions on usage. ;)
 *    
 */
if(!class_exists('sponsors_logos_grid')) {

    /**
     * PageSection class.
     */
    class sponsors_logos_grid
        extends AbstractPageSection 
        implements PageSectionInterface
        {

        /**
         * @var Page $obj
         */
        
        /////////////////////////////////////////////////
        /** 
         * Section HTML Template
         * This required HTML code will be used to render the final section content.
         * You can use tokens (see above) and you can run loops or any other PHP tricks you like.
         *
         * @returns string
         */
        public function section_html_template() {
            $section_html = '

            <section 
                id="'.htmlspecialchars($this->section_row['css_id'],ENT_COMPAT).'" 
                class="section section-sponsors-grid
                    '.htmlspecialchars($this->section_type_row['css_class'],ENT_COMPAT).' 
                    '.htmlspecialchars($this->section_row['css_class'],ENT_COMPAT).'
                " 
                >
                <div class="container-xl">
                    <div 
                        class="sponsors-grid d-flex flex-fow flex-wrap justify-content-center align-items-stretch 
                            '.htmlspecialchars($this->section_row['option_text_layout'],ENT_COMPAT).'
                        "
                    >
                        '.$this->obj->show_list('list_C_sponsor.').'
                    </div>
                </div>
            </section>

            ';
            return $section_html;
        }
            
        /////////////////////////////////////////////////
        /** 
         * Image HTML Template
         * This optional HTML code will be used to wrap any present images
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:image_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function image_html_template() { 
            $image_html = '
        <img src="/images/pages/{image}" alt="{alt_text}" class="img-responsive">
            ';
            return $image_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button HTML Template
         * This optional HTML code will be used to wrap any present buton text/URL
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:button_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function button_html_template() {
            $btn_color = '';
            if($this->section_row['option_background_color'] == 'text-bg-white') {
                $btn_color = 'btn-outline-blue';
            }
            elseif ($this->section_row['option_background_color'] == 'text-bg-blue') {
                $btn_color = 'btn-outline-white';
            }
            elseif ($this->section_row['option_background_color'] == 'text-bg-red') {
                $btn_color = 'btn-outline-white';
            }
            $button_html = '
            <a href="{url}" {external} class="btn btn-lg ' . $btn_color . ' ml-sm-4 {css_class}">{text}</a>
            ';
            return $button_html;
        }
        
    } // end class
    
} // end if
