<?php
/**
 * File: __README__.php
 * 
 * This file contains instructions for usage on the PageSection class
 * and the "Page Composer" plugin For Dialogs Framework v8.x.
 *
 * ---- USAGE WITHIN PAGE TEMPLATE ----
 *
 * Rendering the Section-based content for a given page is done by simply calling:
 *
 *    draw_page_sections($this) 
 * 
 * Which is defined in the file inc/cust_functions_pagecomposer.inc.php.
 *
 * ---- WITHIN EACH /sections/ FILE ----
 *
 * You should define a class with the same name as the file, in the following format:
 * 
 *    - File:    section.my_favorites.inc.php
 *    - OR File: section_type.my_favorites.inc.php (for Section Types)
 *    class my_favorites 
 *      extends AbstractPageSection 
 *      implements PageSectionInterface {}
 *
 * This class extends the AbstractPageSection class, which has these variables available:
 *
 *    - $this->obj                      = Dialogs $this object
 *    - $this->obj->this_page_row       = the current page rendering this section 
 *    - $this->section_type_row         = KD_page_section_type row for the given section's type
 *    - $this->section_row              = KD_page_section row for the given section
 *    - $this->node_array               = the array of nodes to be rendered for this page section
 *
 * This class should set at least 1 public method:
 *
 *    - section_html_template()         = returns the HTML template presented for this section (tokenized for nodes)
 *
 * ...and may optionally set any of these methods:
 *
 *    - image_html_template()           = returns the HTML template segment for images (tokenized for nodes; optional)
 *    - image_wrapper_html_template()   = returns the HTML template segment for images (tokenized for nodes; optional)
 *    - button_html_template()          = returns the HTML template segment for buttons (tokenized for nodes; optional)
 *    - button_wrapper_html_template()  = returns the HTML template segment for buttons (tokenized for nodes; optional)
 *
 * All these methods should do is define the HTML (markup) around content tokens.  
 * We do NOT recommend directly rendering node content into these functions, although it is totally possible.
 *    
 * Notes on tokenization:
 * 
 *    The HTML generated by these methods can contain HTML tokens that are filled by the section's nodes.
 *    The format is follows:               
 *
 *                               {0:node_text}
 *
 *    Where the "0" refers to the number of the node you want to pull from, and "node_text" 
 *    refers to the field name from that node.  In the method AbstractPageSection->render_html(),
 *    these tokens will be replaced with the actual node's content.  
 *
 */