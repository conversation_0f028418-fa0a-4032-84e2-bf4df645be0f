tinymce.PluginManager.add("link",function(a){function b(a){return a&&"A"===a.nodeName&&a.href}function c(a){return tinymce.util.Tools.grep(a,b).length>0}function d(b){return a.dom.getParent(b,"a[href]")}function e(){return d(a.selection.getStart())}function f(a){var b=a.getAttribute("data-mce-href");return b?b:a.getAttribute("href")}function g(){var b=a.plugins.contextmenu;return!!b&&b.isContextMenuVisible()}function h(c){var d,e,f;return!!(a.settings.link_context_toolbar&&!g()&&b(c)&&(d=a.selection,e=d.getRng(),f=e.startContainer,3==f.nodeType&&d.isCollapsed()&&e.startOffset>0&&e.startOffset<f.data.length))}function i(a){if(!tinymce.Env.ie||tinymce.Env.ie>10){var b=document.createElement("a");b.target="_blank",b.href=a,b.rel="noreferrer noopener";var c=document.createEvent("MouseEvents");c.initMouseEvent("click",!0,!0,window,!0,0,0,0,0,!1,!1,!1,!1,0,null),b.dispatchEvent(c)}else{var d=window.open("","_blank");if(d){d.opener=null;var e=d.document;e.open(),e.write('<meta http-equiv="refresh" content="0; url='+tinymce.DOM.encode(a)+'">'),e.close()}}}function j(b){if(b){var c=f(b);if(/^#/.test(c)){var d=a.$(c);d.length&&a.selection.scrollIntoView(d[0],!0)}else i(b.href)}}function k(){j(e())}function l(){var b=this,d=function(a){c(a.parents)?b.show():b.hide()};c(a.dom.getParents(a.selection.getStart()))||b.hide(),a.on("nodechange",d),b.on("remove",function(){a.off("nodechange",d)})}function m(b){return function(){var c=a.settings.link_list;"string"==typeof c?tinymce.util.XHR.send({url:c,success:function(a){b(tinymce.util.JSON.parse(a))}}):"function"==typeof c?c(b):b(c)}}function n(a,b,c){function d(a,c){return c=c||[],tinymce.each(a,function(a){var e={text:a.text||a.title};a.menu?e.menu=d(a.menu):(e.value=a.value,b&&b(e)),c.push(e)}),c}return d(a,c||[])}function o(b){function c(a){var b=l.find("#text");(!b.value()||a.lastControl&&b.value()==a.lastControl.text())&&b.value(a.control.text()),l.find("#href").value(a.control.value())}function d(b){var d=[];if(tinymce.each(a.dom.select("a:not([href])"),function(a){var c=a.name||a.id;c&&d.push({text:c,value:"#"+c,selected:b.indexOf("#"+c)!=-1})}),d.length)return d.unshift({text:"None",value:""}),{name:"anchor",type:"listbox",label:"Anchors",values:d,onselect:c}}function e(){!k&&0===w.text.length&&m&&this.parent().parent().find("#text")[0].value(this.value())}function f(b){var c=b.meta||{};q&&q.value(a.convertURL(this.value(),"href")),tinymce.each(b.meta,function(a,b){var c=l.find("#"+b);"text"===b?0===k.length&&(c.value(a),w.text=a):c.value(a)}),c.attach&&(p={href:this.value(),attach:c.attach}),c.text||e.call(this)}function g(a){var b=x.getContent();if(/</.test(b)&&(!/^<a [^>]+>[^<]+<\/a>$/.test(b)||b.indexOf("href=")==-1))return!1;if(a){var c,d=a.childNodes;if(0===d.length)return!1;for(c=d.length-1;c>=0;c--)if(3!=d[c].nodeType)return!1}return!0}function h(a){a.meta=l.toJSON()}var i,j,k,l,m,o,q,r,s,t,u,v,w={},x=a.selection,y=a.dom;i=x.getNode(),j=y.getParent(i,"a[href]"),m=g(),w.text=k=j?j.innerText||j.textContent:x.getContent({format:"text"}),w.href=j?y.getAttrib(j,"href"):"",j?w.target=y.getAttrib(j,"target"):a.settings.default_link_target&&(w.target=a.settings.default_link_target),(v=y.getAttrib(j,"rel"))&&(w.rel=v),(v=y.getAttrib(j,"class"))&&(w.class=v),(v=y.getAttrib(j,"title"))&&(w.title=v),m&&(o={name:"text",type:"textbox",size:40,label:"Text to display",onchange:function(){w.text=this.value()}}),b&&(q={type:"listbox",label:"Link list",values:n(b,function(b){b.value=a.convertURL(b.value||b.url,"href")},[{text:"None",value:""}]),onselect:c,value:a.convertURL(w.href,"href"),onPostRender:function(){q=this}}),a.settings.target_list!==!1&&(a.settings.target_list||(a.settings.target_list=[{text:"None",value:""},{text:"New window",value:"_blank"}]),s={name:"target",type:"listbox",label:"Target",values:n(a.settings.target_list)}),a.settings.rel_list&&(r={name:"rel",type:"listbox",label:"Rel",values:n(a.settings.rel_list)}),a.settings.link_class_list&&(t={name:"class",type:"listbox",label:"Class",values:n(a.settings.link_class_list,function(b){b.value&&(b.textStyle=function(){return a.formatter.getCssText({inline:"a",classes:[b.value]})})})}),a.settings.link_title!==!1&&(u={name:"title",type:"textbox",label:"Title",value:w.title}),l=a.windowManager.open({title:"Insert link",data:w,body:[{name:"href",type:"filepicker",filetype:"file",size:40,autofocus:!0,label:"Url",onchange:f,onkeyup:e,onbeforecall:h},o,u,d(w.href),q,r,s,t],onSubmit:function(b){function c(b,c){var d=a.selection.getRng();tinymce.util.Delay.setEditorTimeout(a,function(){a.windowManager.confirm(b,function(b){a.selection.setRng(d),c(b)})})}function d(a,b){function c(a){return a=d(a),a?[a,e].join(" "):e}function d(a){var b=new RegExp("("+e.replace(" ","|")+")","g");return a&&(a=tinymce.trim(a.replace(b,""))),a?a:null}var e="noopener noreferrer";return b?c(a):d(a)}function e(){var b={href:g,target:w.target?w.target:null,rel:w.rel?w.rel:null,class:w.class?w.class:null,title:w.title?w.title:null};a.settings.allow_unsafe_link_target||(b.rel=d(b.rel,"_blank"==b.target)),g===p.href&&(p.attach(),p={}),j?(a.focus(),m&&w.text!=k&&("innerText"in j?j.innerText=w.text:j.textContent=w.text),y.setAttribs(j,b),x.select(j),a.undoManager.add()):m?a.insertContent(y.createHTML("a",b,y.encode(w.text))):a.execCommand("mceInsertLink",!1,b)}function f(){a.undoManager.transact(e)}var g;return w=tinymce.extend(w,b.data),(g=w.href)?g.indexOf("@")>0&&g.indexOf("//")==-1&&g.indexOf("mailto:")==-1?void c("The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",function(a){a&&(g="mailto:"+g),f()}):a.settings.link_assume_external_targets&&!/^\w+:/i.test(g)||!a.settings.link_assume_external_targets&&/^\s*www[\.|\d\.]/i.test(g)?void c("The URL you entered seems to be an external link. Do you want to add the required http:// prefix?",function(a){a&&(g="http://"+g),f()}):void f():void a.execCommand("unlink")}})}var p={},q=function(a){return a.altKey===!0&&a.shiftKey===!1&&a.ctrlKey===!1&&a.metaKey===!1};a.addButton("link",{icon:"link",tooltip:"Insert/edit link",shortcut:"Meta+K",onclick:m(o),stateSelector:"a[href]"}),a.addButton("unlink",{icon:"unlink",tooltip:"Remove link",cmd:"unlink",stateSelector:"a[href]"}),a.addContextToolbar&&(a.addButton("openlink",{icon:"newtab",tooltip:"Open link",onclick:k}),a.addContextToolbar(h,"openlink | link unlink")),a.addShortcut("Meta+K","",m(o)),a.addCommand("mceLink",m(o)),a.on("click",function(a){var b=d(a.target);b&&tinymce.util.VK.metaKeyPressed(a)&&(a.preventDefault(),j(b))}),a.on("keydown",function(a){var b=e();b&&13===a.keyCode&&q(a)&&(a.preventDefault(),j(b))}),this.showDialog=o,a.addMenuItem("openlink",{text:"Open link",icon:"newtab",onclick:k,onPostRender:l,prependToContext:!0}),a.addMenuItem("link",{icon:"link",text:"Link",shortcut:"Meta+K",onclick:m(o),stateSelector:"a[href]",context:"insert",prependToContext:!0})});