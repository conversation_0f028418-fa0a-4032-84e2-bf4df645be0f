tinymce.PluginManager.add("toc",function(a){function b(b){return a.schema.isValidChild("div",b)}function c(b){return b&&a.dom.is(b,"."+l.className)&&a.getBody().contains(b)}function d(){var b=this;b.disabled(a.readonly||!f()),a.on("LoadContent SetContent change",function(){b.disabled(a.readonly||!f())})}function e(a){var b,c=[];for(b=1;b<=a;b++)c.push("h"+b);return c.join(",")}function f(){return!(!l||!g(l).length)}function g(b){var c=e(b.depth),d=m(c);return d.length&&/^h[1-9]$/i.test(b.headerTag)&&(d=d.filter(function(c,d){return!a.dom.hasClass(d.parentNode,b.className)})),tinymce.map(d,function(a){return a.id||(a.id=p()),{id:a.id,level:parseInt(a.nodeName.replace(/^H/i,""),10),title:m.text(a)}})}function h(a){var b,c=9;for(b=0;b<a.length;b++)if(a[b].level<c&&(c=a[b].level),1==c)return c;return c}function i(b,c){var d="<"+b+' contenteditable="true">',e="</"+b+">";return d+a.dom.encode(c)+e}function j(a){var b=k(a);return'<div class="'+a.className+'" contenteditable="false">'+b+"</div>"}function k(a){var b,c,d,e,f="",j=g(a),k=h(j)-1;if(!j.length)return"";for(f+=i(a.headerTag,tinymce.translate("Table of Contents")),b=0;b<j.length;b++){if(d=j[b],e=j[b+1]&&j[b+1].level,k===d.level)f+="<li>";else for(c=k;c<d.level;c++)f+="<ul><li>";if(f+='<a href="#'+d.id+'">'+d.title+"</a>",e!==d.level&&e)for(c=d.level;c>e;c--)f+="</li></ul><li>";else f+="</li>",e||(f+="</ul>");k=d.level}return f}var l,m=a.$,n={depth:3,headerTag:"h2",className:"mce-toc"},o=function(a){var b=0;return function(){var c=(new Date).getTime().toString(32);return a+c+(b++).toString(32)}},p=o("mcetoc_");a.on("PreInit",function(){var c=a.settings,d=parseInt(c.toc_depth,10)||0;l={depth:d>=1&&d<=9?d:n.depth,headerTag:b(c.toc_header)?c.toc_header:n.headerTag,className:c.toc_class?a.dom.encode(c.toc_class):n.className}}),a.on("PreProcess",function(a){var b=m("."+l.className,a.node);b.length&&(b.removeAttr("contentEditable"),b.find("[contenteditable]").removeAttr("contentEditable"))}),a.on("SetContent",function(){var a=m("."+l.className);a.length&&(a.attr("contentEditable",!1),a.children(":first-child").attr("contentEditable",!0))}),a.addCommand("mceInsertToc",function(){m("."+l.className).length?a.execCommand("mceUpdateToc"):a.insertContent(j(l))}),a.addCommand("mceUpdateToc",function(){var b=m("."+l.className);b.length&&a.undoManager.transact(function(){b.html(k(l))})}),a.addButton("toc",{tooltip:"Table of Contents",cmd:"mceInsertToc",icon:"toc",onPostRender:d}),a.addButton("tocupdate",{tooltip:"Update",cmd:"mceUpdateToc",icon:"reload"}),a.addContextToolbar(c,"tocupdate"),a.addMenuItem("toc",{text:"Table of Contents",context:"insert",cmd:"mceInsertToc",onPostRender:d})});