!function(a,b){"use strict";function c(a,b){for(var c,d=[],g=0;g<a.length;++g){if(c=f[a[g]]||e(a[g]),!c)throw"module definition dependecy not found: "+a[g];d.push(c)}b.apply(null,d)}function d(a,d,e){if("string"!=typeof a)throw"invalid module definition, module id must be defined and be a string";if(d===b)throw"invalid module definition, dependencies must be specified";if(e===b)throw"invalid module definition, definition function must be specified";c(d,function(){f[a]=e.apply(null,arguments)})}function e(b){for(var c=a,d=b.split(/[.\/]/),e=0;e<d.length;++e){if(!c[d[e]])return;c=c[d[e]]}return c}var f={};d("tinymce/tableplugin/Utils",["tinymce/Env"],function(a){function b(b){(!a.ie||a.ie>9)&&(b.hasChild<PERSON>()||(b.innerHTML='<br data-mce-bogus="1" />'))}var c=function(a){return function(b,c){b&&(c=parseInt(c,10),1===c||0===c?b.removeAttribute(a,1):b.setAttribute(a,c,1))}},d=function(a){return function(b){return parseInt(b.getAttribute(a)||1,10)}};return{setColSpan:c("colSpan"),setRowSpan:c("rowspan"),getColSpan:d("colSpan"),getRowSpan:d("rowSpan"),setSpanVal:function(a,b,d){c(b)(a,d)},getSpanVal:function(a,b){return d(b)(a)},paddCell:b}}),d("tinymce/tableplugin/SplitCols",["tinymce/util/Tools","tinymce/tableplugin/Utils"],function(a,b){var c=function(a,b,c){return a[c]?a[c][b]:null},d=function(a,b,d){var e=c(a,b,d);return e?e.elm:null},e=function(a,b,e,f){var g,h,i=0,j=d(a,b,e);for(g=e;(f>0?g<a.length:g>=0)&&(h=c(a,b,g),j===h.elm);g+=f)i++;return i},f=function(a,b,c){for(var d,e=a[c],f=b;f<e.length;f++)if(d=e[f],d.real)return d.elm;return null},g=function(a,c){for(var d,f=[],g=a[c],h=0;h<g.length;h++)d=g[h],f.push({elm:d.elm,above:e(a,h,c,-1)-1,below:e(a,h,c,1)-1}),h+=b.getColSpan(d.elm)-1;return f},h=function(a,c){var d=a.elm.ownerDocument,e=d.createElement("td");return b.setColSpan(e,b.getColSpan(a.elm)),b.setRowSpan(e,c),b.paddCell(e),e},i=function(a,b,c,d){var e=f(a,c+1,d);e?e.parentNode.insertBefore(b,e):(e=f(a,0,d),e.parentNode.appendChild(b))},j=function(a,c,d,e){if(0!==c.above){b.setRowSpan(c.elm,c.above);var f=h(c,c.below+1);return i(a,f,d,e),f}return null},k=function(a,c,d,e){if(0!==c.below){b.setRowSpan(c.elm,c.above+1);var f=h(c,c.below);return i(a,f,d,e+1),f}return null},l=function(b,c,e,f){var h=g(b,e),i=d(b,c,e).parentNode,l=[];return a.each(h,function(a,c){var d=f?j(b,a,c,e):k(b,a,c,e);null!==d&&l.push(l)}),{cells:l,row:i}};return{splitAt:l}}),d("tinymce/tableplugin/TableGrid",["tinymce/util/Tools","tinymce/Env","tinymce/tableplugin/Utils","tinymce/tableplugin/SplitCols"],function(a,c,d,e){var f=a.each,g=d.getSpanVal,h=d.setSpanVal;return function(i,j,k){function l(){i.$("td[data-mce-selected],th[data-mce-selected]").removeAttr("data-mce-selected")}function m(a){return a===i.getBody()}function n(b,c){return b?(c=a.map(c.split(","),function(a){return a.toLowerCase()}),a.grep(b.childNodes,function(b){return a.inArray(c,b.nodeName.toLowerCase())!==-1})):[]}function o(){var a=0;X=[],Y=0,f(["thead","tbody","tfoot"],function(b){var c=n(j,b)[0],d=n(c,"tr");f(d,function(c,d){d+=a,f(n(c,"td,th"),function(a,c){var e,f,h,i;if(X[d])for(;X[d][c];)c++;for(h=g(a,"rowspan"),i=g(a,"colspan"),f=d;f<d+h;f++)for(X[f]||(X[f]=[]),e=c;e<c+i;e++)X[f][e]={part:b,real:f==d&&e==c,elm:a,rowspan:h,colspan:i};Y=Math.max(Y,c+1)})}),a+=d.length})}function p(a){return i.fire("newrow",{node:a}),a}function q(a){return i.fire("newcell",{node:a}),a}function r(a,b){return a=a.cloneNode(b),a.removeAttribute("id"),a}function s(a,b){var c;if(c=X[b])return c[a]}function t(a,b){return a[b]?a[b]:null}function u(a,b){for(var c=[],d=0;d<a.length;d++)c.push(s(b,d));return c}function v(a){return a&&(!!aa.getAttrib(a.elm,"data-mce-selected")||a==k)}function w(){var a=[];return f(j.rows,function(b){f(b.cells,function(c){if(aa.getAttrib(c,"data-mce-selected")||k&&c==k.elm)return a.push(b),!1})}),a}function x(){var a=aa.createRng();m(j)||(a.setStartAfter(j),a.setEndAfter(j),_.setRng(a),aa.remove(j))}function y(b){var e,g={};return i.settings.table_clone_elements!==!1&&(g=a.makeMap((i.settings.table_clone_elements||"strong em b i span font h1 h2 h3 h4 h5 h6 p div").toUpperCase(),/[ ,]/)),a.walk(b,function(a){var d;if(3==a.nodeType)return f(aa.getParents(a.parentNode,null,b).reverse(),function(a){g[a.nodeName]&&(a=r(a,!1),e?d&&d.appendChild(a):e=d=a,d=a)}),d&&(d.innerHTML=c.ie&&c.ie<10?"&nbsp;":'<br data-mce-bogus="1" />'),!1},"childNodes"),b=r(b,!1),q(b),h(b,"rowSpan",1),h(b,"colSpan",1),e?b.appendChild(e):d.paddCell(b),b}function z(){var a,b=aa.createRng();return f(aa.select("tr",j),function(a){0===a.cells.length&&aa.remove(a)}),0===aa.select("tr",j).length?(b.setStartBefore(j),b.setEndBefore(j),_.setRng(b),void aa.remove(j)):(f(aa.select("thead,tbody,tfoot",j),function(a){0===a.rows.length&&aa.remove(a)}),o(),void(Z&&(a=X[Math.min(X.length-1,Z.y)],a&&(_.select(a[Math.min(a.length-1,Z.x)].elm,!0),_.collapse(!0)))))}function A(a,b,c,d){var e,f,g,h,i;for(e=X[b][a].elm.parentNode,g=1;g<=c;g++)if(e=aa.getNext(e,"tr")){for(f=a;f>=0;f--)if(i=X[b+g][f].elm,i.parentNode==e){for(h=1;h<=d;h++)aa.insertAfter(y(i),i);break}if(f==-1)for(h=1;h<=d;h++)e.insertBefore(y(e.cells[0]),e.cells[0])}}function B(){f(X,function(a,b){f(a,function(a,c){var d,e,f;if(v(a)&&(a=a.elm,d=g(a,"colspan"),e=g(a,"rowspan"),d>1||e>1)){for(h(a,"rowSpan",1),h(a,"colSpan",1),f=0;f<d-1;f++)aa.insertAfter(y(a),a);A(c,b,e-1,d)}})})}function C(a,b,c){for(var d=[],e=0;e<a.length;e++)(e<b||e>c)&&d.push(a[e]);return d}function D(b){return a.grep(b,function(a){return a.real===!1})}function E(a){for(var b=[],c=0;c<a.length;c++){var d=a[c].elm;b[b.length-1]!==d&&b.push(d)}return b}function F(b,c,e,f,g){var h=0;if(g-e<1)return 0;for(var i=e+1;i<=g;i++){var j=C(t(b,i),c,f),k=D(j);j.length===k.length&&(a.each(E(k),function(a){d.setRowSpan(a,d.getRowSpan(a)-1)}),h++)}return h}function G(b,c,e,f,g){var h=0;if(f-c<1)return 0;for(var i=c+1;i<=f;i++){var j=C(u(b,i),e,g),k=D(j);j.length===k.length&&(a.each(E(k),function(a){d.setColSpan(a,d.getColSpan(a)-1)}),h++)}return h}function H(b,c,d){var e,g,i,j,k,l,m,n,p,q,r,t,u;if(b?(e=R(b),g=e.x,i=e.y,j=g+(c-1),k=i+(d-1)):(Z=$=null,f(X,function(a,b){f(a,function(a,c){v(a)&&(Z||(Z={x:c,y:b}),$={x:c,y:b})})}),Z&&(g=Z.x,i=Z.y,j=$.x,k=$.y)),n=s(g,i),p=s(j,k),n&&p&&n.part==p.part){B(),o(),t=F(X,g,i,j,k),u=G(X,g,i,j,k),n=s(g,i).elm;var w=j-g-u+1,x=k-i-t+1;for(w===Y&&x===X.length&&(w=1,x=1),w===Y&&x>1&&(x=1),h(n,"colSpan",w),h(n,"rowSpan",x),m=i;m<=k;m++)for(l=g;l<=j;l++)X[m]&&X[m][l]&&(b=X[m][l].elm,b!=n&&(q=a.grep(b.childNodes),f(q,function(a){n.appendChild(a)}),q.length&&(q=a.grep(n.childNodes),r=0,f(q,function(a){"BR"==a.nodeName&&r++<q.length-1&&n.removeChild(a)})),aa.remove(b)));z()}}function I(a){var c,d,e,i,j,k,l,m,n,o;if(f(X,function(b,d){if(f(b,function(b){if(v(b)&&(b=b.elm,j=b.parentNode,k=p(r(j,!1)),c=d,a))return!1}),a)return!c}),c!==b){for(i=0,o=0;i<X[0].length;i+=o)if(X[c][i]&&(d=X[c][i].elm,o=g(d,"colspan"),d!=e)){if(a){if(c>0&&X[c-1][i]&&(m=X[c-1][i].elm,n=g(m,"rowSpan"),n>1)){h(m,"rowSpan",n+1);continue}}else if(n=g(d,"rowspan"),n>1){h(d,"rowSpan",n+1);continue}l=y(d),h(l,"colSpan",d.colSpan),k.appendChild(l),e=d}k.hasChildNodes()&&(a?j.parentNode.insertBefore(k,j):aa.insertAfter(k,j))}}function J(a){var b,c;f(X,function(c){if(f(c,function(c,d){if(v(c)&&(b=d,a))return!1}),a)return!b}),f(X,function(d,e){var f,i,j;d[b]&&(f=d[b].elm,f!=c&&(j=g(f,"colspan"),i=g(f,"rowspan"),1==j?a?(f.parentNode.insertBefore(y(f),f),A(b,e,i-1,j)):(aa.insertAfter(y(f),f),A(b,e,i-1,j)):h(f,"colSpan",f.colSpan+1),c=f))})}function K(b){return a.grep(L(b),v)}function L(a){var b=[];return f(a,function(a){f(a,function(a){b.push(a)})}),b}function M(){var b=[];if(m(j)){if(1==X[0].length)return;if(K(X).length==L(X).length)return}f(X,function(c){f(c,function(c,d){v(c)&&a.inArray(b,d)===-1&&(f(X,function(a){var b,c=a[d].elm;b=g(c,"colSpan"),b>1?h(c,"colSpan",b-1):aa.remove(c)}),b.push(d))})}),z()}function N(){function a(a){var b,c;f(a.cells,function(a){var c=g(a,"rowSpan");c>1&&(h(a,"rowSpan",c-1),b=R(a),A(b.x,b.y,1,1))}),b=R(a.cells[0]),f(X[b.y],function(a){var b;a=a.elm,a!=c&&(b=g(a,"rowSpan"),b<=1?aa.remove(a):h(a,"rowSpan",b-1),c=a)})}var b;b=w(),m(j)&&b.length==j.rows.length||(f(b.reverse(),function(b){a(b)}),z())}function O(){var a=w();if(!m(j)||a.length!=j.rows.length)return aa.remove(a),z(),a}function P(){var a=w();return f(a,function(b,c){a[c]=r(b,!0)}),a}function Q(b,c){var d,g,i;b&&(d=e.splitAt(X,Z.x,Z.y,c),g=d.row,a.each(d.cells,q),i=a.map(b,function(a){return a.cloneNode(!0)}),c||i.reverse(),f(i,function(a){var b,d,e=a.cells.length;for(p(a),b=0;b<e;b++)d=a.cells[b],q(d),h(d,"colSpan",1),h(d,"rowSpan",1);for(b=e;b<Y;b++)a.appendChild(q(y(a.cells[e-1])));for(b=Y;b<e;b++)aa.remove(a.cells[b]);c?g.parentNode.insertBefore(a,g):aa.insertAfter(a,g)}),l())}function R(a){var b;return f(X,function(c,d){return f(c,function(c,e){if(c.elm==a)return b={x:e,y:d},!1}),!b}),b}function S(a){Z=R(a)}function T(){var a,b;return a=b=0,f(X,function(c,d){f(c,function(c,e){var f,g;v(c)&&(c=X[d][e],e>a&&(a=e),d>b&&(b=d),c.real&&(f=c.colspan-1,g=c.rowspan-1,f&&e+f>a&&(a=e+f),g&&d+g>b&&(b=d+g)))})}),{x:a,y:b}}function U(a){var b,c,d,e,f,g,h,i,j,k;if($=R(a),Z&&$){for(b=Math.min(Z.x,$.x),c=Math.min(Z.y,$.y),d=Math.max(Z.x,$.x),e=Math.max(Z.y,$.y),f=d,g=e,k=c;k<=e;k++)for(j=b;j<=d;j++)a=X[k][j],a.real&&(h=a.colspan-1,i=a.rowspan-1,h&&j+h>f&&(f=j+h),i&&k+i>g&&(g=k+i));for(l(),k=c;k<=g;k++)for(j=b;j<=f;j++)X[k][j]&&aa.setAttrib(X[k][j].elm,"data-mce-selected","1")}}function V(a,b){var c,d,e;c=R(a),d=c.y*Y+c.x;do{if(d+=b,e=s(d%Y,Math.floor(d/Y)),!e)break;if(e.elm!=a)return _.select(e.elm,!0),aa.isEmpty(e.elm)&&_.collapse(!0),!0}while(e.elm==a);return!1}function W(b){if(Z){var c=e.splitAt(X,Z.x,Z.y,b);a.each(c.cells,q)}}var X,Y,Z,$,_=i.selection,aa=_.dom;j=j||aa.getParent(_.getStart(!0),"table"),o(),k=k||aa.getParent(_.getStart(!0),"th,td"),k&&(Z=R(k),$=T(),k=s(Z.x,Z.y)),a.extend(this,{deleteTable:x,split:B,merge:H,insertRow:I,insertCol:J,splitCols:W,deleteCols:M,deleteRows:N,cutRows:O,copyRows:P,pasteRows:Q,getPos:R,setStartCell:S,setEndCell:U,moveRelIdx:V,refresh:o})}}),d("tinymce/tableplugin/Quirks",["tinymce/util/VK","tinymce/util/Delay","tinymce/Env","tinymce/util/Tools","tinymce/tableplugin/Utils"],function(a,b,c,d,e){var f=d.each,g=e.getSpanVal;return function(h){function i(){function c(c){function d(a,b){var d=a?"previousSibling":"nextSibling",f=h.dom.getParent(b,"tr"),g=f[d];if(g)return r(h,b,g,a),c.preventDefault(),!0;var i=h.dom.getParent(f,"table"),l=f.parentNode,m=l.nodeName.toLowerCase();if("tbody"===m||m===(a?"tfoot":"thead")){var n=e(a,i,l,"tbody");if(null!==n)return j(a,n,b)}return k(a,f,d,i)}function e(a,b,c,d){var e=h.dom.select(">"+d,b),f=e.indexOf(c);if(a&&0===f||!a&&f===e.length-1)return i(a,b);if(f===-1){var g="thead"===c.tagName.toLowerCase()?0:e.length-1;return e[g]}return e[f+(a?-1:1)]}function i(a,b){var c=a?"thead":"tfoot",d=h.dom.select(">"+c,b);return 0!==d.length?d[0]:null}function j(a,b,d){var e=l(b,a);return e&&r(h,d,e,a),c.preventDefault(),!0}function k(a,b,e,f){var g=f[e];if(g)return m(g),!0;var i=h.dom.getParent(f,"td,th");if(i)return d(a,i,c);var j=l(b,!a);return m(j),c.preventDefault(),!1}function l(a,b){var c=a&&a[b?"lastChild":"firstChild"];return c&&"BR"===c.nodeName?h.dom.getParent(c,"td,th"):c}function m(a){h.selection.setCursorLocation(a,0)}function n(){return u==a.UP||u==a.DOWN}function o(a){var b=a.selection.getNode(),c=a.dom.getParent(b,"tr");return null!==c}function p(a){for(var b=0,c=a;c.previousSibling;)c=c.previousSibling,b+=g(c,"colspan");return b}function q(a,b){var c=0,d=0;return f(a.children,function(a,e){if(c+=g(a,"colspan"),d=e,c>b)return!1}),d}function r(a,b,c,d){var e=p(h.dom.getParent(b,"td,th")),f=q(c,e),g=c.childNodes[f],i=l(g,d);m(i||g)}function s(a){var b=h.selection.getNode(),c=h.dom.getParent(b,"td,th"),d=h.dom.getParent(a,"td,th");return c&&c!==d&&t(c,d)}function t(a,b){return h.dom.getParent(a,"TABLE")===h.dom.getParent(b,"TABLE")}var u=c.keyCode;if(n()&&o(h)){var v=h.selection.getNode();b.setEditorTimeout(h,function(){s(v)&&d(!c.shiftKey&&u===a.UP,v,c)},0)}}h.on("KeyDown",function(a){c(a)})}function j(){function a(a,b){var c,d=b.ownerDocument,e=d.createRange();return e.setStartBefore(b),e.setEnd(a.endContainer,a.endOffset),c=d.createElement("body"),c.appendChild(e.cloneContents()),0===c.innerHTML.replace(/<(br|img|object|embed|input|textarea)[^>]*>/gi,"-").replace(/<[^>]+>/g,"").length}h.on("KeyDown",function(b){var c,d,e=h.dom;37!=b.keyCode&&38!=b.keyCode||(c=h.selection.getRng(),d=e.getParent(c.startContainer,"table"),d&&h.getBody().firstChild==d&&a(c,d)&&(c=e.createRng(),c.setStartBefore(d),c.setEndBefore(d),h.selection.setRng(c),b.preventDefault()))})}function k(){h.on("KeyDown SetContent VisualAid",function(){var a;for(a=h.getBody().lastChild;a;a=a.previousSibling)if(3==a.nodeType){if(a.nodeValue.length>0)break}else if(1==a.nodeType&&("BR"==a.tagName||!a.getAttribute("data-mce-bogus")))break;a&&"TABLE"==a.nodeName&&(h.settings.forced_root_block?h.dom.add(h.getBody(),h.settings.forced_root_block,h.settings.forced_root_block_attrs,c.ie&&c.ie<10?"&nbsp;":'<br data-mce-bogus="1" />'):h.dom.add(h.getBody(),"br",{"data-mce-bogus":"1"}))}),h.on("PreProcess",function(a){var b=a.node.lastChild;b&&("BR"==b.nodeName||1==b.childNodes.length&&("BR"==b.firstChild.nodeName||"\xa0"==b.firstChild.nodeValue))&&b.previousSibling&&"TABLE"==b.previousSibling.nodeName&&h.dom.remove(b)})}function l(){function a(a,b,c,d){var e,f,g,h=3,i=a.dom.getParent(b.startContainer,"TABLE");return i&&(e=i.parentNode),f=b.startContainer.nodeType==h&&0===b.startOffset&&0===b.endOffset&&d&&("TR"==c.nodeName||c==e),g=("TD"==c.nodeName||"TH"==c.nodeName)&&!d,f||g}function b(){var b=h.selection.getRng(),c=h.selection.getNode(),d=h.dom.getParent(b.startContainer,"TD,TH");if(a(h,b,c,d)){d||(d=c);for(var e=d.lastChild;e.lastChild;)e=e.lastChild;3==e.nodeType&&(b.setEnd(e,e.data.length),h.selection.setRng(b))}}h.on("KeyDown",function(){b()}),h.on("MouseDown",function(a){2!=a.button&&b()})}function m(){function b(a){h.selection.select(a,!0),h.selection.collapse(!0)}function c(a){h.$(a).empty(),e.paddCell(a)}h.on("keydown",function(e){if((e.keyCode==a.DELETE||e.keyCode==a.BACKSPACE)&&!e.isDefaultPrevented()){var f,g,i,j;if(f=h.dom.getParent(h.selection.getStart(),"table")){if(g=h.dom.select("td,th",f),i=d.grep(g,function(a){return!!h.dom.getAttrib(a,"data-mce-selected")}),0===i.length)return j=h.dom.getParent(h.selection.getStart(),"td,th"),void(h.selection.isCollapsed()&&j&&h.dom.isEmpty(j)&&(e.preventDefault(),c(j),b(j)));e.preventDefault(),h.undoManager.transact(function(){g.length==i.length?h.execCommand("mceTableDelete"):(d.each(i,c),b(i[0]))})}}})}m(),c.webkit&&(i(),l()),c.gecko&&(j(),k()),c.ie>9&&(j(),k())}}),d("tinymce/tableplugin/CellSelection",["tinymce/tableplugin/TableGrid","tinymce/dom/TreeWalker","tinymce/util/Tools"],function(a,b,c){return function(d,e){function f(a){d.getBody().style.webkitUserSelect="",(a||p)&&(d.$("td[data-mce-selected],th[data-mce-selected]").removeAttr("data-mce-selected"),p=!1)}function g(a,b){return!(!a||!b)&&a===o.getParent(b,"table")}function h(b){var c,f,h=b.target;if(!m&&!n&&h!==l&&(l=h,k&&j)){if(f=o.getParent(h,"td,th"),g(k,f)||(f=o.getParent(k,"td,th")),j===f&&!p)return;if(e(!0),g(k,f)){b.preventDefault(),i||(i=new a(d,k,j),d.getBody().style.webkitUserSelect="none"),i.setEndCell(f),p=!0,c=d.selection.getSel();try{c.removeAllRanges?c.removeAllRanges():c.empty()}catch(a){}}}}var i,j,k,l,m,n,o=d.dom,p=!0,q=function(){j=i=k=l=null,e(!1)};return d.on("SelectionChange",function(a){p&&a.stopImmediatePropagation()},!0),d.on("MouseDown",function(a){2==a.button||m||n||(f(),j=o.getParent(a.target,"td,th"),k=o.getParent(j,"table"))}),d.on("mouseover",h),d.on("remove",function(){o.unbind(d.getDoc(),"mouseover",h),f()}),d.on("MouseUp",function(){function a(a,d){var f=new b(a,a);do{if(3==a.nodeType&&0!==c.trim(a.nodeValue).length)return void(d?e.setStart(a,0):e.setEnd(a,a.nodeValue.length));if("BR"==a.nodeName)return void(d?e.setStartBefore(a):e.setEndBefore(a))}while(a=d?f.next():f.prev())}var e,f,g,h,k,l=d.selection;if(j){if(i&&(d.getBody().style.webkitUserSelect=""),f=o.select("td[data-mce-selected],th[data-mce-selected]"),f.length>0){e=o.createRng(),h=f[0],e.setStartBefore(h),e.setEndAfter(h),a(h,1),g=new b(h,o.getParent(f[0],"table"));do if("TD"==h.nodeName||"TH"==h.nodeName){if(!o.getAttrib(h,"data-mce-selected"))break;k=h}while(h=g.next());a(k),l.setRng(e)}d.nodeChanged(),q()}}),d.on("KeyUp Drop SetContent",function(a){f("setcontent"==a.type),q(),m=!1}),d.on("ObjectResizeStart ObjectResized",function(a){m="objectresized"!=a.type}),d.on("dragstart",function(){n=!0}),d.on("drop dragend",function(){n=!1}),{clear:f}}}),d("tinymce/tableplugin/Dialogs",["tinymce/util/Tools","tinymce/Env"],function(a,b){var c=a.each;return function(d){function e(){var a=d.settings.color_picker_callback;if(a)return function(){var b=this;a.call(d,function(a){b.value(a).fire("change")},b.value())}}function f(a){return{title:"Advanced",type:"form",defaults:{onchange:function(){l(a,this.parents().reverse()[0],"style"==this.name())}},items:[{label:"Style",name:"style",type:"textbox"},{type:"form",padding:0,formItemDefaults:{layout:"grid",alignH:["start","right"]},defaults:{size:7},items:[{label:"Border color",type:"colorbox",name:"borderColor",onaction:e()},{label:"Background color",type:"colorbox",name:"backgroundColor",onaction:e()}]}]}}function g(a){return a?a.replace(/px$/,""):""}function h(a){return/^[0-9]+$/.test(a)&&(a+="px"),a}function i(a){c("left center right".split(" "),function(b){d.formatter.remove("align"+b,{},a)})}function j(a){c("top middle bottom".split(" "),function(b){d.formatter.remove("valign"+b,{},a)})}function k(b,c,d){function e(b,d){return d=d||[],a.each(b,function(a){var b={text:a.text||a.title};a.menu?b.menu=e(a.menu):(b.value=a.value,c&&c(b)),d.push(b)}),d}return e(b,d||[])}function l(a,b,c){var d=b.toJSON(),e=a.parseStyle(d.style);c?(b.find("#borderColor").value(e["border-color"]||"")[0].fire("change"),b.find("#backgroundColor").value(e["background-color"]||"")[0].fire("change")):(e["border-color"]=d.borderColor,e["background-color"]=d.backgroundColor),b.find("#style").value(a.serializeStyle(a.parseStyle(a.serializeStyle(e))))}function m(a,b,c){var d=a.parseStyle(a.getAttrib(c,"style"));d["border-color"]&&(b.borderColor=d["border-color"]),d["background-color"]&&(b.backgroundColor=d["background-color"]),b.style=a.serializeStyle(d)}function n(a,b,d){var e=a.parseStyle(a.getAttrib(b,"style"));c(d,function(a){e[a.name]=a.value}),a.setAttrib(b,"style",a.serializeStyle(a.parseStyle(a.serializeStyle(e))))}var o=this;o.tableProps=function(){o.table(!0)},o.table=function(e){function j(){function c(a,b,d){if("TD"===a.tagName||"TH"===a.tagName)v.setStyle(a,b,d);else if(a.children)for(var e=0;e<a.children.length;e++)c(a.children[e],b,d)}var e;l(v,this),w=a.extend(w,this.toJSON()),w.class===!1&&delete w.class,d.undoManager.transact(function(){if(p||(p=d.plugins.table.insertTable(w.cols||1,w.rows||1)),d.dom.setAttribs(p,{style:w.style,class:w.class}),d.settings.table_style_by_css){if(u=[],u.push({name:"border",value:w.border}),u.push({name:"border-spacing",value:h(w.cellspacing)}),n(v,p,u),v.setAttribs(p,{"data-mce-border-color":w.borderColor,"data-mce-cell-padding":w.cellpadding,"data-mce-border":w.border}),p.children)for(var a=0;a<p.children.length;a++)c(p.children[a],"border",w.border),c(p.children[a],"padding",h(w.cellpadding))}else d.dom.setAttribs(p,{border:w.border,cellpadding:w.cellpadding,cellspacing:w.cellspacing});v.getAttrib(p,"width")&&!d.settings.table_style_by_css?v.setAttrib(p,"width",g(w.width)):v.setStyle(p,"width",h(w.width)),v.setStyle(p,"height",h(w.height)),e=v.select("caption",p)[0],e&&!w.caption&&v.remove(e),!e&&w.caption&&(e=v.create("caption"),e.innerHTML=b.ie?"\xa0":'<br data-mce-bogus="1"/>',p.insertBefore(e,p.firstChild)),i(p),w.align&&d.formatter.apply("align"+w.align,{},p),d.focus(),d.addVisual()})}function o(a,b){function c(a,c){for(var d=0;d<c.length;d++){var e=v.getStyle(c[d],b);if("undefined"==typeof a&&(a=e),a!=e)return""}return a}var e,f=d.dom.select("td,th",a);return e=c(e,f)}var p,q,r,s,t,u,v=d.dom,w={};e===!0?(p=v.getParent(d.selection.getStart(),"table"),p&&(w={width:g(v.getStyle(p,"width")||v.getAttrib(p,"width")),height:g(v.getStyle(p,"height")||v.getAttrib(p,"height")),cellspacing:g(v.getStyle(p,"border-spacing")||v.getAttrib(p,"cellspacing")),cellpadding:v.getAttrib(p,"data-mce-cell-padding")||v.getAttrib(p,"cellpadding")||o(p,"padding"),border:v.getAttrib(p,"data-mce-border")||v.getAttrib(p,"border")||o(p,"border"),borderColor:v.getAttrib(p,"data-mce-border-color"),caption:!!v.select("caption",p)[0],class:v.getAttrib(p,"class")},c("left center right".split(" "),function(a){d.formatter.matchNode(p,"align"+a)&&(w.align=a)}))):(q={label:"Cols",name:"cols"},r={label:"Rows",name:"rows"}),d.settings.table_class_list&&(w.class&&(w.class=w.class.replace(/\s*mce\-item\-table\s*/g,"")),s={name:"class",type:"listbox",label:"Class",values:k(d.settings.table_class_list,function(a){a.value&&(a.textStyle=function(){return d.formatter.getCssText({block:"table",classes:[a.value]})})})}),t={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",labelGapCalc:!1,padding:0,layout:"grid",columns:2,defaults:{type:"textbox",maxWidth:50},items:d.settings.table_appearance_options!==!1?[q,r,{label:"Width",name:"width"},{label:"Height",name:"height"},{label:"Cell spacing",name:"cellspacing"},{label:"Cell padding",name:"cellpadding"},{label:"Border",name:"border"},{label:"Caption",name:"caption",type:"checkbox"}]:[q,r,{label:"Width",name:"width"},{label:"Height",name:"height"}]},{label:"Alignment",name:"align",type:"listbox",text:"None",values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},s]},d.settings.table_advtab!==!1?(m(v,w,p),d.windowManager.open({title:"Table properties",data:w,bodyType:"tabpanel",body:[{title:"General",type:"form",items:t},f(v)],onsubmit:j})):d.windowManager.open({title:"Table properties",data:w,body:t,onsubmit:j})},o.merge=function(a,b){d.windowManager.open({title:"Merge cells",body:[{label:"Cols",name:"cols",type:"textbox",value:"1",size:10},{label:"Rows",name:"rows",type:"textbox",value:"1",size:10}],onsubmit:function(){var c=this.toJSON();d.undoManager.transact(function(){a.merge(b,c.cols,c.rows)})}})},o.cell=function(){function b(a,b,c){(1===s.length||c)&&r.setAttrib(a,b,c)}function e(a,b,c){(1===s.length||c)&&r.setStyle(a,b,c)}function n(){l(r,this),p=a.extend(p,this.toJSON()),d.undoManager.transact(function(){c(s,function(a){b(a,"scope",p.scope),b(a,"style",p.style),b(a,"class",p.class),e(a,"width",h(p.width)),e(a,"height",h(p.height)),p.type&&a.nodeName.toLowerCase()!==p.type&&(a=r.rename(a,p.type)),1===s.length&&(i(a),j(a)),p.align&&d.formatter.apply("align"+p.align,{},a),p.valign&&d.formatter.apply("valign"+p.valign,{},a)}),d.focus()})}var o,p,q,r=d.dom,s=[];if(s=d.dom.select("td[data-mce-selected],th[data-mce-selected]"),o=d.dom.getParent(d.selection.getStart(),"td,th"),!s.length&&o&&s.push(o),o=o||s[0]){s.length>1?p={width:"",height:"",scope:"",class:"",align:"",style:"",type:o.nodeName.toLowerCase()}:(p={width:g(r.getStyle(o,"width")||r.getAttrib(o,"width")),height:g(r.getStyle(o,"height")||r.getAttrib(o,"height")),scope:r.getAttrib(o,"scope"),class:r.getAttrib(o,"class")},p.type=o.nodeName.toLowerCase(),c("left center right".split(" "),function(a){d.formatter.matchNode(o,"align"+a)&&(p.align=a)}),c("top middle bottom".split(" "),function(a){d.formatter.matchNode(o,"valign"+a)&&(p.valign=a)}),m(r,p,o)),d.settings.table_cell_class_list&&(q={name:"class",type:"listbox",label:"Class",values:k(d.settings.table_cell_class_list,function(a){a.value&&(a.textStyle=function(){return d.formatter.getCssText({block:"td",classes:[a.value]})})})});var t={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",layout:"grid",columns:2,labelGapCalc:!1,padding:0,defaults:{type:"textbox",maxWidth:50},items:[{label:"Width",name:"width"},{label:"Height",name:"height"},{label:"Cell type",name:"type",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{label:"Scope",name:"scope",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{label:"H Align",name:"align",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"V Align",name:"valign",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}]},q]};d.settings.table_cell_advtab!==!1?d.windowManager.open({title:"Cell properties",bodyType:"tabpanel",data:p,body:[{title:"General",type:"form",items:t},f(r)],onsubmit:n}):d.windowManager.open({title:"Cell properties",data:p,body:t,onsubmit:n})}},o.row=function(){function b(a,b,c){(1===u.length||c)&&t.setAttrib(a,b,c)}function e(a,b,c){(1===u.length||c)&&t.setStyle(a,b,c)}function j(){var f,g,j;l(t,this),r=a.extend(r,this.toJSON()),d.undoManager.transact(function(){var a=r.type;c(u,function(c){b(c,"scope",r.scope),b(c,"style",r.style),b(c,"class",r.class),e(c,"height",h(r.height)),a!==c.parentNode.nodeName.toLowerCase()&&(f=t.getParent(c,"table"),g=c.parentNode,j=t.select(a,f)[0],j||(j=t.create(a),f.firstChild?f.insertBefore(j,f.firstChild):f.appendChild(j)),j.appendChild(c),g.hasChildNodes()||t.remove(g)),1===u.length&&i(c),r.align&&d.formatter.apply("align"+r.align,{},c)}),d.focus()})}var n,o,p,q,r,s,t=d.dom,u=[];n=d.dom.getParent(d.selection.getStart(),"table"),o=d.dom.getParent(d.selection.getStart(),"td,th"),c(n.rows,function(a){c(a.cells,function(b){if(t.getAttrib(b,"data-mce-selected")||b==o)return u.push(a),!1})}),p=u[0],p&&(u.length>1?r={height:"",scope:"",class:"",align:"",type:p.parentNode.nodeName.toLowerCase()}:(r={height:g(t.getStyle(p,"height")||t.getAttrib(p,"height")),scope:t.getAttrib(p,"scope"),class:t.getAttrib(p,"class")},r.type=p.parentNode.nodeName.toLowerCase(),c("left center right".split(" "),function(a){d.formatter.matchNode(p,"align"+a)&&(r.align=a)}),m(t,r,p)),d.settings.table_row_class_list&&(q={name:"class",type:"listbox",label:"Class",values:k(d.settings.table_row_class_list,function(a){a.value&&(a.textStyle=function(){return d.formatter.getCssText({block:"tr",classes:[a.value]})})})}),s={type:"form",columns:2,padding:0,defaults:{type:"textbox"},items:[{type:"listbox",name:"type",label:"Row type",text:"Header",maxWidth:null,values:[{text:"Header",value:"thead"},{text:"Body",value:"tbody"},{text:"Footer",value:"tfoot"}]},{type:"listbox",name:"align",label:"Alignment",text:"None",maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height"},q]},d.settings.table_row_advtab!==!1?d.windowManager.open({title:"Row properties",data:r,bodyType:"tabpanel",body:[{title:"General",type:"form",items:s},f(t)],onsubmit:j}):d.windowManager.open({title:"Row properties",data:r,body:s,onsubmit:j}))}}}),d("tinymce/tableplugin/ResizeBars",["tinymce/util/Tools","tinymce/util/VK"],function(a,c){var d;return function(e){function f(a,b){return{index:a,y:e.dom.getPos(b).y}}function g(a,b){return{index:a,y:e.dom.getPos(b).y+b.offsetHeight}}function h(a,b){return{index:a,x:e.dom.getPos(b).x}}function i(a,b){return{index:a,x:e.dom.getPos(b).x+b.offsetWidth}}function j(){var a=e.getBody().dir;return"rtl"===a}function k(){return e.inline}function l(){return k?e.getBody().ownerDocument.body:e.getBody()}function m(a,b){return j()?i(a,b):h(a,b)}function n(a,b){return j()?h(a,b):i(a,b)}function o(a,b){return p(a,"width")/p(b,"width")*100}function p(a,b){var c=e.dom.getStyle(a,b,!0),d=parseInt(c,10);return d}function q(a){var b=p(a,"width"),c=p(a.parentElement,"width");return b/c*100}function r(a,b){var c=p(a,"width");return b/c*100}function s(a,b){var c=p(a.parentElement,"width");return b/c*100}function t(a,b,c){for(var d=[],e=1;e<c.length;e++){var f=c[e].element;d.push(a(e-1,f))}var g=c[c.length-1];return d.push(b(c.length-1,g.element)),d}function u(){var b=e.dom.select("."+ma,l());a.each(b,function(a){e.dom.remove(a)})}function v(a){u(),F(a)}function w(a,b,c,d,e,f,g,h){var i={"data-mce-bogus":"all",class:ma+" "+a,unselectable:"on","data-mce-resize":!1,style:"cursor: "+b+"; margin: 0; padding: 0; position: absolute; left: "+c+"px; top: "+d+"px; height: "+e+"px; width: "+f+"px; "};return i[g]=h,i}function x(b,c,d){a.each(b,function(a){var b=d.x,f=a.y-va/2,g=va,h=c;e.dom.add(l(),"div",w(na,oa,b,f,g,h,pa,a.index))})}function y(b,c,d){a.each(b,function(a){var b=a.x-va/2,f=d.y,g=c,h=va;e.dom.add(l(),"div",w(ra,sa,b,f,g,h,ta,a.index))})}function z(b){return a.map(b.rows,function(b){var c=a.map(b.cells,function(a){var b=a.hasAttribute("rowspan")?parseInt(a.getAttribute("rowspan"),10):1,c=a.hasAttribute("colspan")?parseInt(a.getAttribute("colspan"),10):1;return{element:a,rowspan:b,colspan:c}});return{element:b,cells:c}})}function A(c){function d(a,b){return a+","+b}function e(a,b){return h[d(a,b)]}function f(){var b=[];return a.each(i,function(a){b=b.concat(a.cells)}),b}function g(){return i}var h={},i=[],j=0,k=0;return a.each(c,function(c,e){var f=[];a.each(c.cells,function(a){for(var c=0;h[d(e,c)]!==b;)c++;for(var g={element:a.element,colspan:a.colspan,rowspan:a.rowspan,rowIndex:e,colIndex:c},i=0;i<a.colspan;i++)for(var l=0;l<a.rowspan;l++){var m=e+l,n=c+i;h[d(m,n)]=g,j=Math.max(j,m+1),k=Math.max(k,n+1)}f.push(g)}),i.push({element:c.element,cells:f})}),{grid:{maxRows:j,maxCols:k},getAt:e,getAllCells:f,getAllRows:g}}function B(a,b){for(var c=[],d=a;d<b;d++)c.push(d);return c}function C(a,b,c){for(var d,e=a(),f=0;f<e.length;f++)b(e[f])&&(d=e[f]);return d?d:c()}function D(b){var c=B(0,b.grid.maxCols),d=B(0,b.grid.maxRows);return a.map(c,function(a){function c(){for(var c=[],e=0;e<d.length;e++){var f=b.getAt(e,a);f&&f.colIndex===a&&c.push(f)}return c}function e(a){return 1===a.colspan}function f(){for(var c,e=0;e<d.length;e++)if(c=b.getAt(e,a))return c;return null}return C(c,e,f)})}function E(b){var c=B(0,b.grid.maxCols),d=B(0,b.grid.maxRows);return a.map(d,function(a){function d(){for(var d=[],e=0;e<c.length;e++){var f=b.getAt(a,e);f&&f.rowIndex===a&&d.push(f)}return d}function e(a){return 1===a.rowspan}function f(){return b.getAt(a,0)}return C(d,e,f)})}function F(a){var b=z(a),c=A(b),d=E(c),h=D(c),i=e.dom.getPos(a),j=d.length>0?t(f,g,d):[],k=h.length>0?t(m,n,h):[];x(j,a.offsetWidth,i),y(k,a.offsetHeight,i)}function G(a,b,c,d){if(b<0||b>=a.length-1)return"";var e=a[b];if(e)e={value:e,delta:0};else for(var f=a.slice(0,b).reverse(),g=0;g<f.length;g++)f[g]&&(e={value:f[g],delta:g+1});var h=a[b+1];if(h)h={value:h,delta:1};else for(var i=a.slice(b+1),j=0;j<i.length;j++)i[j]&&(h={value:i[j],delta:j+1});var k=h.delta-e.delta,l=Math.abs(h.value-e.value)/k;return c?l/p(d,"width")*100:l}function H(a,b){var c=e.dom.getStyle(a,b);return c||(c=e.dom.getAttrib(a,b)),c||(c=e.dom.getStyle(a,b,!0)),c}function I(a,b,c){var d=H(a,"width"),e=parseInt(d,10),f=b?o(a,c):p(a,"width");return(b&&!R(d)||!b&&!S(d))&&(e=0),!isNaN(e)&&e>0?e:f}function J(b,c,d){for(var e=D(b),f=a.map(e,function(a){return m(a.colIndex,a.element).x}),g=[],h=0;h<e.length;h++){var i=e[h].element.hasAttribute("colspan")?parseInt(e[h].element.getAttribute("colspan"),10):1,j=i>1?G(f,h):I(e[h].element,c,d);j=j?j:wa,g.push(j)}return g}function K(a){var b=H(a,"height"),c=parseInt(b,10);return R(b)&&(c=0),!isNaN(c)&&c>0?c:p(a,"height")}function L(b){for(var c=E(b),d=a.map(c,function(a){return f(a.rowIndex,a.element).y}),e=[],g=0;g<c.length;g++){var h=c[g].element.hasAttribute("rowspan")?parseInt(c[g].element.getAttribute("rowspan"),10):1,i=h>1?G(d,g):K(c[g].element);
i=i?i:xa,e.push(i)}return e}function M(b,c,d,e,f){function g(b){return a.map(b,function(){return 0})}function h(){var a;if(f)a=[100-l[0]];else{var b=Math.max(e,l[0]+d);a=[b-l[0]]}return a}function i(a,b){var c,f=g(l.slice(0,a)),h=g(l.slice(b+1));if(d>=0){var i=Math.max(e,l[b]-d);c=f.concat([d,i-l[b]]).concat(h)}else{var j=Math.max(e,l[a]+d),k=l[a]-j;c=f.concat([j-l[a],k]).concat(h)}return c}function j(a,b){var c,f=g(l.slice(0,b));if(d>=0)c=f.concat([d]);else{var h=Math.max(e,l[b]+d);c=f.concat([h-l[b]])}return c}var k,l=b.slice(0);return k=0===b.length?[]:1===b.length?h():0===c?i(0,1):c>0&&c<b.length-1?i(c,c+1):c===b.length-1?j(c-1,c):[]}function N(a,b,c){for(var d=0,e=a;e<b;e++)d+=c[e];return d}function O(b,c){var d=b.getAllCells();return a.map(d,function(a){var b=N(a.colIndex,a.colIndex+a.colspan,c);return{element:a.element,width:b,colspan:a.colspan}})}function P(b,c){var d=b.getAllCells();return a.map(d,function(a){var b=N(a.rowIndex,a.rowIndex+a.rowspan,c);return{element:a.element,height:b,rowspan:a.rowspan}})}function Q(b,c){var d=b.getAllRows();return a.map(d,function(a,b){return{element:a.element,height:c[b]}})}function R(a){return za.test(a)}function S(a){return Aa.test(a)}function T(b,c,d){function f(b,c){a.each(b,function(a){e.dom.setStyle(a.element,"width",a.width+c),e.dom.setAttrib(a.element,"width",null)})}function g(){return d<k.grid.maxCols-1?q(b):q(b)+s(b,c)}function h(){return d<k.grid.maxCols-1?p(b,"width"):p(b,"width")+c}function i(a,c,f){d!=k.grid.maxCols-1&&f||(e.dom.setStyle(b,"width",a+c),e.dom.setAttrib(b,"width",null))}for(var j=z(b),k=A(j),l=R(b.width)||R(b.style.width),m=J(k,l,b),n=l?r(b,c):c,o=M(m,d,n,wa,l,b),t=[],u=0;u<o.length;u++)t.push(o[u]+m[u]);var v=O(k,t),w=l?"%":"px",x=l?g():h();e.undoManager.transact(function(){f(v,w),i(x,w,l)})}function U(b,c,d){for(var f=z(b),g=A(f),h=L(g),i=[],j=0,k=0;k<h.length;k++)i.push(k===d?c+h[k]:h[k]),j+=j[k];var l=P(g,i),m=Q(g,i);e.undoManager.transact(function(){a.each(m,function(a){e.dom.setStyle(a.element,"height",a.height+"px"),e.dom.setAttrib(a.element,"height",null)}),a.each(l,function(a){e.dom.setStyle(a.element,"height",a.height+"px"),e.dom.setAttrib(a.element,"height",null)}),e.dom.setStyle(b,"height",j+"px"),e.dom.setAttrib(b,"height",null)})}function V(){ga=setTimeout(function(){Z()},200)}function W(){clearTimeout(ga)}function X(){var a=document.createElement("div");return a.setAttribute("style","margin: 0; padding: 0; position: fixed; left: 0px; top: 0px; height: 100%; width: 100%;"),a.setAttribute("data-mce-bogus","all"),a}function Y(a,b){e.dom.bind(a,"mouseup",function(){Z()}),e.dom.bind(a,"mousemove",function(a){W(),ha&&b(a)}),e.dom.bind(a,"mouseout",function(){V()})}function Z(){if(e.dom.remove(ia),ha){e.dom.removeClass(ja,ya),ha=!1;var a,b;if(_(ja)){var c=parseInt(e.dom.getAttrib(ja,ua),10),f=e.dom.getPos(ja).x;a=parseInt(e.dom.getAttrib(ja,ta),10),b=j()?c-f:f-c,Math.abs(b)>=1&&T(d,b,a)}else if(aa(ja)){var g=parseInt(e.dom.getAttrib(ja,qa),10),h=e.dom.getPos(ja).y;a=parseInt(e.dom.getAttrib(ja,pa),10),b=h-g,Math.abs(b)>=1&&U(d,b,a)}v(d),e.nodeChanged()}}function $(a,b){ia=ia?ia:X(),ha=!0,e.dom.addClass(a,ya),ja=a,Y(ia,b),e.dom.add(l(),ia)}function _(a){return e.dom.hasClass(a,ra)}function aa(a){return e.dom.hasClass(a,na)}function ba(a){ka=ka!==b?ka:a.clientX;var c=a.clientX-ka;ka=a.clientX;var d=e.dom.getPos(ja).x;e.dom.setStyle(ja,"left",d+c+"px")}function ca(a){la=la!==b?la:a.clientY;var c=a.clientY-la;la=a.clientY;var d=e.dom.getPos(ja).y;e.dom.setStyle(ja,"top",d+c+"px")}function da(a){ka=b,$(a,ba)}function ea(a){la=b,$(a,ca)}function fa(a){var b=a.target,c=e.getBody();if(e.$.contains(c,d)||d===c)if(_(b)){a.preventDefault();var f=e.dom.getPos(b).x;e.dom.setAttrib(b,ua,f),da(b)}else if(aa(b)){a.preventDefault();var g=e.dom.getPos(b).y;e.dom.setAttrib(b,qa,g),ea(b)}else u()}var ga,ha,ia,ja,ka,la,ma="mce-resize-bar",na="mce-resize-bar-row",oa="row-resize",pa="data-row",qa="data-initial-top",ra="mce-resize-bar-col",sa="col-resize",ta="data-col",ua="data-initial-left",va=4,wa=10,xa=10,ya="mce-resize-bar-dragging",za=new RegExp(/(\d+(\.\d+)?%)/),Aa=new RegExp(/px|em/);return e.on("init",function(){e.dom.bind(l(),"mousedown",fa)}),e.on("ObjectResized",function(b){var c=b.target;if("TABLE"===c.nodeName){var d=[];a.each(c.rows,function(b){a.each(b.cells,function(a){var b=e.dom.getStyle(a,"width",!0);d.push({cell:a,width:b})})}),a.each(d,function(a){e.dom.setStyle(a.cell,"width",a.width),e.dom.setAttrib(a.cell,"width",null)})}}),e.on("mouseover",function(a){if(!ha){var b=e.dom.getParent(a.target,"table");("TABLE"===a.target.nodeName||b)&&(d=b,v(b))}}),e.on("keydown",function(a){switch(a.keyCode){case c.LEFT:case c.RIGHT:case c.UP:case c.DOWN:u()}}),e.on("remove",function(){u(),e.dom.unbind(l(),"mousedown",fa)}),{adjustWidth:T,adjustHeight:U,clearBars:u,drawBars:F,determineDeltas:M,getTableGrid:A,getTableDetails:z,getWidths:J,getPixelHeights:L,isPercentageBasedSize:R,isPixelBasedSize:S,recalculateWidths:O,recalculateCellHeights:P,recalculateRowHeights:Q}}}),d("tinymce/tableplugin/Plugin",["tinymce/tableplugin/TableGrid","tinymce/tableplugin/Quirks","tinymce/tableplugin/CellSelection","tinymce/tableplugin/Dialogs","tinymce/tableplugin/ResizeBars","tinymce/util/Tools","tinymce/dom/TreeWalker","tinymce/Env","tinymce/PluginManager"],function(a,b,c,d,e,f,g,h,i){function j(f){function g(a){return function(){f.execCommand(a)}}function i(a,b){var c,d,e,g;for(e='<table id="__mce"><tbody>',c=0;c<b;c++){for(e+="<tr>",d=0;d<a;d++)e+="<td>"+(h.ie&&h.ie<10?"&nbsp;":"<br>")+"</td>";e+="</tr>"}return e+="</tbody></table>",f.undoManager.transact(function(){f.insertContent(e),g=f.dom.get("__mce"),f.dom.setAttrib(g,"id",null),f.$("tr",g).each(function(a,b){f.fire("newrow",{node:b}),f.$("th,td",b).each(function(a,b){f.fire("newcell",{node:b})})}),f.dom.setAttribs(g,f.settings.table_default_attributes||{}),f.dom.setStyles(g,f.settings.table_default_styles||{})}),g}function j(a,b,c){function d(){var d,e,g,h={},i=0;e=f.dom.select("td[data-mce-selected],th[data-mce-selected]"),d=e[0],d||(d=f.selection.getStart()),c&&e.length>0?(k(e,function(a){return h[a.parentNode.parentNode.nodeName]=1}),k(h,function(a){i+=a}),g=1!==i):g=!f.dom.getParent(d,b),a.disabled(g),f.selection.selectorChanged(b,function(b){a.disabled(!b)})}f.initialized?d():f.on("init",d)}function l(){j(this,"table")}function m(){j(this,"td,th")}function n(){j(this,"td,th",!0)}function o(){var a="";a='<table role="grid" class="mce-grid mce-grid-border" aria-readonly="true">';for(var b=0;b<10;b++){a+="<tr>";for(var c=0;c<10;c++)a+='<td role="gridcell" tabindex="-1"><a id="mcegrid'+(10*b+c)+'" href="#" data-mce-x="'+c+'" data-mce-y="'+b+'"></a></td>';a+="</tr>"}return a+="</table>",a+='<div class="mce-text-center" role="presentation">1 x 1</div>'}function p(a,b,c){var d,e,g,h,i,j=c.getEl().getElementsByTagName("table")[0],k=c.isRtl()||"tl-tr"==c.parent().rel;for(j.nextSibling.innerHTML=a+1+" x "+(b+1),k&&(a=9-a),e=0;e<10;e++)for(d=0;d<10;d++)h=j.rows[e].childNodes[d].firstChild,i=(k?d>=a:d<=a)&&e<=b,f.dom.toggleClass(h,"mce-active",i),i&&(g=h);return g.parentNode}function q(){f.addButton("tableprops",{title:"Table properties",onclick:y.tableProps,icon:"table"}),f.addButton("tabledelete",{title:"Delete table",onclick:g("mceTableDelete")}),f.addButton("tablecellprops",{title:"Cell properties",onclick:g("mceTableCellProps")}),f.addButton("tablemergecells",{title:"Merge cells",onclick:g("mceTableMergeCells")}),f.addButton("tablesplitcells",{title:"Split cell",onclick:g("mceTableSplitCells")}),f.addButton("tableinsertrowbefore",{title:"Insert row before",onclick:g("mceTableInsertRowBefore")}),f.addButton("tableinsertrowafter",{title:"Insert row after",onclick:g("mceTableInsertRowAfter")}),f.addButton("tabledeleterow",{title:"Delete row",onclick:g("mceTableDeleteRow")}),f.addButton("tablerowprops",{title:"Row properties",onclick:g("mceTableRowProps")}),f.addButton("tablecutrow",{title:"Cut row",onclick:g("mceTableCutRow")}),f.addButton("tablecopyrow",{title:"Copy row",onclick:g("mceTableCopyRow")}),f.addButton("tablepasterowbefore",{title:"Paste row before",onclick:g("mceTablePasteRowBefore")}),f.addButton("tablepasterowafter",{title:"Paste row after",onclick:g("mceTablePasteRowAfter")}),f.addButton("tableinsertcolbefore",{title:"Insert column before",onclick:g("mceTableInsertColBefore")}),f.addButton("tableinsertcolafter",{title:"Insert column after",onclick:g("mceTableInsertColAfter")}),f.addButton("tabledeletecol",{title:"Delete column",onclick:g("mceTableDeleteCol")})}function r(a){var b=f.dom.is(a,"table")&&f.getBody().contains(a);return b}function s(){var a=f.settings.table_toolbar;""!==a&&a!==!1&&(a||(a="tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol"),f.addContextToolbar(r,a))}function t(){return v}function u(a){v=a}var v,w,x=this,y=new d(f);!f.settings.object_resizing||f.settings.table_resize_bars===!1||f.settings.object_resizing!==!0&&"table"!==f.settings.object_resizing||(w=e(f)),f.settings.table_grid===!1?f.addMenuItem("inserttable",{text:"Table",icon:"table",context:"table",onclick:y.table}):f.addMenuItem("inserttable",{text:"Table",icon:"table",context:"table",ariaHideMenu:!0,onclick:function(a){a.aria&&(this.parent().hideAll(),a.stopImmediatePropagation(),y.table())},onshow:function(){p(0,0,this.menu.items()[0])},onhide:function(){var a=this.menu.items()[0].getEl().getElementsByTagName("a");f.dom.removeClass(a,"mce-active"),f.dom.addClass(a[0],"mce-active")},menu:[{type:"container",html:o(),onPostRender:function(){this.lastX=this.lastY=0},onmousemove:function(a){var b,c,d=a.target;"A"==d.tagName.toUpperCase()&&(b=parseInt(d.getAttribute("data-mce-x"),10),c=parseInt(d.getAttribute("data-mce-y"),10),(this.isRtl()||"tl-tr"==this.parent().rel)&&(b=9-b),b===this.lastX&&c===this.lastY||(p(b,c,a.control),this.lastX=b,this.lastY=c))},onclick:function(a){var b=this;"A"==a.target.tagName.toUpperCase()&&(a.preventDefault(),a.stopPropagation(),b.parent().cancel(),f.undoManager.transact(function(){i(b.lastX+1,b.lastY+1)}),f.addVisual())}}]}),f.addMenuItem("tableprops",{text:"Table properties",context:"table",onPostRender:l,onclick:y.tableProps}),f.addMenuItem("deletetable",{text:"Delete table",context:"table",onPostRender:l,cmd:"mceTableDelete"}),f.addMenuItem("cell",{separator:"before",text:"Cell",context:"table",menu:[{text:"Cell properties",onclick:g("mceTableCellProps"),onPostRender:m},{text:"Merge cells",onclick:g("mceTableMergeCells"),onPostRender:n},{text:"Split cell",onclick:g("mceTableSplitCells"),onPostRender:m}]}),f.addMenuItem("row",{text:"Row",context:"table",menu:[{text:"Insert row before",onclick:g("mceTableInsertRowBefore"),onPostRender:m},{text:"Insert row after",onclick:g("mceTableInsertRowAfter"),onPostRender:m},{text:"Delete row",onclick:g("mceTableDeleteRow"),onPostRender:m},{text:"Row properties",onclick:g("mceTableRowProps"),onPostRender:m},{text:"-"},{text:"Cut row",onclick:g("mceTableCutRow"),onPostRender:m},{text:"Copy row",onclick:g("mceTableCopyRow"),onPostRender:m},{text:"Paste row before",onclick:g("mceTablePasteRowBefore"),onPostRender:m},{text:"Paste row after",onclick:g("mceTablePasteRowAfter"),onPostRender:m}]}),f.addMenuItem("column",{text:"Column",context:"table",menu:[{text:"Insert column before",onclick:g("mceTableInsertColBefore"),onPostRender:m},{text:"Insert column after",onclick:g("mceTableInsertColAfter"),onPostRender:m},{text:"Delete column",onclick:g("mceTableDeleteCol"),onPostRender:m}]});var z=[];k("inserttable tableprops deletetable | cell row column".split(" "),function(a){"|"==a?z.push({text:"-"}):z.push(f.menuItems[a])}),f.addButton("table",{type:"menubutton",title:"Table",menu:z}),h.isIE||f.on("click",function(a){a=a.target,"TABLE"===a.nodeName&&(f.selection.select(a),f.nodeChanged())}),x.quirks=new b(f),f.on("Init",function(){x.cellSelection=new c(f,function(a){a&&w&&w.clearBars()}),x.resizeBars=w}),f.on("PreInit",function(){f.serializer.addAttributeFilter("data-mce-cell-padding,data-mce-border,data-mce-border-color",function(a,b){for(var c=a.length;c--;)a[c].attr(b,null)})}),k({mceTableSplitCells:function(a){a.split()},mceTableMergeCells:function(a){var b;b=f.dom.getParent(f.selection.getStart(),"th,td"),f.dom.select("td[data-mce-selected],th[data-mce-selected]").length?a.merge():y.merge(a,b)},mceTableInsertRowBefore:function(a){a.insertRow(!0)},mceTableInsertRowAfter:function(a){a.insertRow()},mceTableInsertColBefore:function(a){a.insertCol(!0)},mceTableInsertColAfter:function(a){a.insertCol()},mceTableDeleteCol:function(a){a.deleteCols()},mceTableDeleteRow:function(a){a.deleteRows()},mceTableCutRow:function(a){v=a.cutRows()},mceTableCopyRow:function(a){v=a.copyRows()},mceTablePasteRowBefore:function(a){a.pasteRows(v,!0)},mceTablePasteRowAfter:function(a){a.pasteRows(v)},mceSplitColsBefore:function(a){a.splitCols(!0)},mceSplitColsAfter:function(a){a.splitCols(!1)},mceTableDelete:function(a){w&&w.clearBars(),a.deleteTable()}},function(b,c){f.addCommand(c,function(){var c=new a(f);c&&(b(c),f.execCommand("mceRepaint"),x.cellSelection.clear())})}),k({mceInsertTable:y.table,mceTableProps:function(){y.table(!0)},mceTableRowProps:y.row,mceTableCellProps:y.cell},function(a,b){f.addCommand(b,function(b,c){a(c)})}),q(),s(),f.settings.table_tab_navigation!==!1&&f.on("keydown",function(b){var c,d,e;9==b.keyCode&&(c=f.dom.getParent(f.selection.getStart(),"th,td"),c&&(b.preventDefault(),d=new a(f),e=b.shiftKey?-1:1,f.undoManager.transact(function(){!d.moveRelIdx(c,e)&&e>0&&(d.insertRow(),d.refresh(),d.moveRelIdx(c,e))})))}),x.insertTable=i,x.setClipboardRows=u,x.getClipboardRows=t}var k=f.each;i.add("table",j)})}(this);