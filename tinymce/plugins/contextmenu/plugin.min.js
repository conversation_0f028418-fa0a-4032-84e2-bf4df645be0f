tinymce.PluginManager.add("contextmenu",function(a){var b,c,d=a.settings.contextmenu_never_use_native,e=function(a){return a.ctrlKey&&!d},f=function(){return tinymce.Env.mac&&tinymce.Env.webkit},g=function(){return c===!0};return a.on("mousedown",function(b){f()&&2===b.button&&!e(b)&&a.selection.isCollapsed()&&a.once("contextmenu",function(b){a.selection.placeCaretAt(b.clientX,b.clientY)})}),a.on("contextmenu",function(d){var f;if(!e(d)){if(d.preventDefault(),f=a.settings.contextmenu||"link openlink image inserttable | cell row column deletetable",b)b.show();else{var g=[];tinymce.each(f.split(/[ ,]/),function(b){var c=a.menuItems[b];"|"==b&&(c={text:b}),c&&(c.shortcut="",g.push(c))});for(var h=0;h<g.length;h++)"|"==g[h].text&&(0!==h&&h!=g.length-1||g.splice(h,1));b=new tinymce.ui.Menu({items:g,context:"contextmenu",classes:"contextmenu"}).renderTo(),b.on("hide",function(a){a.control===this&&(c=!1)}),a.on("remove",function(){b.remove(),b=null})}var i={x:d.pageX,y:d.pageY};a.inline||(i=tinymce.DOM.getPos(a.getContentAreaContainer()),i.x+=d.clientX,i.y+=d.clientY),b.moveTo(i.x,i.y),c=!0}}),{isContextMenuVisible:g}});