tinymce.PluginManager.add("advlist",function(a){function b(b){return a.$.contains(a.getBody(),b)}function c(a){return a&&/^(OL|UL|DL)$/.test(a.nodeName)&&b(a)}function d(a,b){var c=[];return tinymce.each(b.split(/[ ,]/),function(a){c.push({text:a.replace(/\-/g," ").replace(/\b\w/g,function(a){return a.toUpperCase()}),data:"default"==a?"":a})}),c}function e(b,c){a.undoManager.transact(function(){var d,e=a.dom,f=a.selection;if(d=e.getParent(f.getNode(),"ol,ul"),!d||d.nodeName!=b||c===!1){var g={"list-style-type":c?c:""};a.execCommand("UL"==b?"InsertUnorderedList":"InsertOrderedList",!1,g)}d=e.getParent(f.getNode(),"ol,ul"),d&&tinymce.util.Tools.each(e.select("ol,ul",d).concat([d]),function(a){a.nodeName!==b&&c!==!1&&(a=e.rename(a,b)),e.setStyle(a,"listStyleType",c?c:null),a.removeAttribute("data-mce-style")}),a.focus()})}function f(b){var c=a.dom.getStyle(a.dom.getParent(a.selection.getNode(),"ol,ul"),"listStyleType")||"";b.control.items().each(function(a){a.active(a.settings.data===c)})}var g,h;g=d("OL",a.getParam("advlist_number_styles","default,lower-alpha,lower-greek,lower-roman,upper-alpha,upper-roman")),h=d("UL",a.getParam("advlist_bullet_styles","default,circle,disc,square"));var i=function(b){return function(){var d=this;a.on("NodeChange",function(a){var e=tinymce.util.Tools.grep(a.parents,c);d.active(e.length>0&&e[0].nodeName===b)})}};tinymce.PluginManager.get("lists")&&(a.addCommand("ApplyUnorderedListStyle",function(a,b){e("UL",b["list-style-type"])}),a.addCommand("ApplyOrderedListStyle",function(a,b){e("OL",b["list-style-type"])}),a.addButton("numlist",{type:"splitbutton",tooltip:"Numbered list",menu:g,onPostRender:i("OL"),onshow:f,onselect:function(a){e("OL",a.control.settings.data)},onclick:function(){e("OL",!1)}}),a.addButton("bullist",{type:"splitbutton",tooltip:"Bullet list",onPostRender:i("UL"),menu:h,onshow:f,onselect:function(a){e("UL",a.control.settings.data)},onclick:function(){e("UL",!1)}}))});