tinymce.PluginManager.add("lists",function(a){function b(b){return a.$.contains(a.getBody(),b)}function c(a){return a&&"BR"==a.nodeName}function d(a){return a&&/^(OL|UL|DL)$/.test(a.nodeName)&&b(a)}function e(a){return a&&/^(LI|DT|DD)$/.test(a.nodeName)}function f(a){return a.parentNode.firstChild==a}function g(a){return a.parentNode.lastChild==a}function h(b){return b&&!!a.schema.getTextBlockElements()[b.nodeName]}function i(b){return b===a.getBody()}function j(a){return a&&3===a.nodeType}function k(a,b){var c=tinymce.dom.RangeUtils.getNode(a,b);if(e(a)&&j(c)){var d=b>=a.childNodes.length?c.data.length:0;return{container:c,offset:d}}return{container:a,offset:b}}function l(a){var b=a.cloneRange(),c=k(a.startContainer,a.startOffset);b.setStart(c.container,c.offset);var d=k(a.endContainer,a.endOffset);return b.setEnd(d.container,d.offset),b}var m=this;a.on("init",function(){function j(a,b){var c=H.isEmpty(a);return!(b&&H.select("span[data-mce-type=bookmark]").length>0)&&c}function k(a){function b(b){var d,e,f;e=a[b?"startContainer":"endContainer"],f=a[b?"startOffset":"endOffset"],1==e.nodeType&&(d=H.create("span",{"data-mce-type":"bookmark"}),e.hasChildNodes()?(f=Math.min(f,e.childNodes.length-1),b?e.insertBefore(d,e.childNodes[f]):H.insertAfter(d,e.childNodes[f])):e.appendChild(d),e=d,f=0),c[b?"startContainer":"endContainer"]=e,c[b?"startOffset":"endOffset"]=f}var c={};return b(!0),a.collapsed||b(),c}function n(a){function b(b){function c(a){for(var b=a.parentNode.firstChild,c=0;b;){if(b==a)return c;1==b.nodeType&&"bookmark"==b.getAttribute("data-mce-type")||c++,b=b.nextSibling}return-1}var d,e,f;d=f=a[b?"startContainer":"endContainer"],e=a[b?"startOffset":"endOffset"],d&&(1==d.nodeType&&(e=c(d),d=d.parentNode,H.remove(f)),a[b?"startContainer":"endContainer"]=d,a[b?"startOffset":"endOffset"]=e)}b(!0),b();var c=H.createRng();c.setStart(a.startContainer,a.startOffset),a.endContainer&&c.setEnd(a.endContainer,a.endOffset),I.setRng(l(c))}function o(b,c){var d,e,f,g=H.createFragment(),h=a.schema.getBlockElements();if(a.settings.forced_root_block&&(c=c||a.settings.forced_root_block),c&&(e=H.create(c),e.tagName===a.settings.forced_root_block&&H.setAttribs(e,a.settings.forced_root_block_attrs),g.appendChild(e)),b)for(;d=b.firstChild;){var i=d.nodeName;f||"SPAN"==i&&"bookmark"==d.getAttribute("data-mce-type")||(f=!0),h[i]?(g.appendChild(d),e=null):c?(e||(e=H.create(c),g.appendChild(e)),e.appendChild(d)):g.appendChild(d)}return a.settings.forced_root_block?f||tinymce.Env.ie&&!(tinymce.Env.ie>10)||e.appendChild(H.create("br",{"data-mce-bogus":"1"})):g.appendChild(H.create("br")),g}function p(){return tinymce.grep(I.getSelectedBlocks(),function(a){return e(a)})}function q(a,b,c){function d(a){tinymce.each(g,function(c){a.parentNode.insertBefore(c,b.parentNode)}),H.remove(a)}var e,f,g,h;for(g=H.select('span[data-mce-type="bookmark"]',a),c=c||o(b),e=H.createRng(),e.setStartAfter(b),e.setEndAfter(a),f=e.extractContents(),h=f.firstChild;h;h=h.firstChild)if("LI"==h.nodeName&&H.isEmpty(h)){H.remove(h);break}H.isEmpty(f)||H.insertAfter(f,a),H.insertAfter(c,a),j(b.parentNode)&&d(b.parentNode),H.remove(b),j(a)&&H.remove(a)}function r(a){var b,c;if(b=a.nextSibling,b&&d(b)&&b.nodeName==a.nodeName&&J(a,b)){for(;c=b.firstChild;)a.appendChild(c);H.remove(b)}if(b=a.previousSibling,b&&d(b)&&b.nodeName==a.nodeName&&J(a,b)){for(;c=b.lastChild;)a.insertBefore(c,a.firstChild);H.remove(b)}}function s(a){tinymce.each(tinymce.grep(H.select("ol,ul",a)),t)}function t(a){var b,c=a.parentNode;"LI"==c.nodeName&&c.firstChild==a&&(b=c.previousSibling,b&&"LI"==b.nodeName?(b.appendChild(a),j(c)&&H.remove(c)):H.setStyle(c,"listStyleType","none")),d(c)&&(b=c.previousSibling,b&&"LI"==b.nodeName&&b.appendChild(a))}function u(a){function b(a){j(a)&&H.remove(a)}var c,e=a.parentNode,h=e.parentNode;return!!i(e)||("DD"==a.nodeName?(H.rename(a,"DT"),!0):f(a)&&g(a)?("LI"==h.nodeName?(H.insertAfter(a,h),b(h),H.remove(e)):d(h)?H.remove(e,!0):(h.insertBefore(o(a),e),H.remove(e)),!0):f(a)?("LI"==h.nodeName?(H.insertAfter(a,h),a.appendChild(e),b(h)):d(h)?h.insertBefore(a,e):(h.insertBefore(o(a),e),H.remove(a)),!0):g(a)?("LI"==h.nodeName?H.insertAfter(a,h):d(h)?H.insertAfter(a,e):(H.insertAfter(o(a),e),H.remove(a)),!0):("LI"==h.nodeName?(e=h,c=o(a,"LI")):c=d(h)?o(a,"LI"):o(a),q(e,a,c),s(e.parentNode),!0))}function v(a){function b(b,c){var e;if(d(b)){for(;e=a.lastChild.firstChild;)c.appendChild(e);H.remove(b)}}var c,e,f;return"DT"==a.nodeName?(H.rename(a,"DD"),!0):(c=a.previousSibling,c&&d(c)?(c.appendChild(a),!0):c&&"LI"==c.nodeName&&d(c.lastChild)?(c.lastChild.appendChild(a),b(a.lastChild,c.lastChild),!0):(c=a.nextSibling,c&&d(c)?(c.insertBefore(a,c.firstChild),!0):(c=a.previousSibling,!(!c||"LI"!=c.nodeName)&&(e=H.create(a.parentNode.nodeName),f=H.getStyle(a.parentNode,"listStyleType"),f&&H.setStyle(e,"listStyleType",f),c.appendChild(e),e.appendChild(a),b(a.lastChild,e),!0))))}function w(){var b=p();if(b.length){for(var c=k(I.getRng(!0)),d=0;d<b.length&&(v(b[d])||0!==d);d++);return n(c),a.nodeChanged(),!0}}function x(){var b=p();if(b.length){var c,d,e=k(I.getRng(!0)),f=a.getBody();for(c=b.length;c--;)for(var g=b[c].parentNode;g&&g!=f;){for(d=b.length;d--;)if(b[d]===g){b.splice(c,1);break}g=g.parentNode}for(c=0;c<b.length&&(u(b[c])||0!==c);c++);return n(e),a.nodeChanged(),!0}}function y(b,e){function f(){function b(a){var b,c;for(b=i[a?"startContainer":"endContainer"],c=i[a?"startOffset":"endOffset"],1==b.nodeType&&(b=b.childNodes[Math.min(c,b.childNodes.length-1)]||b);b.parentNode!=f;){if(h(b))return b;if(/^(TD|TH)$/.test(b.parentNode.nodeName))return b;b=b.parentNode}return b}for(var d,e=[],f=a.getBody(),g=b(!0),j=b(),k=[],l=g;l&&(k.push(l),l!=j);l=l.nextSibling);return tinymce.each(k,function(a){if(h(a))return e.push(a),void(d=null);if(H.isBlock(a)||c(a))return c(a)&&H.remove(a),void(d=null);var b=a.nextSibling;return tinymce.dom.BookmarkManager.isBookmarkNode(a)&&(h(b)||!b&&a.parentNode==f)?void(d=null):(d||(d=H.create("p"),a.parentNode.insertBefore(d,a),e.push(d)),void d.appendChild(a))}),e}var g,i=I.getRng(!0),j="LI";"false"!==H.getContentEditable(I.getNode())&&(b=b.toUpperCase(),"DL"==b&&(j="DT"),g=k(i),tinymce.each(f(),function(a){var c,f,g=function(a){var b=H.getStyle(a,"list-style-type"),c=e?e["list-style-type"]:"";return c=null===c?"":c,b===c};f=a.previousSibling,f&&d(f)&&f.nodeName==b&&g(f)?(c=f,a=H.rename(a,j),f.appendChild(a)):(c=H.create(b),a.parentNode.insertBefore(c,a),c.appendChild(a),a=H.rename(a,j)),K(c,e),r(c)}),n(g))}function z(){var b=k(I.getRng(!0)),c=a.getBody();tinymce.each(p(),function(a){var b,e;if(!i(a.parentNode)){if(j(a))return void u(a);for(b=a;b&&b!=c;b=b.parentNode)d(b)&&(e=b);q(e,a),s(e.parentNode)}}),n(b)}function A(a,b){var c=H.getParent(I.getStart(),"OL,UL,DL");if(!i(c))if(c)if(c.nodeName==a)z(a);else{var d=k(I.getRng(!0));K(c,b),r(H.rename(c,a)),n(d)}else y(a,b)}function B(b){return function(){var c=H.getParent(a.selection.getStart(),"UL,OL,DL");return c&&c.nodeName==b}}function C(a){return!!c(a)&&!(!H.isBlock(a.nextSibling)||c(a.previousSibling))}function D(b,c){var d,e,f=b.startContainer,g=b.startOffset;if(3==f.nodeType&&(c?g<f.data.length:g>0))return f;for(d=a.schema.getNonEmptyElements(),1==f.nodeType&&(f=tinymce.dom.RangeUtils.getNode(f,g)),e=new tinymce.dom.TreeWalker(f,a.getBody()),c&&C(f)&&e.next();f=e[c?"next":"prev2"]();){if("LI"==f.nodeName&&!f.hasChildNodes())return f;if(d[f.nodeName])return f;if(3==f.nodeType&&f.data.length>0)return f}}function E(a,e){var f,g,h=a.parentNode;if(b(a)&&b(e)){if(d(e.lastChild)&&(g=e.lastChild),h==e.lastChild&&c(h.previousSibling)&&H.remove(h.previousSibling),f=e.lastChild,f&&c(f)&&a.hasChildNodes()&&H.remove(f),j(e,!0)&&H.$(e).empty(),!j(a,!0))for(;f=a.firstChild;)e.appendChild(f);g&&e.appendChild(g),H.remove(a),j(h)&&!i(h)&&H.remove(h)}}function F(a){var b,c,d,e=H.getParent(I.getStart(),"LI");if(e){if(b=e.parentNode,i(b)&&H.isEmpty(b))return!0;if(c=l(I.getRng(!0)),d=H.getParent(D(c,a),"LI"),d&&d!=e){var f=k(c);return a?E(d,e):E(e,d),n(f),!0}if(!d&&!a&&z(b.nodeName))return!0}}function G(){var b=a.dom.getParent(a.selection.getStart(),"LI,DT,DD");return!!(b||p().length>0)&&(a.undoManager.transact(function(){a.execCommand("Delete"),s(a.getBody())}),!0)}var H=a.dom,I=a.selection,J=function(b,c){var d=a.dom.getStyle(b,"list-style-type",!0),e=a.dom.getStyle(c,"list-style-type",!0);return d===e},K=function(a,b){H.setStyle(a,"list-style-type",b?b["list-style-type"]:null)};m.backspaceDelete=function(a){return I.isCollapsed()?F(a):G()},a.on("BeforeExecCommand",function(b){var c,d=b.command.toLowerCase();if("indent"==d?w()&&(c=!0):"outdent"==d&&x()&&(c=!0),c)return a.fire("ExecCommand",{command:b.command}),b.preventDefault(),!0}),a.addCommand("InsertUnorderedList",function(a,b){A("UL",b)}),a.addCommand("InsertOrderedList",function(a,b){A("OL",b)}),a.addCommand("InsertDefinitionList",function(a,b){A("DL",b)}),a.addQueryStateHandler("InsertUnorderedList",B("UL")),a.addQueryStateHandler("InsertOrderedList",B("OL")),a.addQueryStateHandler("InsertDefinitionList",B("DL")),a.on("keydown",function(b){9!=b.keyCode||tinymce.util.VK.metaKeyPressed(b)||a.dom.getParent(a.selection.getStart(),"LI,DT,DD")&&(b.preventDefault(),b.shiftKey?x():w())})});var n=function(b){return function(){var c=this;a.on("NodeChange",function(a){var e=tinymce.util.Tools.grep(a.parents,d);c.active(e.length>0&&e[0].nodeName===b)})}};tinymce.PluginManager.get("advlist")||(a.addButton("numlist",{title:"Numbered list",cmd:"InsertOrderedList",onPostRender:n("OL")}),a.addButton("bullist",{title:"Bullet list",cmd:"InsertUnorderedList",onPostRender:n("UL")})),a.addButton("indent",{icon:"indent",title:"Increase indent",cmd:"Indent",onPostRender:function(){var b=this;a.on("nodechange",function(){for(var c=a.selection.getSelectedBlocks(),d=!1,e=0,g=c.length;!d&&e<g;e++){var h=c[e].nodeName;d="LI"==h&&f(c[e])||"UL"==h||"OL"==h||"DD"==h}b.disabled(d)})}}),a.on("keydown",function(a){a.keyCode==tinymce.util.VK.BACKSPACE?m.backspaceDelete()&&a.preventDefault():a.keyCode==tinymce.util.VK.DELETE&&m.backspaceDelete(!0)&&a.preventDefault()})});