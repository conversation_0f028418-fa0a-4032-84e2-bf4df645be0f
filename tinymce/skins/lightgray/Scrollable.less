// Scrollbar

.@{prefix}-scrollbar {
	position: absolute;
	width: 7px;
	height: 100%;
	top: 2px;
	right: 2px;
	.opacity(0.4);
}

.@{prefix}-scrollbar-h {
	top: auto;
	right: auto;
	left: 2px;
	bottom: 2px;
	width: 100%;
	height: 7px;
}

.@{prefix}-scrollbar-thumb {
	position: absolute;
	background-color: #000;
	border: 1px solid #888;
	border-color: rgba(85, 85, 85, .6);
	width: 5px;
	height: 100%;
	.border-radius(7px);
}

.@{prefix}-scrollbar-h .@{prefix}-scrollbar-thumb {
	width: 100%;
	height: 5px;
}

.@{prefix}-scrollbar:hover, .@{prefix}-scrollbar.@{prefix}-active {
	background-color: #AAA;
	.opacity(0.6);
	.border-radius(7px);
}

.@{prefix}-scroll {
	position: relative;
}
