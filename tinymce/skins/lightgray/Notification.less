// notification

.@{prefix}-notification {
	position: absolute;
	background-color: @notification-bg;
	padding: 5px;
	margin-top: 5px;
	border-width: 1px;
	border-style: solid;
	border-color: @notification-border;
	transition: transform 100ms ease-in, opacity 150ms ease-in;
	opacity: 0;
}

.@{prefix}-notification.@{prefix}-in {
	opacity: 1;
}

.@{prefix}-notification-success {
	background-color: @notification-success-bg;
	border-color: @notification-success-border;
}

.@{prefix}-notification-info {
	background-color: @notification-info-bg;
	border-color: @notification-info-border;
}

.@{prefix}-notification-warning {
	background-color: @notification-warning-bg;
	border-color: @notification-warning-border;
}

.@{prefix}-notification-error {
	background-color: @notification-error-bg;
	border-color: @notification-error-border;
}

.@{prefix}-notification.@{prefix}-has-close {
	padding-right: 15px;
}

.@{prefix}-notification .@{prefix}-ico {
	margin-top: 5px;
}

.@{prefix}-notification-inner {
	display: inline-block;
	font-size: @notification-font-size;
	margin: 5px 8px 4px 8px;
	text-align: center;
	white-space: normal;
	color: @notification-info-text;
}

.@{prefix}-notification-inner a {
	text-decoration: underline;
	cursor: pointer;
}

.@{prefix}-notification .@{prefix}-progress {
	margin-right: 8px;
}

.@{prefix}-notification .@{prefix}-progress .@{prefix}-text {
	margin-top: 5px;
}

.@{prefix}-notification *, .@{prefix}-notification .@{prefix}-progress .@{prefix}-text {
	color: @notification-text;
}

.@{prefix}-notification .@{prefix}-progress .@{prefix}-bar-container {
	border-color: @notification-border;
}

.@{prefix}-notification .@{prefix}-progress .@{prefix}-bar-container .@{prefix}-bar {
	background-color: @notification-text;
}

.@{prefix}-notification-success *, .@{prefix}-notification-success .@{prefix}-progress .@{prefix}-text {
	color: @notification-success-text;
}

.@{prefix}-notification-success .@{prefix}-progress .@{prefix}-bar-container {
	border-color: @notification-success-border;
}

.@{prefix}-notification-success .@{prefix}-progress .@{prefix}-bar-container .@{prefix}-bar {
	background-color: @notification-success-text;
}

.@{prefix}-notification-info *, .@{prefix}-notification-info .@{prefix}-progress .@{prefix}-text  {
	color: @notification-info-text;
}

.@{prefix}-notification-info .@{prefix}-progress .@{prefix}-bar-container {
	border-color: @notification-info-border;
}

.@{prefix}-notification-info .@{prefix}-progress .@{prefix}-bar-container .@{prefix}-bar {
	background-color: @notification-info-text;
}

.@{prefix}-notification-warning *, .@{prefix}-notification-warning .@{prefix}-progress .@{prefix}-text  {
	color: @notification-warning-text;
}

.@{prefix}-notification-warning .@{prefix}-progress .@{prefix}-bar-container {
	border-color: @notification-warning-border;
}

.@{prefix}-notification-warning .@{prefix}-progress .@{prefix}-bar-container .@{prefix}-bar {
	background-color: @notification-warning-text;
}

.@{prefix}-notification-error *, .@{prefix}-notification-error .@{prefix}-progress .@{prefix}-text  {
	color: @notification-error-text;
}

.@{prefix}-notification-error .@{prefix}-progress .@{prefix}-bar-container {
	border-color: @notification-error-border;
}

.@{prefix}-notification-error .@{prefix}-progress .@{prefix}-bar-container .@{prefix}-bar {
	background-color: @notification-error-text;
}

.@{prefix}-notification .@{prefix}-close {
	position: absolute;
	top: 6px;
	right: 8px;
	font-size: 20px;
	font-weight: bold;
	line-height: 20px;
	color: @window-head-close;
	cursor: pointer;

	// IE7
	height: 20px;
	overflow: hidden;
}