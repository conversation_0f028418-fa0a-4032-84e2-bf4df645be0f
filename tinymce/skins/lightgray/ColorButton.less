// ColorButton

.@{prefix}-colorbutton .@{prefix}-ico {
	position: relative;
}

.@{prefix}-colorbutton-grid {
	margin: 4px;	
}

.@{prefix}-colorbutton button {
    padding-right: 6px;
    padding-left: 6px;
}

.@{prefix}-colorbutton .@{prefix}-preview {
	padding-right: 3px;
	display: block;
	position: absolute;
	left: 50%;
	top: 50%;
	margin-left: -17px;
	margin-top: 7px;
	background: gray;
	width: 13px;
	height: 2px;
	overflow: hidden;
}

.@{prefix}-colorbutton.@{prefix}-btn-small .@{prefix}-preview {
	margin-left: -16px;
	padding-right: 0;
	width: 16px;
}

.@{prefix}-colorbutton .@{prefix}-open {
	padding-left: 4px;
	padding-right: 4px;
	border-left: 1px solid transparent;
}

.@{prefix}-colorbutton:hover .@{prefix}-open {
	border-color: darken(@btn-bg, 20%);
}

.@{prefix}-colorbutton.@{prefix}-btn-small .@{prefix}-open {
	padding: 0 3px 0 3px;
}

// RTL

.@{prefix}-rtl .@{prefix}-colorbutton {
	direction: rtl;
}

.@{prefix}-rtl .@{prefix}-colorbutton .@{prefix}-preview {
	margin-left: 0;
	padding-right: 0;
	padding-left: 3px;
}

.@{prefix}-rtl .@{prefix}-colorbutton.@{prefix}-btn-small .@{prefix}-preview {
	margin-left: 0;
	padding-right: 0;
	padding-left: 2px;
}

.@{prefix}-rtl .@{prefix}-colorbutton .@{prefix}-open {
	padding-left: 4px;
	padding-right: 4px;
	border-left: 0;
}
