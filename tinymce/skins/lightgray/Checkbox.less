// Checkbox

.@{prefix}-checkbox {
	cursor: pointer;
}

i.@{prefix}-i-checkbox {
	margin: 0 3px 0 0;
	border: 1px solid @checkbox-border;
	.border-radius(3px);
	.box-shadow(@checkbox-box-shadow);
	.vertical-gradient(@checkbox-bg, @checkbox-bg-hlight);
	text-indent: -10em;
	*font-size: 0;
	*line-height: 0;
	*text-indent: 0;
	overflow: hidden;
}

.@{prefix}-checked i.@{prefix}-i-checkbox {
	color: @btn-text;
	font-size: 16px;
	line-height: 16px;
	text-indent: 0;
}

.@{prefix}-checkbox:focus i.@{prefix}-i-checkbox, .@{prefix}-checkbox.@{prefix}-focus i.@{prefix}-i-checkbox {
	border: 1px solid @checkbox-border-focus;
	.box-shadow(inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px fadeout(@checkbox-border-focus, 15%));
}

.@{prefix}-checkbox.@{prefix}-disabled .@{prefix}-label, .@{prefix}-checkbox.@{prefix}-disabled i.@{prefix}-i-checkbox {
	color: mix(@text, @panel-bg, 40%);
}

.@{prefix}-checkbox .@{prefix}-label {
	vertical-align: middle;
}

// RTL

.@{prefix}-rtl .@{prefix}-checkbox {
	direction: rtl;
	text-align: right;
}

.@{prefix}-rtl i.@{prefix}-i-checkbox {
	margin: 0 0 0 3px;
}
