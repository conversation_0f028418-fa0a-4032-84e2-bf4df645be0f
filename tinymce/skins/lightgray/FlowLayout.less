// FlowLayout

.@{prefix}-flow-layout-item {
	.inline-block();
}

.@{prefix}-flow-layout-item {
	margin: @flow-layout-spacing 0 @flow-layout-spacing @flow-layout-spacing;
}

.@{prefix}-flow-layout-item.@{prefix}-last {
	margin-right: @flow-layout-spacing;
}

.@{prefix}-flow-layout {
	white-space: normal;
}

.@{prefix}-tinymce-inline .@{prefix}-flow-layout {
	white-space: nowrap;
}

// RTL

.@{prefix}-rtl .@{prefix}-flow-layout {
	text-align: right;
	direction: rtl;
}

.@{prefix}-rtl .@{prefix}-flow-layout-item {
	margin: @flow-layout-spacing @flow-layout-spacing @flow-layout-spacing 0;
}

.@{prefix}-rtl .@{prefix}-flow-layout-item.@{prefix}-last {
	margin-left: @flow-layout-spacing;
}
